package pub

import (
	"encoding/json"
	"mirulib"
	"regexp"

	"github.com/gin-gonic/gin"
)

var (
	AuthAll          = []int{RoleTypeSuper, RoleTypeManager, RoleTypeGuest}
	AuthSuperManager = []int{RoleTypeSuper, RoleTypeManager}
	AuthSuper        = []int{RoleTypeSuper}
)

var AuthorityMap map[string]Allow = map[string]Allow{
	"/v1/miru/camera/group/edit":       Allow{AccountType: AuthSuperManager},
	"/v1/miru/camera/group/list":       Allow{AccountType: AuthAll},
	"/v1/miru/camera/group/info":       Allow{AccountType: AuthSuperManager},
	"/v1/miru/camera/group/delete":     Allow{AccountType: AuthSuperManager},
	"/v1/miru/camera/edit":             Allow{AccountType: AuthSuperManager},
	"/v1/miru/camera/list":             Allow{AccountType: AuthAll},
	"/v1/miru/camera/delete":           Allow{AccountType: AuthSuperManager},
	"/v1/miru/account/list":            Allow{AccountType: AuthSuper},
	"/v1/miru/account/add":             Allow{AccountType: AuthSuper},
	"/v1/miru/account/edit":            Allow{AccountType: AuthSuper},
	"/v1/miru/account/delete":          Allow{AccountType: AuthSuper},
	"/v1/miru/account/password/reset":  Allow{AccountType: AuthSuper},
	"/v1/miru/account/password/change": Allow{AccountType: AuthSuperManager},
	"/v1/miru/account/rename":          Allow{AccountType: AuthSuperManager},
	"/v1/miru/log/list":                Allow{AccountType: AuthSuperManager},
	"/v1/miru/video/list":              Allow{AccountType: AuthSuper},
	"/v1/miru/video/get":               Allow{AccountType: AuthSuper},
	"/v1/miru/video/delete":            Allow{AccountType: AuthSuper},
}

type Allow struct {
	AccountType []int
	Pass        bool
}

func CheckAccountType(uri string, accountType int16) bool {
	allow, ok := AuthorityMap[uri]
	if !ok {
		return false
	}
	if allow.Pass { // 免权限校验
		return true
	}
	for _, t := range allow.AccountType {
		if t == int(accountType) {
			return true
		}
	}
	return false
}

func ValidateMiruAuthToken() gin.HandlerFunc {
	return func(c *gin.Context) {

		uuid := GetGuid()
		body := GetHttpBody(c)
		if len(body) > LOGLENGTHLIMIT { // 请求体过大时。只打印长度
			logger.Info("recv request body: %d bytes, uri: %s, uuid: %s", len(body), c.Request.RequestURI, uuid) //打印请求参数，方便查数据
		} else {
			logger.Info("recv request body: %s, uri: %s, uuid: %s", body, c.Request.RequestURI, uuid) //打印请求参数，方便查数据
		}

		var req H5ReqHeader
		//err := c.BindJSON(&req)
		err := json.Unmarshal([]byte(body), &req)
		if err != nil { //参数错误
			c.Abort()
			SendNormal(c, ErrInvalidBody, uuid)
			return
		}

		if req.AuthToken == "" {
			c.Abort()
			SendNormal(c, StatusUnauthorized, uuid)
			return
		}

		kvsSObj := mirulib.GetKVSSession(req.AuthToken)
		kvsSObj.SetDebugStr(uuid)
		if err := kvsSObj.Get(); err != nil {
			c.Abort()
			SendNormal(c, StatusUnauthorized, uuid)
			return
		}
		if kvsSObj.Net == 0 {
			c.Abort()
			SendNormal(c, StatusUnauthorized, uuid)
			return
		}
		account := kvsSObj.Account
		if account == "" { //账号错误或缓存被清空
			c.Abort()
			SendNormal(c, StatusUnauthorized, uuid)
			return
		}

		var dbPUObj mirulib.TBPhoneUser
		err = mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).First(&dbPUObj)
		if err != nil {
			c.Abort()
			logger.Info("err: %s,uuid: %s", err, uuid)
			SendNormal(c, ErrInternalServer, uuid)
			return
		}

		if !CheckAccountType(c.Request.RequestURI, dbPUObj.AccountType) {
			c.Abort()
			logger.Info("account: %s type: %d, uuid: %s", account, dbPUObj.AccountType, uuid)
			SendNormal(c, ErrPermissionDenied, uuid)
			return
		}

		if htmlEscape(body) { // html关键字过滤
			c.Abort()
			SendNormal(c, ErrInvalidParam, uuid)
			return
		}

		c.Set(LOGID, uuid)
		c.Set(H5ACCOUNT, account)

		//c.Request.Body = ioutil.NopCloser(bytes.NewReader([]byte(body))) //请求的 body只能读一次，BindJSON里取了，故需要在这里再重新写进去
		c.Next()
	}
}

func htmlEscape(s string) bool {

	var xs map[string]interface{}
	json.Unmarshal([]byte(s), &xs)
	for _, v := range xs {
		if ss, ok := v.(string); ok {
			r := checkHTMLString(ss)
			if r {
				return r
			}
		}
	}
	return false
}

func checkHTMLString(account string) bool {
	var isMatch bool

	r := `\<|\>`

	isMatch, _ = regexp.MatchString(r, account)

	return isMatch
}
func passCheckHTML(uri string) bool {
	if uri == "/v1/help/use_question/add" {
		return true
	}
	if uri == "/v1/help/use_question/edit" {
		return true
	}
	return false
}
