package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mirulib"
	"mirulib/redislib"
	"mirulib/worker"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
)

const CheckQQAccessTokenUrl = "https://graph.qq.com/oauth2.0/me?access_token=%s"
const GetQQUserInfoUrl = "https://graph.qq.com/user/get_user_info?access_token=%s&oauth_consumer_key=%s&openid=%s"

type CheckQQAccessTokenOutput struct {
	AppId  string `json:"client_id"`
	OpenId string `json:"openid"`
}

type QQUserInfoOutput struct {
	Ret      int    `json:"ret"`
	Msg      string `json:"msg"`
	NickName string `json:"nickname"`
}

func getPrevSessid(account string, route string, seq int64) string {
	val := redislib.GetRouteSeq(route, seq)
	if val != nil {
		if val.Account == account {
			return val.Sessid
		} else {
			mirulib.GetKVSSession(val.Sessid).Delete()
		}
	}
	return ""
}

func newSessid(account, vid string, ctx *worker.JsonWorkerContext) string {
	sessid := getPrevSessid(account, ctx.Route, int64(ctx.Seq))
	if sessid == "" {
		for {
			sessid = mirulib.GetRegionByCtype(worker.RegionType) + vid + mirulib.GetRandomString(mirulib.RandomSession)
			kvsObj := mirulib.GetKVSSession(sessid)
			if !kvsObj.Exist() {
				break
			} else {
				logger.Info("-----newSessid for vid[%s] account[%s] has same sessid[%s], continue!", vid, account, sessid)
			}
		}
	}
	redislib.SetRouteSeq(sessid, account, ctx.Route, int64(ctx.Seq))
	return sessid
}

func addLoginSession(ctx *worker.JsonWorkerContext, data worker.JCliLogin, kvsSObj *mirulib.KVSSession, sessid string, saveLogin bool, iotVideo int16) *mirulib.KVSSession {
	if kvsSObj == nil {
		kvsSObj = mirulib.GetKVSSession(sessid)
		kvsSObj.Get()
	}
	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
	kvsSObj.Account = data.Body.Account
	kvsSObj.LastLoginTime = time.Now().Unix()
	kvsSObj.Os = data.Body.Os
	kvsSObj.Net = data.Body.Net
	kvsSObj.Name = data.Body.Name
	if data.Body.DeviceToken != "" {
		kvsSObj.ServiceType = data.Body.ServiceType
		kvsSObj.DeviceToken = data.Body.DeviceToken
	}
	if kvsSObj.DeviceToken == "xxx" {
		kvsSObj.ServiceType = mirulib.PushTypeNone
		kvsSObj.DeviceToken = ""
	}
	kvsSObj.BundleId = data.Body.BundleID
	kvsSObj.LanguageType = data.Body.LanguageType
	kvsSObj.Oem = data.Body.Vid
	kvsSObj.Peer = ctx.RmtAddr
	kvsSObj.Version = data.Body.Version
	kvsSObj.SysVersion = data.Body.SysVersion
	kvsSObj.Model = data.Body.Model
	kvsSObj.UDID = data.Body.UDID
	kvsSObj.IotVideo = iotVideo
	kvsSObj.Save(ctx.Seq, ctx.Route, true, true)

	if saveLogin {
		bSave := worker.SaveLoginUDIDMsg(kvsSObj.Account, kvsSObj.Model, kvsSObj.Sessid, kvsSObj.UDID, kvsSObj.DeviceToken, kvsSObj.IotVideo)
		if bSave && data.Body.ScreenSize != "" {
			kvsCnt := mirulib.GetKVSDPCounter(mirulib.AppScreenSize, "")
			kvsCnt.IncrSubKey(data.Body.ScreenSize, 1)
			kvsCnt.Expire(mirulib.YearExTime)
		}
	}

	// if !worker.OpenRegion { //xwx 区域标识开关
	// 	return kvsSObj
	// }

	// if kvsPuObj == nil {
	// 	kvsPuObj = mirulib.GetKVSPU(data.Body.Account)
	// 	kvsPuObj.Get()
	// }
	// kvsPuObj.Region = worker.GetCombineRgn(kvsPuObj.Region)
	// kvsPuObj.Save(false)

	//mirulib.ZSetMag.AddSet(mirulib.RegionClearZSetKey, time.Now().Unix(), data.Body.Account, ctx.ClientMsg.DStr)
	return kvsSObj
}

func addLoginSessionMiru(ctx *worker.JsonWorkerContext, data worker.JCliLogin, kvsSObj *mirulib.KVSSession, sessid string, saveLogin bool, iotVideo, accountType int16) *mirulib.KVSSession {
	if kvsSObj == nil {
		kvsSObj = mirulib.GetKVSSession(sessid)
		kvsSObj.Get()
	}
	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
	kvsSObj.Account = data.Body.Account
	kvsSObj.LastLoginTime = time.Now().Unix()
	kvsSObj.Os = data.Body.Os
	kvsSObj.Net = data.Body.Net
	kvsSObj.Name = data.Body.Name
	if data.Body.DeviceToken != "" {
		kvsSObj.ServiceType = data.Body.ServiceType
		kvsSObj.DeviceToken = data.Body.DeviceToken
	}
	if kvsSObj.DeviceToken == "xxx" {
		kvsSObj.ServiceType = mirulib.PushTypeNone
		kvsSObj.DeviceToken = ""
	}
	kvsSObj.BundleId = data.Body.BundleID
	kvsSObj.LanguageType = data.Body.LanguageType
	kvsSObj.Oem = data.Body.Vid
	kvsSObj.Peer = ctx.RmtAddr
	kvsSObj.Version = data.Body.Version
	kvsSObj.SysVersion = data.Body.SysVersion
	kvsSObj.Model = data.Body.Model
	kvsSObj.UDID = data.Body.UDID
	kvsSObj.IotVideo = iotVideo
	kvsSObj.AccountType = accountType
	kvsSObj.Save(ctx.Seq, ctx.Route, true, true)
	kvsSObj.Expire(120)
	mirulib.SetAccountSession(data.Body.Account, sessid, 120, "")

	if saveLogin {
		bSave := worker.SaveLoginUDIDMsg(kvsSObj.Account, kvsSObj.Model, kvsSObj.Sessid, kvsSObj.UDID, kvsSObj.DeviceToken, kvsSObj.IotVideo)
		if bSave && data.Body.ScreenSize != "" {
			kvsCnt := mirulib.GetKVSDPCounter(mirulib.AppScreenSize, "")
			kvsCnt.IncrSubKey(data.Body.ScreenSize, 1)
			kvsCnt.Expire(mirulib.YearExTime)
		}
	}

	// if !worker.OpenRegion { //xwx 区域标识开关
	// 	return kvsSObj
	// }

	// if kvsPuObj == nil {
	// 	kvsPuObj = mirulib.GetKVSPU(data.Body.Account)
	// 	kvsPuObj.Get()
	// }
	// kvsPuObj.Region = worker.GetCombineRgn(kvsPuObj.Region)
	// kvsPuObj.Save(false)

	//mirulib.ZSetMag.AddSet(mirulib.RegionClearZSetKey, time.Now().Unix(), data.Body.Account, ctx.ClientMsg.DStr)
	return kvsSObj
}

// func addOpenLoginSession(ctx *worker.JsonWorkerContext, data mirulib.MsgClientOpenLoginReq, sessid string) *mirulib.KVSSession {
// 	kvsSObj := mirulib.GetKVSSession(sessid)
// 	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
// 	kvsSObj.Account = data.OpenId
// 	kvsSObj.LastLoginTime = time.Now().Unix()
// 	kvsSObj.Net = data.Net
// 	kvsSObj.Name = data.Name
// 	kvsSObj.Os = data.Os
// 	kvsSObj.Version = data.Version
// 	kvsSObj.SysVersion = data.SysVersion
// 	if data.Os != mirulib.OSAndroidPhone {
// 		kvsSObj.DeviceToken = data.DeviceToken
// 		kvsSObj.BundleId = data.BundleId
// 	}
// 	kvsSObj.Temp = 1
// 	kvsSObj.LanguageType = data.LanguageType
// 	kvsSObj.Model = data.Model
// 	kvsSObj.Oem = data.Oem
// 	kvsSObj.Peer = ctx.RmtAddr
// 	kvsSObj.Save(ctx.Seq, ctx.Route, true, true)

// 	if !worker.OpenRegion { //xwx 区域标识开关
// 		return kvsSObj
// 	}

// 	kvsPuObj := mirulib.GetKVSPU(data.OpenId)
// 	kvsPuObj.Get()
// 	kvsPuObj.Region = worker.GetCombineRgn(kvsPuObj.Region)
// 	kvsPuObj.Save(false)

// 	mirulib.ZSetMag.AddSet(mirulib.RegionClearZSetKey, time.Now().Unix(), data.OpenId, ctx.ClientMsg.DStr)
// 	return kvsSObj
// }

// func addOpenLoginSessionNew(ctx *worker.JsonWorkerContext, data mirulib.MsgClientOpenLoginReqNew, kvsSObj, oldKvsSObj *mirulib.KVSSession, sessid string) {
// 	if kvsSObj == nil {
// 		kvsTmp := mirulib.GetKVSSession(sessid)
// 		kvsSObj = kvsTmp
// 	}
// 	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
// 	kvsSObj.Account = data.OpenId
// 	kvsSObj.LastLoginTime = time.Now().Unix()
// 	kvsSObj.Os = data.Os
// 	kvsSObj.Net = data.Net
// 	kvsSObj.Name = data.Name
// 	if data.Os != mirulib.OSAndroidPhone {
// 		kvsSObj.DeviceToken = data.DeviceToken
// 		kvsSObj.BundleId = data.BundleId
// 	}
// 	kvsSObj.LanguageType = data.LanguageType
// 	kvsSObj.Oem = data.Vid
// 	kvsSObj.Temp = 1
// 	kvsSObj.Peer = ctx.RmtAddr
// 	if oldKvsSObj != nil {
// 		kvsSObj.Version = oldKvsSObj.Version
// 		kvsSObj.SysVersion = oldKvsSObj.SysVersion
// 		kvsSObj.Model = oldKvsSObj.Model
// 	}
// 	kvsSObj.Save(ctx.Seq, ctx.Route, true, true)

// 	if !worker.OpenRegion { //xwx 区域标识开关
// 		return
// 	}

// 	kvsPuObj := mirulib.GetKVSPU(data.OpenId)
// 	kvsPuObj.Region = worker.GetCombineRgn(kvsPuObj.Region)
// 	kvsPuObj.Save(false)
// 	mirulib.ZSetMag.AddSet(mirulib.RegionClearZSetKey, time.Now().Unix(), data.OpenId, ctx.ClientMsg.DStr)
// }

func checkSMS(ctx *worker.JsonWorkerContext, account, code string) (string, error) {
	kvsSmsObj := mirulib.GetKVSSms(account)
	kvsSmsObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsSmsObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorCodeTimeout)
		return "", fmt.Errorf("Sms code timeout")
	}
	if kvsSmsObj.Code != code {
		worker.MQSendErrorReply(ctx, worker.ErrorCodeNotMatch)
		return "", fmt.Errorf("Sms code false")
	}

	kvsSmsObj.Step = 1
	kvsSmsObj.Save()
	return kvsSmsObj.Token, nil
}

func checkSMSToken(ctx *worker.JsonWorkerContext, account, smsToken string) error {
	kvsSmsObj := mirulib.GetKVSSms(account)
	kvsSmsObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsSmsObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorCodeTimeout)
		return fmt.Errorf("Sms code timeout")
	}
	if kvsSmsObj.Token != smsToken {
		worker.MQSendErrorReply(ctx, worker.ErrorCodeNotMatch)
		return fmt.Errorf("Sms code false")
	}

	if kvsSmsObj.Step != 1 {
		worker.MQSendErrorReply(ctx, worker.ErrorCodeNotMatch)
		return fmt.Errorf("Sms Step false")
	}
	kvsSmsObj.Delete()
	return nil
}

func sendSMS(phone, oem string, smsType, notifyType int, val ...interface{}) bool {
	err, ret, content := mirulib.SMS.SendCode(phone, oem, smsType, notifyType, val...)
	retTip := "失败"
	if err == nil {
		retTip = "成功"
	}
	logMsg := fmt.Sprintf("发送短信: %s - %s。内容: %s 短信状态: %v", phone, retTip, content, ret)
	logger.InfoDx("admin", logMsg)
	return err == nil
}

// 电话验证码
func sendVMS(phone, code string) bool {
	vid, err := mirulib.HYCall(phone, mirulib.VmsTplTypeRegister, code)
	logMsg := fmt.Sprintf("send voice to: %s - code: %s - voiceid: %s - error: %s", phone, code, vid, err)
	logger.InfoDx("admin", logMsg)
	return err == nil
}

// 电话提醒
func callUser(account string) bool {
	var dbVSIObj mirulib.TBVoiceServiceInfo
	qs := mirulib.DB.Query(mirulib.GVoiceServiceInfo).Filter("account", account)
	if !qs.Exist() {
		return false
	}
	qs.First(&dbVSIObj)
	if dbVSIObj.Enable == 0 || dbVSIObj.Remaining == 0 || dbVSIObj.ExpireTime < time.Now().Unix() {
		//logger.InfoDx("callUser", fmt.Sprintf("account: %s disable phone call serice", account))
		return false
	}
	if dbVSIObj.PhoneNumber == "" && dbVSIObj.PhoneNumber2 == "" {
		logger.InfoDx("callUser", fmt.Sprintf("account: %s not find phone number", account))
		return false
	}
	var used int
	if dbVSIObj.PhoneNumber != "" {
		vid, err := mirulib.HYCall(dbVSIObj.PhoneNumber, mirulib.VmsTplTypeDeviceCall, "")
		logMsg := fmt.Sprintf("call phone: %s - voiceid: %s - error: %s", dbVSIObj.PhoneNumber, vid, err)
		logger.InfoDx("callUser", logMsg)
		if vid != "" { // 缓存发送消息，以便与互亿回调核对
			var dbHCRObj mirulib.TBHYCallRecord
			dbHCRObj.Account = account
			dbHCRObj.CreateTime = time.Now().Unix()
			dbHCRObj.Phone = dbVSIObj.PhoneNumber
			dbHCRObj.VoiceID = vid
			mirulib.DB.Insert(&dbHCRObj)
		}
		used -= 1
	}
	if dbVSIObj.PhoneNumber2 != "" {
		vid, err := mirulib.HYCall(dbVSIObj.PhoneNumber2, mirulib.VmsTplTypeDeviceCall, "")
		logMsg := fmt.Sprintf("call phone: %s - voiceid: %s - error: %s", dbVSIObj.PhoneNumber2, vid, err)
		logger.InfoDx("callUser", logMsg)
		if vid != "" { // 缓存发送消息，以便与互亿回调核对
			var dbHCRObj mirulib.TBHYCallRecord
			dbHCRObj.Account = account
			dbHCRObj.CreateTime = time.Now().Unix()
			dbHCRObj.Phone = dbVSIObj.PhoneNumber
			dbHCRObj.VoiceID = vid
			mirulib.DB.Insert(&dbHCRObj)
		}
		used -= 1
	}
	// 先扣除一次计费，在互亿回调中再处理用户是否实际消费了
	mirulib.DB.Query(mirulib.GVoiceServiceInfo).Filter("account", account).Update("remaining", mirulib.ColValueAdd(used))
	return true
}

func checkReqData(ctx *worker.JsonWorkerContext, data interface{}, getSession bool) (*mirulib.KVSSession, error) {
	if data != nil {
		if err := worker.JsonUnmarshal(&data, ctx); err != nil {
			return nil, err
		}

		// if err := mirulib.CopyInterface(data, ctx.ClientMsg.ClientMsg); err != nil {
		// 	logger.ErrorfDx(ctx.ClientMsg.DStr, "invalid ClientMsg:%v, err:%s", ctx.ClientMsg, err)
		// 	return nil, err
		// }
	}

	if getSession {
		kvsSObj := mirulib.GetKVSSession(ctx.Headers.Caller)
		kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
		if err := kvsSObj.Get(); err != nil {
			if !ctx.IsCall {
				worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
			}
			return nil, err
		}
		return kvsSObj, nil
	} else {
		return nil, nil
	}
}

func checkPUC(ctx *worker.JsonWorkerContext, cid, account string, toRgn bool) (*mirulib.KVSPhoneUserCamera, error) {
	kvsPucObj := mirulib.GetKVSPUC(cid, account)
	err := kvsPucObj.Get()
	if err != nil {
		if toRgn {
			worker.MQSendErrorReply(ctx, worker.ErrorCIDNotBind)
		} else {
			worker.MQSendRegionErrorReply(ctx, worker.ErrorCIDNotBind, worker.WorkRoute)
		}
		return nil, err
	}
	return kvsPucObj, err
}

//3.0  cidlist
func cidlistUngroup(ctx *worker.JsonWorkerContext, account string, languageType int, changeTime int64, page, num int, search string, iotVideo int16) *worker.JCliDevListUngroupRsp {
	rsp := worker.JCliDevListUngroupRsp{}
	rsp.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)

	//不能直接从缓存过滤，已数据库为准，并且有缓存的获取缓存，防止缓存默认值覆盖数据库值
	/*kvsPucObj := mirulib.GetKVSPUC("", "")
	var pucObjs []mirulib.TBPhoneUserCamera
	mirulib.DB.Query(mirulib.GPhoneUserCamera).Filter("account", account).All(pucObjs, "cid", "account")
	for _, obj := range pucObjs {
		kvsPucObj.SetPk(obj.Cid, obj.Account)
		if kvsPucObj.Get() == nil {
			kvsPucObj.Save(false)
		}
	}*/

	cidDataList := []worker.DevDataGroupInfo{}
	datas, _, _ := worker.PucSelectV3(account, page, num, search, ctx.ClientMsg.DStr, iotVideo)
	for _, obj := range datas {
		row := worker.CidlistFormat(obj, account)
		if row != nil {
			cidDataList = append(cidDataList, *row)
		}
	}

	rsp.Body.Time = changeTime
	rsp.Body.List = cidDataList
	return &rsp
}

func noChangeCidlistUngroup(ctx *worker.JsonWorkerContext, changeTime int64) *worker.JCliDevListUngroupRsp {
	rsp := worker.JCliDevListUngroupRsp{}
	rsp.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)

	rsp.Body.Time = changeTime
	rsp.Body.List = []worker.DevDataGroupInfo{}

	return &rsp
}

func noChangeCidlist(ctx *worker.JsonWorkerContext, changeTime int64) *worker.JCliDevListRsp {
	rsp := worker.JCliDevListRsp{}
	rsp.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)

	rsp.Body.Time = changeTime
	rsp.Body.List = []worker.GroupInfo{}

	return &rsp
}

func checkValue(value string, valueList []string) bool {
	if 0 == len(value) {
		return false
	}
	for _, v := range valueList {
		if strings.Index(value, v) == 0 {
			return true
		}
	}
	return false
}

func checkVKEYAndBundleID(vid, vkey, bundleID string) bool {
	return checkVKEYAndBundleIDDx(vid, vkey, bundleID, "")
}

func checkVKEY(vid, vkey, dStr string) bool {
	if vid == "" {
		return false
	}

	kvsObj := mirulib.GetKVSRbtVKey(vid)
	kvsObj.SetDebugStr(dStr)
	err := kvsObj.Get()
	if err != nil {
		logger.ErrorfDx(dStr, "Error! KVS %v nil! ", vid)
		robotVkey, err := mirulib.Robot.GetVkey(vid)
		if err != nil {
			return false
		}
		kvsObj.Url = robotVkey.Url
		kvsObj.VKeyList = robotVkey.VkeyList
		kvsObj.BundleID = robotVkey.BundleID
		kvsObj.PubKey = robotVkey.PubKey
		kvsObj.Save()
	}

	if !checkValue(vkey, kvsObj.VKeyList) {
		logger.ErrorfDx(dStr, "Error! %v vkey %v false!", vid, vkey)
		return false
	}

	return true
}

func checkVKEYAndBundleIDDx(vid, vkey, bundleID, dStr string) bool {
	//兼容2.4.6无vid, vkey版本
	if vid == "" {
		return false
	}

	kvsObj := mirulib.GetKVSRbtVKey(vid)
	kvsObj.SetDebugStr(dStr)
	err := kvsObj.Get()
	if err != nil {
		logger.ErrorfDx(dStr, "Error! KVS %v nil! ", vid)
		robotVkey, err := mirulib.Robot.GetVkey(vid)
		if err != nil {
			return false
		}
		kvsObj.Url = robotVkey.Url
		kvsObj.VKeyList = robotVkey.VkeyList
		kvsObj.BundleID = robotVkey.BundleID
		kvsObj.PubKey = robotVkey.PubKey
		kvsObj.Save()
	}

	if !checkValue(bundleID, kvsObj.BundleID) {
		logger.ErrorfDx(dStr, "Error! %v BundleID %v false!", vid, bundleID)
		return false
	}

	if !checkValue(vkey, kvsObj.VKeyList) {
		logger.ErrorfDx(dStr, "Error! %v vkey %v false!", vid, vkey)
		return false
	}

	return true
}

func checkVKEYAndBundleIDEx(vid, vkey, bundleID, dStr string) (string, bool) {
	//兼容2.4.6无vid, vkey版本
	if vid == "" {
		return "", false
	}

	kvsObj := mirulib.GetKVSRbtVKey(vid)
	kvsObj.SetDebugStr(dStr)
	err := kvsObj.Get()
	if err != nil {
		logger.ErrorfDx(dStr, "Error! KVS %v nil! ", vid)
		robotVkey, err := mirulib.Robot.GetVkey(vid)
		if err != nil {
			return "", false
		}
		kvsObj.Url = robotVkey.Url
		kvsObj.VKeyList = robotVkey.VkeyList
		kvsObj.BundleID = robotVkey.BundleID
		kvsObj.PubKey = robotVkey.PubKey
		kvsObj.Save()
	}
	logger.Info("vid:%s, vkey:%s", vid, vkey)

	if !checkValue(bundleID, kvsObj.BundleID) {
		logger.ErrorfDx(dStr, "Error! %v BundleID %v false!", vid, bundleID)
		return "", false
	}

	if !checkValue(vkey, kvsObj.VKeyList) {
		logger.ErrorfDx(dStr, "Error! %v vkey %v false!", vid, bundleID)
		return "", false
	}

	return kvsObj.Url, true
}

//账号有效性验证
//aType int 账号类型
func checkAccount(account string, aType int) (bool, int) {
	var isMatch bool
	var ret int
	switch aType {
	case mirulib.RegisterTypePhone:
		if isMatch, _ = regexp.MatchString(mirulib.ReStringPhone, account); !isMatch {
			ret = worker.ErrorIsNotPhone
		}
	case mirulib.RegisterTypeEmail, mirulib.RegisterTypeEmailByCode:
		if isMatch, _ = regexp.MatchString(mirulib.ReStringEmail, account); !isMatch {
			ret = worker.ErrorIsNotEmail
		}
	default:
		isMatch = true
		ret = worker.CodeOK
	}

	return isMatch, ret
}

func CheckQQAccessToken(dstr, accessToken string) (CheckQQAccessTokenOutput, error) {
	var jsonObj CheckQQAccessTokenOutput
	checkUrl := fmt.Sprintf(CheckQQAccessTokenUrl, accessToken)
	resp, err := http.Get(checkUrl)
	if err != nil {
		logger.ErrorfDx(dstr, "CheckQQAccessToken failed access token: %s, err: %s", accessToken, err)
		return jsonObj, err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.ErrorfDx(dstr, "CheckQQAccessToken failed access token: %s, err: %s", accessToken, err)
		return jsonObj, err
	}
	bodyStr := string(body)
	start := strings.Index(bodyStr, "{")
	end := strings.LastIndex(bodyStr, "}")
	length := end - start + 1
	jsonStr := mirulib.Substr(bodyStr, start, length)

	err = json.Unmarshal([]byte(jsonStr), &jsonObj)
	if err != nil {
		logger.ErrorfDx(dstr, "CheckQQAccessToken json decode error: %s access token: %s, json string: %s", err, accessToken, jsonStr)
		return jsonObj, err
	}

	return jsonObj, nil
}

func GetQQUserInfo(accessToken, appId, openId string) (QQUserInfoOutput, error) {
	var jsonObj QQUserInfoOutput
	checkUrl := fmt.Sprintf(GetQQUserInfoUrl, accessToken, appId, openId)
	resp, err := http.Get(checkUrl)
	if err != nil {
		return jsonObj, err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return jsonObj, err
	}

	err = json.Unmarshal([]byte(body), &jsonObj)
	if err != nil {
		return jsonObj, err
	}
	return jsonObj, nil
}

func CheckDIYAccessToken(dstr, urlStr, accessToken string) (HuiJuRsp, error) {

	var jsonObj HuiJuRsp
	//resp, err := http.PostForm(urlStr, url.Values{"access_token": {accessToken}})
	params := url.Values{"access_token": {accessToken}, "act": {"auth_account"}}
	encodeStr := ioutil.NopCloser(strings.NewReader(params.Encode()))
	client := &http.Client{}
	req, err := http.NewRequest("POST", urlStr, encodeStr)
	header := make(http.Header)
	header["Accept"] = []string{"application/json"}
	header["Authorization"] = []string{fmt.Sprintf("bearer %s", accessToken)}
	header["Content-Type"] = []string{"application/x-www-form-urlencoded;param=value"}
	req.Header = header
	//req.Header.Set("Content-Type", "application/x-www-form-urlencoded; param=value")
	resp, err := client.Do(req)
	if err != nil {
		logger.ErrorfDx(dstr, "CheckDIYAccessToken failed access token: %s, err: %s", accessToken, err)
		return jsonObj, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.ErrorfDx(dstr, "CheckDIYAccessToken failed access token: %s, err: %s", accessToken, err)
		return jsonObj, err
	}
	jsonStr := string(body)
	err = json.Unmarshal([]byte(jsonStr), &jsonObj)
	if err != nil {
		logger.ErrorfDx(dstr, "CheckDIYAccessToken json decode error: %s access token: %s, json string: %s", err, accessToken, jsonStr)
		return jsonObj, err
	}

	return jsonObj, nil
}

func checkVidPid(ctx *worker.JsonWorkerContext, vid string, pid uint32) bool {
	if !mirulib.DB.Query(mirulib.GCompanyVid).Filter("company_vid", vid).Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDBindVidNotExist)
		return false
	}

	if !mirulib.DB.Query(mirulib.GPid).Filter("pid", pid).Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDBindPidNotExist)
		return false
	}
	return true
}

// 第三方绑定，该SN+vid是否存在，不存在则生成新cid，再执行绑定，
// func bindCid3rd(ctx *worker.JsonWorkerContext, data *mirulib.MsgClientBindCidReq) {
// 	if !checkVidPid(ctx, data.Vid, data.Pid) {
// 		return
// 	}

// 	if data.Sn == "" {
// 		worker.MQSendErrorReply(ctx, worker.ErrorCIDBindErrorCidLen)
// 		return
// 	}

// 	var cid string
// 	var camObj mirulib.TBCamera

// 	// 加锁，防止APP发起重复绑定请求
// 	lockCam := redislib.Lock(redislib.RDLockCam, data.Sn)
// 	defer lockCam.Unlock()

// 	// 查找设备是否存在
// 	err := mirulib.DB.Query(mirulib.GCamera).Filter("sn", data.Sn).First(&camObj)
// 	if err == nil {
// 		cid = camObj.Cid
// 		if len(cid) != mirulib.CidLength3rd {
// 			worker.MQSendErrorReply(ctx, worker.ErrorCIDBindErrorCidLen)
// 			return
// 		}
// 	} else {
// 		cid = mirulib.GetRand3rdCID(data.Vid)
// 		camObj = mirulib.TBCamera{
// 			Cid:          cid,
// 			Sn:           data.Sn,
// 			Os:           uint32(data.Pid),
// 			CompanyVid:   data.Vid,
// 			GenerateTime: time.Now().Unix(),
// 		}
// 		mirulib.DB.Insert(&camObj)
// 	}

// 	// 绑定逻辑
// 	sessid := ctx.Headers.Caller
// 	kvsSObj := mirulib.GetKVSSession(sessid)
// 	if kvsSObj.Get() != nil {
// 		worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
// 		return
// 	}
// 	account := kvsSObj.Account

// 	workerMsg := mirulib.MsgClientBindCidRsp{
// 		MsgWorkerRspHeader: mirulib.MsgWorkerRspHeader{
// 			MsgHeader: mirulib.MsgHeader{
// 				Id:     mirulib.MIDClientBindCidRsp,
// 				Caller: "",
// 				Callee: sessid,
// 			},
// 			Msg: mirulib.MsgOK,
// 		},
// 		Cid: cid,
// 		Vid: data.Vid,
// 		Sn:  data.Sn,
// 	}

// 	var pucObj mirulib.TBPhoneUserCamera
// 	err = mirulib.DB.Query(mirulib.GPhoneUserCamera).Filter("cid", cid, "type", mirulib.PhoneUserCameraTypeAdmin).First(&pucObj)
// 	idBindedAccount := pucObj.Account
// 	if err == nil {
// 		if idBindedAccount != account {
// 			if 1 == data.IsRebind {
// 				mirulib.MQUnbindcid(ctx, idBindedAccount, cid, nil, true)
// 				mirulib.LogPu(kvsSObj, "", ctx.Id, fmt.Sprintf("DStr:[%s] [%s]下的[%s]被[%s]强制绑定", ctx.ClientMsg.DStr, idBindedAccount, cid, account))
// 			} else {
// 				workerMsg.Ret = worker.ErrorCIDBinded
// 				workerMsg.Account = worker.GetBindedSecret(idBindedAccount)
// 				worker.MQResendMsg(sessid, &workerMsg, 0, "resend bind")
// 				return
// 			}
// 		} else {
// 			worker.MQResendMsg(sessid, &workerMsg, 0, "resend bind")
// 			return
// 		}
// 	}

// 	mirulib.PucAdd(account, cid, 0, "", cid, "", mirulib.PT_JFG_NEW, ctx.ClientMsg.DStr)

// 	mirulib.SaveBindMsg(account, cid, "", data.Sn, data.Pid, true)
// 	//添加一条hello消息
// 	content := fmt.Sprintf("DStr:[%s] %s->绑定[%s] ", ctx.ClientMsg.DStr, account, cid)
// 	mirulib.LogPu(kvsSObj, "", ctx.Id, content)

// 	mirulib.MQResendMsg(sessid, &workerMsg, 0, "resend bind")

// 	mirulib.SetCidListTime(account, time.Now().Unix(), ctx.ClientMsg.DStr)
// 	mirulib.MQSyncCidlist(ctx, account, sessid)
// }

func deactivateAccount(user *mirulib.KVSPhoneUser, url string, pri mirulib.PriMinioConf) {
	// ai.DelAIGroupsByAccount(user.Account, url, pri)
	mirulib.DB.Query(mirulib.GPhoneUserScene).Filter("account", user.Account).DeleteEx()
	mirulib.DB.Query(mirulib.GJFGDeviceGroup).Filter("account", user.Account).DeleteEx()
	mirulib.DB.Query(mirulib.GVoiceServiceInfo).Filter("account", user.Account).DeleteEx()
	//mirulib.DB.Query(mirulib.GCloudOrderInfo).Filter("account", user.Account).DeleteEx()
	mirulib.DB.Query(mirulib.GFeedback).Filter("account", user.Account).DeleteEx()
	mirulib.DB.Query(mirulib.GDeviceServiceInfo).Filter("account", user.Account).DeleteEx()
	user.Delete(true)
}

func updateAccountLastLoginTime(account string) {
	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).Update("last_login_time", time.Now().Unix())
}

func isSwitchRegion(url string) bool {
	lowUrl := strings.ToLower(url)
	if strings.Contains(lowUrl, strings.ToLower(conf.Base.WorkerMQ.Region[5:])) ||
		strings.Contains(lowUrl, "yf") || strings.Contains(lowUrl, "test") {
		return false
	}
	return true
}

func httpGetImage(url string) ([]byte, error) {
	res, err := http.Get(url)
	if err != nil || res.StatusCode != 200 {
		logger.Info("err get image:%v for url[%s] ", err, url)
		return nil, err
	}
	defer res.Body.Close()

	return ioutil.ReadAll(res.Body)
}
