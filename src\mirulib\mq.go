package mirulib

import "time"

// SEQ 消息序列号
type SEQ int64

const (
	// MQBuffSize mq缓存消息大小
	MQBuffSize = 64 * 1024
	// MQChanBuffSize mq 发送channel缓存消息个数
	MQChanBuffSize = 100000
)

const (
	// MQIDClient TCP发往WORKER的消息
	MQIDClient = iota
	// MQIDWorker 1 WORKER发送TCP的消息
	MQIDWorker
	// MQIDPeers 2 其他节点转发消息
	MQIDPeers
	// MQIDClientOffline 3 Client掉线
	MQIDClientOffline
	// MQIDPushStat  4 PushServer统计消息
	MQIDPushStat
	// MQIDWorkerStat 5 Worker统计信息
	MQIDWorkerStat
	// MQIDPushCtl 6 PushServer控制消息
	MQIDPushCtl
	// MQIDAgent  7 Agent 控制消息
	MQIDAgent
	// MQIDApns 8 apns消息
	MQIDApns
	// MQIDInitialCall 9 发起呼叫消息
	MQIDInitialCall
	// MQIDMail 10 mail消息
	MQIDMail
	// MQIDGcm 11
	MQIDGcm
	// MQIDGcmFail 12
	MQIDGcmFail
	// MQIDVerify 13
	MQIDVerify
	// MQIDClearSess 14 pushServer检测seq，sess匹配问题，发送给video清除
	MQIDClearSessByPS
	// MQIDRestartClean 15
	MQIDRestartClean
	// 16 回收
	_
	// MQIDSendRestartSMS 17
	MQIDSendRestartSMS
	// MQIDPushSystemMsg 18 push同一系统消息给大量设备
	MQIDPushSystemMsg
	//MQIDWorkerNew 19 回复类worker消息 pushServer需要根据新旧版本判断是否去掉Msg字段
	MQIDWorkerNew
	// MQIDClientCall 20 worker复制请求消息，该消息不回复，用此标记
	MQIDClientCall
	// MQIDPic2Video 21  图像序列合成视频
	MQIDPic2Video
	// MQIDCron  22 定时任务
	MQIDCron
	// MQIDUpgrade 23 推送升级
	MQIDUpgrade
	// MQIDHms 24 华为推送
	MQIDHms
	// MQIDRPCCall 25 Rpc call
	MQIDRpcCall
	// MQIDChangeCA 26
	MQIDChangeCA
	// MQIDGcmUpdateReq 27
	MQIDGcmUpdateReq
	// MQIDGcmUpdate 28
	MQIDGcmUpdate
	//MQIDMiPush 29 小米推送
	MQIDMiPush

	//MQIDWeChatPushBellCall 30 微信推送
	MQIDWeChatPushBellCall

	// MQIDCidHook 31 劫持cid消息
	MQIDCidHook
	//MQIDWeChatPushWarn 32
	MQIDWeChatPushWarn
	//MQIDWebAPNs 33
	MQIDWebAPNs
	//MQIDQPSClient 34
	MQIDQPSClient
	//MQIDAlexa 35
	MQIDAlexa
	//MQIDHms2 36
	MQIDHms2
	//MQIDGetuiAndroid 37
	MQIDGetuiAndroid
	//MQIDFCM 38
	MQIDFCM
	//MQIDOppoPush 39
	MQIDOppoPush
	//MQIDVivoPush 40
	MQIDVivoPush
	//MQIDHonorPush 41
	MQIDHonorPush
	//MQIDApplePush 42
	MQIDApplePush
)

const (
	// MQIDWorkerRegion 100 区域worker消息转发
	MQIDWorkerRegion = 100 + iota
	// MQIDRegionCtrl 101 区域控制消息
	MQIDRegionCtrl
	// MQIDRegionDBSync 102 区域DB同步消息
	MQIDRegionDBSync
	// MQIDRegionPush 103 区域推送消息
	MQIDRegionPush
	// MQIDRegionClientOffline 104 Client掉线
	MQIDRegionClientOffline
)

const (
	APIChanName = "APICHAN"
)

// MqHeader MQ通用消息头
type MqHeader struct {
	// ID 消息ID, 见上面定义
	ID int
}

// QClientMsg 客户端发送的消息, MQIDClient
type QClientMsg struct {
	MqHeader

	// Seq 序列号，worker回送响应时应复制请求的Seq返回
	Seq SEQ
	// Sess 管理的session id
	Sess string
	// Route worker需要判定消息来自何方
	Route string
	// RmtAddr 客户端IP地址
	RmtAddr string
	// DStr debug信息体，非空则打印debug日志
	DStr string
	// ClientMsg 消息体
	ClientMsg interface{}
	//region route，tcp需要将这个区域路由回给video转发
	RegionRoute string
	Account     string
	ApnsNum     int
	IsCall      bool
	RecvTime    int64
}

// QWorkerMsg worker发送的消息, MQIDWorker
type QWorkerMsg struct {
	MqHeader

	// Seq 序列号，worker回送响应时应复制请求的Seq返回
	Seq SEQ
	// Sess 客户端登录时，worker处理完毕后此字段要填充本次客户端的session,如果是狗登录，填充为cid
	Sess string
	// UpdateSess 是否更新Session
	UpdateSess bool
	// WorkerMsg 消息体
	WorkerMsg interface{}
	//Debug string
	DStr            string
	Net             int
	UpdateLoginOK   bool
	UpdateHeartBeat bool
}

// QPushPeerMsg pushServer peer 发来的转发消息 MQIDPeers
type QPushPeerMsg struct {
	MqHeader

	// Seq 消息序列号
	Seq SEQ
	// Sess 消息session
	Sess string
	// PeerMsg 消息体
	PeerMsg interface{}
}

// QClientOfflineMsg Client掉线消息 MQIDClientOffline
// type QClientOfflineMsg struct {
// 	MqHeader

// 	// Seq 消息序列号
// 	Seq SEQ
// 	// Sess 消息session
// 	Sess string
// 	// Route worker需要判定消息来自何方
// 	Route string
// 	// 客户端IP地址
// 	RmtAddr string
// 	// DStr debug信息体，非空则打印debug日志
// 	DStr string
// 	// region获取，是否获取ihome标记
// 	Net      int
// 	Name     string
// 	Version  string
// 	IsMobile int16
// }

// QPushStaticsMsg MiruServer 统计消息 MQIDStat
type QPushStaticsMsg struct {
	MqHeader

	// 统计消息体
	Msg PushStatMsg
}

// QWorkerStaticsMsg MiruServer 统计消息 MQIDStat
type QWorkerStaticsMsg struct {
	MqHeader

	// Msg 统计消息体
	Msg WorkerStatMsg
}

// QPushCtlMsg MiruServer 控制消息 MQIDCtl
type QPushCtlMsg struct {
	MqHeader

	// CtlMsg 控制消息体
	CtlMsg interface{}
}

// QPushSystemMsg MiruServer 推送系统消息给很多设备
// type QPushSystemMsg struct {
// 	MqHeader

// 	Seqs    []int64
// 	Msg     MsgClientPushData
// 	SeqsNew []int64
// 	DPMsg   DPSetDataReq
// }

// QAgentMsg Agent 消息
type QAgentMsg struct {
	MqHeader

	// AgentMsg 消息体
	AgentMsg interface{}
}

// QApnsMsg Apns 推送消息 ApnsMsg
type QApnsMsg struct {
	MqHeader

	// Msg 消息内容
	Msg  string
	DStr string
}

// QSendApnsMsg Send 推送消息 SendApnsMsg
type QSendApnsMsg struct {
	MqHeader

	// SendMsg 发送消息内容
	BundleId    string
	DeviceToken string
	Alert       string
	Badge       string
	Custom      string
	Sound       string
	Os          uint32
	DStr        string
	Title       string
}

// QGetApnsText Get 推送消息 GetApnsText
type QGetApnsText struct {
	MqHeader

	// SendMsg 发送消息内容
	BundleId     int
	DeviceToken  string
	Account      string
	Badge        int
	LanguageType int
	Custom       string
	Sound        string
}

type QGcmMsg struct {
	MqHeader
	DeviceToken string
	Title       string
	Message     string
	Sessid      string // 加这个信息方便删除
	Account     string
	VID         string
	Notify      string
	Region      string
}

type QGcmUpdateMsg struct {
	MqHeader
	ServerKey map[string]string
}

type QGcmUpdateReqMsg struct {
	MqHeader
}

type QGcmRsp struct {
	MqHeader
	Ret         int
	Msg         string //错误消息
	DeviceToken string
	Sessid      string // 加这个信息方便删除
	Account     string
}

type QFcmMsg struct {
	MqHeader
	DeviceToken string // 推送目标
	Title       string // 通知标题
	Body        string // 通知类容
	PackageName string // APP包名
	Custom      string
	MsgID       string // 消息ID
}

const (
	QGCMRspNoErr        = 0
	QGCMRspReplace      = 1  //发送成功，但是需要替换
	QGCMRspInvalidToken = -2 //发送错误，token已经失效
	QGCMRspErr          = -1 //发送错误，未知错误
)

// QMailMsg mail 消息
type QMailMsg struct {
	MqHeader

	// To 目的地址
	To string
	// MailType 邮件类型
	MailType int
	// LanguageType 客户端语言种类
	LanguageType int
	// Oem 厂商标识
	Oem        string
	Account    string
	Type       int
	Payload    interface{}
	DStr       string
	NotifyType int
}

// QUpgradeMsg  推送升级消息
type QUpgradeMsg struct {
	MqHeader

	PackageId   int
	Region      int
	Count       int
	UpgradeType int
	Cid         string
	DbUIid      string
	IsPush      int
	Uuid        string
}

// QPic2VideoMsg
type QPic2VideoMsg struct {
	MqHeader

	Cid string
	// CloudType 云存储的类型
	CloudType int
	// BeginTime 延迟摄影开始时间
	BeginTime int64
	// Vid 厂商标识
	Vid string
}

// QCronMsg
type QCronMsg struct {
	MqHeader

	TaskName string
	Action   int
}

// QInitialCallMsg 呼叫发起消息
// 通知后端逻辑, worker/video 需要给其他终端发送已接听消息
type QInitialCallMsg struct {
	MqHeader

	// Caller 主叫
	Caller string
	// Callee 被叫
	Callee string
	// DStr
	DStr string
}

// MQIDRestart 清理在线set消息
// 此消息发送给video处理，负责清理在线set等数据逻辑
type QRestartCleanMsg struct {
	MqHeader

	Route string
}

// MQIDSendRestartSMS 重启发送告警消息
// 此消息发送给pushtool，负责发送短信报警
type QRestartHostMsg struct {
	MqHeader

	Host string
}

// QHmsMsg hms 推送消息
type QHmsMsg struct {
	MqHeader

	DeviceToken string
	Content     string
	BundleId    string
}

// QHmsMsg hms2 推送消息
type QHmsMsg2 struct {
	MqHeader

	DeviceToken string
	Title       string
	Body        string
	BundleId    string
	IntentType  int // 指定页 1-呼叫界面 2-消息中心
	Cid         string
	ImageUrl    string
	Oem         string
	LogID       string
}

// QOppoPushMsg  OPPO推送消息
type QOppoPushMsg struct {
	MqHeader

	MsgType     int
	DeviceToken string
	Title       string
	Body        string
	BundleId    string
	Cid         string
	Custom      string
	LogID       string
	AppVersion  string //为了兼容OPPO APP新旧版本设备呼叫推送
}

// QApplePushMsg  APNS推送消息
type QApplePushMsg struct {
	MqHeader

	MsgType     int
	DeviceToken string
	Title       string
	Body        string
	BundleId    string
	Cid         string
	Custom      string
	LogID       string
	Badge       int
}

// QHmsMsg hms 推送消息
type QChangeCA struct {
	MqHeader

	Action   int // 1- add ca/2 - del ca/3 - change ca
	FileName string
	Password string
	BundleId string
	Vid      string
}

// QMiPush 推送消息
type QMiPush struct {
	MqHeader

	DeviceToken string
	BundleId    string
	Content     string
	Title       string
	Description string
}

// QWeChatPushBellCall 微信推送消息门铃呼叫
type QWeChatPushBellCall struct {
	MqHeader
	WechatOpenID string
	Cid          string
	CompanyVid   string
	Content      string
	AliasName    string
	Url          string
	TimeStamp    int64
}

// QWeChatPushWarn 微信推送报警消息
type QWeChatPushWarn struct {
	MqHeader
	OpenId     string
	Cid        string
	CompanyVid string
	Url        []string
	Time       int64
	Content    string
	AliasName  string
}

// QSetOSSApi hms 推送消息
type QSetOSSApi struct {
	MqHeader

	Ret      int
	Url      string
	Duration int
	Sn       string
}

// QCidHookMsg 劫持cid消息
type QCidHookMsg struct {
	MqHeader

	Action string
	Cid    string
	Queue  string
}

// QWebAPNsMsg todo
type QWebAPNsMsg struct {
	MqHeader

	AppID       string
	DeviceToken string
	Payload     interface{}
}

type ClearSessInfo struct {
	Force bool
	Seq   SEQ
	Sess  string
}

// QMQIDClearSessMsg todo
type QClearSessMsg struct {
	MqHeader

	Routing   string
	ClearTime int64
	List      []ClearSessInfo
}

func (p *QClearSessMsg) GetSessList() []string {
	ret := []string{}
	for _, obj := range p.List {
		ret = append(ret, obj.Sess)
	}
	return ret
}

func (p *QClearSessMsg) Set(force bool, seq SEQ, sess string) bool {
	//logger.Info("--- set %v %v %v", seq, sess, p.List)
	for _, obj := range p.List {
		if sess == obj.Sess && seq == obj.Seq && force == obj.Force {
			return true
		}
	}
	data := ClearSessInfo{Force: force, Seq: seq, Sess: sess}
	p.List = append(p.List, data)
	return true
}
func (p *QClearSessMsg) ClearList(cap int) { p.List = make([]ClearSessInfo, 0, cap) }
func (p *QClearSessMsg) Num() int          { return len(p.List) }
func (p *QClearSessMsg) Time() int64       { return p.ClearTime }
func (p *QClearSessMsg) SetTime(tm int64) {
	if tm <= 0 {
		tm = time.Now().Unix()
	}
	p.ClearTime = tm
}
func (p *QClearSessMsg) Route() string         { return p.Routing }
func (p *QClearSessMsg) SetRoute(route string) { p.Routing = route }

// QFTPClientMsg todo
type QQPSClientMsg struct {
	MqHeader

	Type    QPSFlag
	UUID    string
	Payload []byte
}

type QPSFlag int

const (
	QPSForFtp QPSFlag = 1 + iota
	QPSForCylan
)
