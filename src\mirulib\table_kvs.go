package mirulib

const KeySep string = "<>"
const MaxGet int = 512
const MaxDel int = 2048

const KVSForeverTime int = 10 * 365 * 86400
const SMSCodeExTime int = 60 * 5
const EmailCodeExTime int = 60 * 15
const SetAccountInfoExTime int = 60 * 5
const SMSCodeCountExTime int = 60 * 10
const WeChatPushExTime = 60 * 100
const FlowAnalyExTime = 60 * 30
const ClientCallerAccExTime int = 10 * 60
const OnlineForRobotExTime = 3600
const OneHourExTime int = 3600
const OneDayExTime int = 86400
const TwoDayExTime int = 2 * 86400
const ThreeDayExTime int = 3 * 86400
const WeekExTime int = 7 * 86400
const TwoWeekExTime int = 14 * 86400
const HalfMonthExTime int = 16 * 86400
const MonthExTime int = 30 * 86400
const YearExTime int = 366 * 86400
const DPDevSetExTime int = 6 * 366 * 86400
const MinuteExtime int = 60
const ShareQuickExtime int = 30 * 60
const OpenLoginExtime int = 10 * 60

const SetGetDog int = 0x01
const SetGetClient int = 0x02
const SetGetAll int = 0x03

// const OnlineDogKey string = "SetOnlineDogSessid_%s"
// const OnlineClientKey string = "SetOnlineClientSessid_%s"
const OnlineTypeOSPrefix string = "SetOnlineTypeOsPrefix_%s_%d"
const OnlineTypeOSPrefixFilter string = "SetOnlineTypeOsPrefix_%s_*"
const OnlineTypeVIDPrefix string = "SetOnlineTypeVidPrefix_%s_%s"
const CidListTimePrefix string = "CidListTime_%s"
const AvatarVidPrefix string = "AvatarVid_%s"
const CidVersionListKeyFormat string = "CidVersionSet_%d_%s"        //CidVersionSet_os_region
const UpGradeCidsKeyFormat string = "UpGradeCids_%d_%s_%d_%d"       //UpGradeCids_os_region_pkgId_updateType
const DefaultPackageFormat string = "DefaultPackage_%d_%s"          // DefaultPackage_os_region
const DefaultPackageFormatEx string = "DefaultPackage_%d_%s_%s"     // DefaultPackage_os_snPrefix_region
const DefaultPackageFormatNew string = "DefaultPackageNew_%d_%s_%s" // DefaultPackageNew_os_snPrefix_region
const CidPackageFormat string = "CidPackage_%s"                     // CidPackage_cid
const CidPackageFormatNew string = "CidPackageNew_%s"               // CidPackageNew_cid
const GroupPackageFormat string = "GroupPackage_%d"                 // GroupPackage_channelId
const GroupPackageFormatNew string = "GroupPackageNew_%d"           // GroupPackageNew_channelId
const SetAccountInfoFormat string = "EmailOrPhone_%s"               // EmailOrPhone_account
const VersionPartsFormat string = "verparts1<>%s"                   // verparts1<>cid
const ClientCallerAccFormat string = "cliAcc_%s"
const CidTypeForBindFormat string = "CidTypeForBind_%s"     // CidTypeForBind_cid
const CidForRegionMoveFormat string = "CidForRegionMove_%s" // CidForRegionMove_cid
const OnlineForRobotVidFormat string = "OnlineRobotVid_%s"
const OnlineForRobotPidFormat string = "OnlineRobotPid_%d"
const CidLastWarnTime string = "CidLastWarnTime_%s"                     // CidLastWarnTime_cid
const EVCStatus string = "EVC_%s_%d_%d"                                 // EVC_cid_beginTime_endTime
const EGDStatus string = "EGD_%s_%s"                                    // EGD_account_time
const EWMStatus string = "EWM_%s_%d_%d"                                 // EWM_cid_beginTime_endTime
const EFDStatus string = "EFD_%s_%d_%d"                                 // EFD_cid_beginTime_endTime
const CidWarnFilterTime string = "CidWarnFilterTime_%s"                 // CidWarnFilterTime_cid
const CidLivingDetection string = "CidLivingDetection_%s"               // CidLivingDetection_cid
const CidRecognitionResult string = "CRR_%s_%d_%d"                      // EWM_cid_trackID_firstTime
const DeviceGPSInfoFormat string = "GPS_%s"                             // GPS_cid
const CidVipVisitNumberFormat string = "CidVipVisitNumber_%s_%s_%d_%d"  // CidVipVisitNumber_account_cid_time_type
const CidServerVisitCountFormat string = "CidServerVisitCount_%s_%s_%d" // CidServerVisitCount_account_cid_time
const FacesVersionFormat string = "FacesVersion_%s"                     // FacesVersion_account_cid_time
const AlexaCodeFormat string = "AlexaCode_%s"                           // AlexaCode_code
const AlexaAccessTokenFormat string = "AlexaAccessToken_%s"             // AlexaAccessToken_token
const AlexaDeviceRegionFormat string = "DeviceRegion_%s"                // DeviceRegion_deviceID
const RobotAiServiceFormat string = "RobotAiService_%s_%s"              // RobotAiService_service_cid
const DeviceCheckinStatusFormat string = "DeviceCheckinStatus_%s"       // DeviceCheckinStatus_cid
const DeviceRegisterResultFormat string = "DeviceRegisterResult_%s_%s"  // DeviceRegisterResult_cid_personID
const AIFaceLogoFormat string = "AIFace_Logo_%s"                        // AIFace_Logo_account
const WebAuthToken string = "WEB_AuthToken_%s_%s"                       // WEB_AuthToken_account_token
const H5AuthToken string = "H5_AuthToken_%s"                            // H5_AuthToken_token
const IosInAppPurchase string = "Ios_InApp_Purchase_%s"                 // Ios_InApp_Purchase_token
const WebWrongPasswordTimes string = "Web_Wrong_Password_Times_%s"      // Web_Wrong_Password_Times_account
const WebTrialPeriodDays string = "WEB_Trial_Period_Days"               //
const WebTrialPeriodEnable string = "WEB_Trial_Period_Enable"           //
const QrLoginFormat string = "QrLoginFmt_%v"                            // 二维码登录
const CloudStorageDefaultRegion string = "Cloud_Storage_Default_Region" //
const WebLoginTempToken string = "WEB_Login_Temp_Token_%s"              // WEB_Login_Temp_Token__token
const WebOperateCodeFormat string = "WEB_Operate_Code_%s_%d"            // WEB_Operate_Code_account_type
const DeviceButtonDefineFormat string = "DEV_Button_define_v%d_%s"      // 按钮功能定义列表
const CidUpgradeInfoFormat string = "CidUpgradeInfo_%s"                 // CidUpgradeInfo_cid
const Cid4GBurnInfo string = "Cid4GBurnInfo_%s"                         // Cid4GBurnInfo_cid
const QuickShareFormat string = "QuickShare_%s"                         // QuickShare_code
const OpenLoginFormat string = "OpenLogin_%s"                           // OpenLogin_openid
const WebLoginPhoneCodeTimeFormat string = "WebLoginPhoneCodeTime_%s"   // WebLoginPhoneCodeTime_account
const WebStatFlagFormat string = "WebStatFlag_%d"                       // WebStatFlag_time
const MiniSendTimeFormat string = "MiniSendTime_%s"                     // MiniSendTime_sn
const MiniAccountIndex string = "MiniAccountIndex"                      // MiniAccountIndex
const CidChatFreeNumberFormat string = "CidChatFreeNumber_%s"           // CidChatFreeNumber_cid
const FreeChatSetFormat string = "FreeChatSet_%s"                       // FreeChatSet_day
const MiruAccountSessionFormat string = "MiruAccountSession_%s"         // MiruAccountSession_account
// 离线通知的key定义
const OffZSetKey string = "ZSetOffMessage"
const OffZCheckTime int64 = 60
const OffZMaxOutTime int64 = 300

//关闭设备日志的key定义
const CloseZSetKey string = "ZSetCloseLog"
const CloseZCheckTime int64 = 43200  // 12*60*60
const CloseZMaxOutTime int64 = 86400 // 24*60*60

//产测工具剔除的key定义
const BurnDogZSetKey string = "ZSetBurnDog"
const BurnDogZCheckTime int64 = 10
const BurnDogZMaxOutTime int64 = 10

//清理账号本区域的key定义
// const RegionClearZSetKey string = "ZSetRegionClear"
// const RegionClearZCheckTime int64 = 43200 // 12*60*60
// const RegionClearZMaxOutTime int64 = int64(WeekExTime)

//重发消息的key定义
// const ResendMsgZCheckTime int64 = 10
const ResendMsgZSetKey string = "ZSetResendMsg"
const ResendMsgFormat string = "RID<>%s<>%v<>%v" // RID<>sessid<>mid<>seq
const CliOsAttrKVSKey string = "CliOsAttrKVSKey"

// 跨区域随机数绑定
const RandomBindKey string = "RandomBindKey_v1"

// 开放性存储
const RecordSetKeyFmt string = "RecordSet_v1_%v_%v" // RecordSet_v1_type_key
const RecordSetOutTime int = 5 * 366 * 86400

// APP数据埋点 DP250
const AppEventTracking string = "AppTracking_v1"
const AppScreenSize string = "AppScreenSize_vl"

const (
	SysMailType string = "Sys<>Mail<>Type"
)

const (
	BindForRandom = iota + 1
	BindForWait
	BindForCancel
)

// 可以修改参数
var RandomBindExTime int = 303
var DeviceButtonDefineVersion int = 1

//KVSBase
var gkvsAuthObj KVSAuthToken
var gkvsSObj KVSSession
var gkvsPUObj KVSPhoneUser
var gkvsPUCObj KVSPhoneUserCamera
var gkvsPUSObj KVSPhoneUserScene
var gkvsCamObj KVSCamera

//KVSKvBase
// var gkvsHlistObj KVSHistoryList
// var gkvsRbtObj KVSRobotData
// var gkvsRbtMsgCfg KVSRobotMsgConfig
var gkvsRbtVKey KVSRobotVKey
var gkvsSmsObj KVSSms

// var gkvsRbtCIDQuota KVSRobotCIDQuota
var gkvsDataPoint KVSDataPoint
var gkvsDataPointCnt KVSDataPointCnt
var gkvsDataCidRandom KVSDataCidRandom

// var gkvsSessionRgn KVSSessionRgn
// var gkvsVerParts KVSVerParts
var gkvsSmsCountObj KVSSmsCount
var gkvsWeChatPush KVSWeChatPush

const (
	GetInsertSql = iota
	GetSelectSql
	GetDeleteSql
	GetUpdateSql
)

type KVSBase struct {
	_table      interface{}
	_inst       interface{}
	_kvsKey     string
	_prefix     string
	_oldVal     []interface{}
	_cols       []string
	_pkNum      int
	_colNum     int
	_kvsExTime  int
	_kvsVersion int
	_head       string
	_dStr       string
}

type KVSAuthToken struct {
	Sessid       string `kvs:"sessid;;pk"`
	Account      string `kvs:"account;"`
	Time         int64  `kvs:"time;0"`
	LanguageType int    `kvs:"language_type;0"`
	TokenType    int    `kvs:"token_type;0"`

	KVSBase
}

type KVSSession struct {
	Sessid        string `kvs:"sessid;;pk" json:"sessid"`
	Account       string `kvs:"account;" json:"account"`
	Net           int    `kvs:"net;0" json:"net"`  //网络类型
	Name          string `kvs:"name;" json:"name"` //网络名称，例如无线网：Xiaomi_ACF2
	Os            uint32 `kvs:"os;0" json:"os"`
	Version       string `kvs:"version;" json:"version"`         //设备固件版本 2.5.3.7
	SysVersion    string `kvs:"sys_version;" json:"sys_version"` //设备类型名称DOG-1W-V4
	Time          int64  `kvs:"time;0" json:"time"`
	LastLoginTime int64  `kvs:"last_login_time;0" json:"last_login_time"`
	Uptime        int64  `kvs:"uptime;1" json:"uptime"`
	DeviceToken   string `kvs:"device_token;" json:"device_token"`
	Sdcard        int    `kvs:"sdcard;0" json:"sdcard"` //设备是否支持内存卡、插卡
	SdcardErrno   int    `kvs:"sdcard_errno;0" json:"sdcard_errno"`
	Storage       int64  `kvs:"storage;0" json:"storage"`           //内存卡大小
	StorageUsed   int64  `kvs:"storage_used;0" json:"storage_used"` //已使用内存大小
	BundleId      string `kvs:"bundleId;" json:"bundleId"`
	LanguageType  int    `kvs:"language_type;0" json:"language_type"`
	Model         string `kvs:"model;" json:"model"`
	Mac           string `kvs:"mac;" json:"mac"`
	Oem           string `kvs:"oem;" json:"oem"`
	Peer          string `kvs:"peer;" json:"peer"` //113.108.111.209:49153
	IsMobile      int16  `kvs:"isMobile;0" json:"isMobile"`
	Battery       int    `kvs:"battery;100" json:"battery"`
	Route         string `kvs:"route;" json:"route"`
	Seq           int64  `kvs:"seq;0" json:"seq"`
	ServiceType   int    `kvs:"service_type;0" json:"service_type"`
	UDID          string `kvs:"udid;" json:"udid"`
	IotVideo      int16  `kvs:"iot_video;0" json:"iot_video"`       // 使用的P2P服务 0-自研 1-tencent
	AccountType   int16  `kvs:"account_type;0" json:"account_type"` // 账号类型，1-超管 2-管理员

	KVSBase
	headAcc   string
	headToken string
}

type KVSPhoneUser struct {
	Account         string `kvs:"account;;pk"`
	Password        string `kvs:"password;"`
	RegisterTime    int64  `kvs:"register_time;0"`
	Alias           string `kvs:"alias;"`
	SmsPhone        string `kvs:"sms_phone;"`
	Type            int16  `kvs:"type;0"`
	PassIsChanged   int16  `kvs:"pass_is_changed;0"`
	PushEnable      int16  `kvs:"push_enable;1"`
	Vid             int    `kvs:"vid;1"`
	Vibrate         int    `kvs:"vibrate;1"`
	Sound           int    `kvs:"sound;1"`
	Email           string `kvs:"email;"`
	RegisterType    int    `kvs:"register_type;0"`
	CompanyVid      string `kvs:"company_vid;"`
	HeadPhotoRegion int    `kvs:"head_photo_region;0"`
	WeixinOpenID    string `kvs:"weixin_open_id;"` //add weixin open id field
	Region          string `kvs:"region;"`         //客户端的region取并集
	CountryCode     string `kvs:"country_code;"`
	ModifyTimes     int    `kvs:"modify_times;0"`
	Pt              int32  `kvs:"pt;0"`                // 平台标志，0：默认jfg平台，1：smart新零售平台
	LastLoginTime   int64  `kvs:"last_login_time;0"`   // 最后登录时间
	PayAgreement    int    `kvs:"payment_agreement;0"` // 支付协议确认
	AlexaLink       int    `kvs:"alexa_link;0"`        // 是否关联alexa账号
	UnionOpenID     string `kvs:"union_open_id;"`
	MiniOpenID      string `kvs:"mini_open_id;"`
	AppleID         string `kvs:"apple_id;"`
	UniqueAccount   string `kvs:"unique_account;"`
	AccountType     int16  `kvs:"account_type;"` // 账号类型，1-超管 2-管理员

	KVSBase
}

type KVSPhoneUserCamera struct {
	Cid              string `kvs:"cid;;pk"`
	Account          string `kvs:"account;;pk"`
	BindTime         int64  `kvs:"bind_time;0"`
	ShareAccount     string `kvs:"share_account;"`
	Type             int16  `kvs:"type;0"`
	IsRecvWarn       int16  `kvs:"is_recv_warn;0"`
	Alias            string `kvs:"alias;"`
	SceneId          int    `kvs:"scene_id;0"`
	Mark             int32  `kvs:"mark;0"`
	GroupID          string `kvs:"dev_group_id;"`
	Pt               int32  `kvs:"pt;0"`                // 平台标志，0：默认jfg平台，1：smart新零售平台
	SceneStrategyId  int32  `kvs:"scene_strategy_id;0"` // 0:会员识别 1:明厨亮灶 2:门禁考勤 3:抓拍机
	PicRegion        int    `kvs:"pic_region;0"`        // 摄像头绑定时候上传的图片，-1代表未上传
	WebrtcPermitCode string `kvs:"webrtc_permit_code;"` // Webrtc视频查看通行码
	Remark           string `kvs:"remark;"`             // 备注
	IotVideo         int16  `kvs:"iot_video;0"`         // 使用的P2P服务 0-自研 1-tencent
	Star             int16  `kvs:"star;0"`              // 分享用户：星标用户

	KVSBase
	//headCid    string
}

type KVSPhoneUserScene struct {
	SceneId   int    `kvs:"scene_id;;pk"`
	SceneName string `kvs:"scene_name;"`
	Account   string `kvs:"account;"`
	SortNum   int    `kvs:"sort_num;0"`

	KVSBase
}

type KVSCamera struct {
	Cid          string `kvs:"cid;;pk"`
	IsActivated  int16  `kvs:"is_activated;0"`
	ActivateTime int64  `kvs:"activate_time;0"`
	ValidateTime int64  `kvs:"validate_time;0"`
	UsageStatus  int16  `kvs:"usage_status;0"`
	ActiveType   int16  `kvs:"active_type;0"`
	//PushEnable    int16  `kvs:"push_enable;1"`
	// WarnEnable    int16  `kvs:"warn_enable;1"`
	// WarnBeginTime int16  `kvs:"warn_begin_time;0"`
	// WarnEndTime   int16  `kvs:"warn_end_time;5947"`
	// WarnWeek      int16  `kvs:"warn_week;127"`
	Sn string `kvs:"sn;"`
	//MaxConn       int16  `kvs:"max_conn;0"`
	ChannelId int `kvs:"channel_id;0"`
	// SetId         int    `kvs:"set_id;0"`
	// SetBeginTime  int64  `kvs:"set_begin_time;0"`
	Os uint32 `kvs:"os;0"`
	// Color         string `kvs:"color;"`
	GenerateTime int64 `kvs:"generate_time;0"`
	// Led           int16  `kvs:"led;1"`
	// Sound         int    `kvs:"sound;0"`
	//IsPushflow    int    `kvs:"is_pushflow;0"`
	//Vid           int    `kvs:"vid;1"`
	// Direction     int16  `kvs:"direction;0"`
	// Timezone      string `kvs:"timezone;"`
	// SoundLong     int    `kvs:"sound_long;1"`
	// AutoRecord    int16  `kvs:"auto_record;0"`
	// IsNTSC         int16  `kvs:"isNTSC;0"`
	// IsMobile       int16  `kvs:"isMobile;0"`
	// Sensitivity    int16  `kvs:"sensitivity;1"`
	Version    string `kvs:"version;"`
	CompanyVid string `kvs:"company_vid;"`
	Net        int    `kvs:"net;0"`
	Region     string `kvs:"region;"` //设备的region覆盖
	Code       string `kvs:"code;"`
	// ScreenWarn     int    `kvs:"screen_warn;0`      // robot用到弹屏预警开关
	// ScreenWarnLong int    `kvs:"screen_warn_long;5` // robot用到弹屏预警时长
	//MutiWarn string `kvs:"muti_warn;` //多时段报警设置,格式json '[{"warn_alias":"time1", "warn_begin_time":0, "warn_end_time":5947}]'
	Mac               string `kvs:"mac;"`
	UsedTime          int64  `kvs:"used_time;"`            // 使用时间，工厂写入设备的时间
	BatchNumber       string `kvs:"batch_number;"`         // SN生成时的批号
	SnPrefix          string `kvs:"sn_prefix;"`            // SN起始号
	Iccid             string `kvs:"iccid;"`                // 4G卡ICCID
	Card4GStatus      int    `kvs:"card_4g_status;"`       // 4G卡状态 0-正常 1-未授权卡禁止联网 2-未授权卡允许联网
	JobNumber         string `kvs:"job_number;"`           // 4G生产工号
	TuyaUUID          string `kvs:"tuya_uuid;"`            // 涂鸦UUID
	TuyaKey           string `kvs:"tuya_key;"`             // 涂鸦KEY
	DevicePlatform    string `kvs:"device_platform;"`      // 0-赛蓝设备 1-涂鸦设备
	IotVideo          int16  `kvs:"iot_video;0"`           // 使用的P2P服务 0-自研 1-tencent
	WecallCreateTime  int64  `kvs:"wecall_create_time;0"`  // 微信创建时间
	WecallExpireTime  int64  `kvs:"wecall_expire_time;0"`  // 微信过期时间
	WecallActiveCount int16  `kvs:"wecall_active_count;0"` // TWeCall增值服务激活次数，一次一年，最多三年，activenumber需要使用分布式缓存存储，并且不能使用重复数字来激活TWeCall
	MiruCode          string `kvs:"miru_code;"`            // 第三方编码
	GroupID           string `kvs:"group_id;"`             // 设备组ID
	Name              string `kvs:"name;"`                 // 设备昵称
	KVSBase
}

//kvs normal table,simple for key value
type KVSKvBase struct {
	_kvsVersion int
	_head       string
	_kvsExTime  int
	_dStr       string

	_key string
}

type KVSHistoryList struct {
	KVSKvBase
}

type KVSRobotData struct {
	KVSKvBase
}

type KVSRobotMsgConfig struct {
	Vid     string
	headVid string

	KVSKvBase
}

type KVSRobotVKeyValue struct {
	Url      string
	BundleID []string
	VKeyList []string
	PubKey   string
}

type KVSRobotVKey struct {
	//Url      string
	//BundleID []string
	//VKeyList []string
	//PubKey   string
	KVSRobotVKeyValue

	KVSKvBase
}

type KVSSmsValue struct {
	Code   string
	Token  string
	Step   int
	Time   int64
	Type   int
	ExTime int // 超时时间
}

type KVSSms struct {
	KVSKvBase
	KVSSmsValue
}

type KVSRobotCIDQuota struct {
	Quota int

	KVSKvBase
}

type KVSDataPointValue struct {
	//DevId string
	//MsgID int
	Time  int64
	Value []byte
}

type KVSDataPoint struct {
	KVSKvBase
}

type KVSDataPointCnt struct {
	KVSKvBase
}

type KVSCount struct {
	Count int64
	_key  string
}

type CidRandomData struct {
	Random     string `json:"random"`
	Cid        string `json:"cid"`
	BindSessid string `json:"bind_sessid"`
	Mac        string `json:"mac"`
	IsRebind   int    `json:"is_rebind"`
	MutiDevice int    `json:"muti_dev"` // 是否多台绑定
	ReqID      string `json:"reqID"`
	AppOK      bool   `json:"app_ok"`
	DevOK      bool   `json:"dev_ok"`
	Timezone   string `json:"timezone"`
	Alias      string `json:"alias"`
	Offset     int    `json:"offset"`
	IotVideo   int16  `json:"iot_video"`
}

type KVSDataCidRandom struct {
	CidRandomData

	KVSKvBase
}

//保存session的region信息
type KVSSessionRgn struct {
	KVSKvBase
}

type KVSVerPartsValue struct {
	Bootloader string //引导分区
	Kernal     string //内核区
	Rootfs     string //根文件系统区
	App        string //应用区
	Config     string //配置区
	Dtb        string //内核配置区
	Ailib      string //AI配置区
}

type KVSVerParts struct {
	KVSKvBase
	KVSVerPartsValue
}

type KVSSmsCountValue struct {
	Count int
	Time  int64
}

type KVSSmsCount struct {
	KVSKvBase
	KVSSmsCountValue
}

type KVSWeChatPushValue struct {
	CompanyVid      string
	AccessToken     string
	WarnTemplateId  string
	BCallTemplateId string
	BOpenTemplateId string
	Remark          string
	Color           string
	Time            int64
}

type KVSWeChatPush struct {
	KVSWeChatPushValue
	KVSKvBase
}

type KVSFlowAnalyCoordinateValue struct {
	Cid          string
	RelationTime int64
	Directions   []string     //流向分析结果
	FlowResults  []FlowResult //对应一条cass报警记录数据
}

type FlowResult struct {
	TrackId  int //`json:"track_id"`
	FlowInfo FlowInfo
}

type FlowInfo struct {
	Start         PersonCoordinate //`json:"start"`
	End           PersonCoordinate //`json:"end"`
	FlowDerection string           //`json:"flow_derection"`
}

type KVSPersonInfo struct {
	PersonID        string
	PersonName      string
	Age             string
	Sex             string
	Tags            []string
	StrangerGroupID string
	CallbackUrl     string
	CBType          int
	Confidence      float64
}

type KVSDeviceGPSInfo struct {
	Cid       string
	Time      int64
	Longitude string
	Latitude  string
	Speed     string
}

type KVSDeviceAreaDetectImage struct {
	Cid      string
	ImageUrl string //区域侦测的图片地址
	Width    int    //图片分辨率宽度（分辨率为1920*1080，则其值为1920）
	Height   int    //图片分辨率高度（分辨率为1920*1080，则其值为1080）
}

// 二维码登录相关
const (
	QrLoginStatusInit      = iota + 1 // 初始化
	QrLoginStatusScanned              // 已扫描
	QrLoginStatusConfirmed            // 已确认，可以登录
	QrLoginStatusLogined              // 登录成功
	QrLoginTimeOut         = 120      //120
)

type KVSQrLoginData struct {
	Status   int
	InitTime int64

	KvsSObj KVSSession // 存储APP账号信息
}

const (
	ShareQuickDev         = iota + 1 // 设备分享
	ShareQuickAddressBook            // 通讯录分享
)

type KVSShareData struct {
	Sn     string `json:"sn"`
	Mark   int32  `json:"mark,omitempty;"`
	Remark string `json:"remark,omitempty;"`
}

type KVSShareDataRsp struct {
	Sn string `json:"sn"`
}

type KVSShareDevData KVSShareData

type KVSShareQuickData struct {
	Type    int
	Account string
	Data    string
	Sn      string
	Mark    int32
	Remark  string
}

type KVSShareAddressBookData struct {
	Sn     string `json:"sn"`
	Remark string `json:"remark,omitempty;"`
}
