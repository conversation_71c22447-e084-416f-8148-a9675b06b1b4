package worker

const (
	PubPrefix           = "pub_"
	DevPrefix           = "dev_"
	CliPrefix           = "cli_"
	P2PForwardPrefix    = "pub_p2p_"
	P2PForwardPrefixLen = len(P2PForwardPrefix)
)

// 跨区域获取数据相关定义
const (
	JIDRegionGetCidKVS          = "rgn_get_cid_kvs"
	JIDRegionGetCidKVSRsp       = "rgn_get_cid_kvs_rsp"
	JIDRegionRandomAdd          = "rgn_random_add"
	JIDRegionRandomAddRsp       = "rgn_random_add_rsp"
	JIDRegionDPClear            = "rgn_dp_clear"
	JIDRegionDPClearRsp         = "rgn_dp_clear_rsp"
	JIDRegionMsgCount           = "rgn_msg_count"
	JIDRegionMsgCountRsp        = "rgn_msg_count_rsp"
	JIDRegionMoveData           = "rgn_move_data"
	JIDRegionMoveDataRsp        = "rgn_move_data_rsp"
	JIDRegionClearMac           = "rgn_clear_mac"
	JIDRegionClearMacRsp        = "rgn_clear_mac_rsp"
	JIDRegionGetCidMsgCenter    = "rgn_get_cid_msg_center"
	JIDRegionGetCidMsgCenterRsp = "rgn_get_cid_msg_center_rsp"
)

/*
公共消息类
*/
const (
	JIDPubHeaderError       = "pub_header_error"
	JIDPubHeaderErrorRsp    = "pub_header_error_rsp"
	JIDPubHeaderIDError     = "pub_header_id_error"
	JIDPubHeaderIDErrorRsp  = "pub_header_id_error_rsp"
	JIDPubHeartBeat         = "pub_heartbeat"
	JIDPubHeartBeatRsp      = "pub_heartbeat_rsp"
	JIDPubRecordGet         = "pub_record_get"
	JIDPubRecordGetRsp      = "pub_record_get_rsp"
	JIDPubRecordSet         = "pub_record_set"
	JIDPubRecordSetRsp      = "pub_record_set_rsp"
	JIDPubRecordIn          = "pub_record_in"
	JIDPubRecordInRsp       = "pub_record_in_rsp"
	JIDPubDpUnread          = "pub_dp_unread"
	JIDPubDpUnreadRsp       = "pub_dp_unread_rsp"
	JIDPubDpUnreadMuti      = "pub_dp_unread_muti"
	JIDPubDpUnreadMutiRsp   = "pub_dp_unread_muti_rsp"
	JIDPubDpDel             = "pub_dp_del"
	JIDPubDpDelRsp          = "pub_dp_del_rsp"
	JIDPubDpDelMuti         = "pub_dp_del_muti"
	JIDPubDpDelMutiRsp      = "pub_dp_del_muti_rsp"
	JIDPubDpAct             = "pub_dp_act"
	JIDPubDpActRsp          = "pub_dp_act_rsp"
	JIDPubDpGetMuti         = "pub_dp_get_muti"
	JIDPubDpGetMutiRsp      = "pub_dp_get_muti_rsp"
	JIDPubDpGetMutiCount    = "pub_dp_get_muti_count"
	JIDPubDpGetMutiCountRsp = "pub_dp_get_muti_count_rsp"
	JIDPubDpGet             = "pub_dp_get"
	JIDPubDpGetRsp          = "pub_dp_get_rsp"
	JIDPubDpGetList         = "pub_dp_get_list"
	JIDPubDpGetListRsp      = "pub_dp_get_list_rsp"
	JIDPubDpSet             = "pub_dp_set"
	JIDPubDpSetRsp          = "pub_dp_set_rsp"
	JIDPubDpMidData         = "pub_dp_mid_data"
	JIDPubDpMidDataRsp      = "pub_dp_mid_data_rsp"
	JIDPubP2pForward        = "pub_p2p_forward"
	JIDPubP2pForwardRsp     = "pub_p2p_forward_rsp"
	// 通用错误回复消息
	JIDPubDpPushError = "pub_dp_push_error"
	JIDPubDpPushData  = "pub_dp_push_data"
	// 上报
	JIDPubReportOffline = "pub_report_offline"
	JIDPubReportOnline  = "pub_report_online"
)

/*
设备消息类
*/
const (
	JIDDevLogin                    = "dev_login"
	JIDDevLoginRsp                 = "dev_login_rsp"
	JIDDevReset                    = "dev_reset"
	JIDDevResetRsp                 = "dev_reset_rsp"
	JIDDevGetSecretNo              = "dev_get_secret_no" // 保留，暂时不用
	JIDDevGetSecretNoRsp           = "dev_get_secret_no_rsp"
	JIDDevRandom                   = "dev_random"
	JIDDevRandomRsp                = "dev_random_rsp"
	JIDDevCheckVersion             = "dev_check_version"
	JIDDevCheckVersionRsp          = "dev_check_version_rsp"
	JIDDevGetUrlByOsVersion        = "dev_get_url_by_os_version" // 暂不实现
	JIDDevGetUrlByOsVersionRsp     = "dev_get_url_by_os_version_rsp"
	JIDDevCheckVersionParts        = "dev_check_version_parts"
	JIDDevCheckVersionPartsRsp     = "dev_check_version_parts_rsp"
	JIDDevCheckVersionPartsV2      = "dev_check_version_parts_v2"
	JIDDevCheckVersionPartsV2Rsp   = "dev_check_version_parts_v2_rsp"
	JIDDevGetOssApiUrl             = "dev_get_oss_api_url"
	JIDDevGetOssApiUrlRsp          = "dev_get_oss_api_url_rsp"
	JIDDevGetDst                   = "dev_get_dst"
	JIDDevGetDstRsp                = "dev_get_dst_rsp"
	JIDDevGetServiceToken          = "dev_get_service_token"
	JIDDevGetServiceTokenRsp       = "dev_get_service_token_rsp"
	JIDDevGetStrategy              = "dev_get_strategy"
	JIDDevGetStrategyRsp           = "dev_get_strategy_rsp"
	JIDDevGetAiApiUrl              = "dev_get_ai_api_url"
	JIDDevGetAiApiUrlRsp           = "dev_get_ai_api_url_rsp"
	JIDDevGetLoginAddr             = "dev_get_login_addr"
	JIDDevGetLoginAddrRsp          = "dev_get_login_addr_rsp"
	JIDDevGetOssConfig             = "dev_get_oss_config"
	JIDDevGetOssConfigRsp          = "dev_get_oss_config_rsp"
	JIDDevGetCloudStorageConfig    = "dev_get_cloud_storage_config"
	JIDDevGetCloudStorageConfigRsp = "dev_get_cloud_storage_config_rsp"
	JIDDevCameraPreset             = "dev_camera_preset"
	JIDDevCameraPresetRsp          = "dev_camera_preset_rsp"
	JIDDevBurnLogin                = "dev_burn_login"
	JIDDevBurnLoginRsp             = "dev_burn_login_rsp"
	JIDDevKoreaLogin               = "dev_korea_login"
	JIDDevKoreaLoginRsp            = "dev_korea_login_rsp"
	JIDDevMiruLogin                = "dev_miru_login"
	JIDDevMiruLoginRsp             = "dev_miru_login_rsp"
	// 上报类接口
	JIDDevReportWarn           = "dev_report_warn"
	JIDDevBellCall             = "dev_bell_call"
	JIDDevBellCallCancel       = "dev_bell_call_cancel"
	JIDDevReportBellCallStatus = "dev_report_bell_call_status"
	// 推送类接口
	JIDDevPushDebugLog = "dev_push_debug_log"
	JIDDevPushData     = "dev_push_data"
	JIDDevPushRelogin  = "dev_push_relogin"
	JIDDevPushReboot   = "dev_push_reboot"
)

/*
APP消息类
*/
const (
	JIDCliRegister                   = "cli_register"
	JIDCliRegisterRsp                = "cli_register_rsp"
	JIDCliCodeGet                    = "cli_code_get"
	JIDCliCodeGetRsp                 = "cli_code_get_rsp"
	JIDCliVoiceCodeGet               = "cli_voice_code_get"
	JIDCliVoiceCodeGetRsp            = "cli_voice_code_get_rsp"
	JIDCliCodeCheck                  = "cli_code_check"
	JIDCliCodeCheckRsp               = "cli_code_check_rsp"
	JIDCliPasswdSet                  = "cli_passwd_set"
	JIDCliPasswdSetRsp               = "cli_passwd_set_rsp"
	JIDCliPasswdChange               = "cli_passwd_change"
	JIDCliPasswdChangeRsp            = "cli_passwd_change_rsp"
	JIDCliForgetPasswdEmail          = "cli_forget_passwd_email"
	JIDCliForgetPasswdEmailRsp       = "cli_forget_passwd_email_rsp"
	JIDCliUnregisterCodeGet          = "cli_unregister_code_get"
	JIDCliUnregisterCodeGetRsp       = "cli_unregister_code_get_rsp"
	JIDCliUnregister                 = "cli_unregister"
	JIDCliUnregisterRsp              = "cli_unregister_rsp"
	JIDCliLogin                      = "cli_login"
	JIDCliLoginRsp                   = "cli_login_rsp"
	JIDCliOpenCheck                  = "cli_open_check"
	JIDCliOpenCheckRsp               = "cli_open_check_rsp"
	JIDCliOpenLogin                  = "cli_open_login"
	JIDCliOpenLoginRsp               = "cli_open_login_rsp"
	JIDCliLogout                     = "cli_logout"
	JIDCliLogoutRsp                  = "cli_logout_rsp"
	JIDCliGetLoginAddr               = "cli_get_login_addr"
	JIDCliGetLoginAddrRsp            = "cli_get_login_addr_rsp"
	JIDCliShareOperate               = "cli_share_operate"
	JIDCliShareOperateRsp            = "cli_share_operate_rsp"
	JIDCliShareQuickGet              = "cli_share_quick_get"
	JIDCliShareQuickGetRsp           = "cli_share_quick_get_rsp"
	JIDCliShareQuickSet              = "cli_share_quick_set"
	JIDCliShareQuickSetRsp           = "cli_share_quick_set_rsp"
	JIDCliShareQuickCheck            = "cli_share_quick_check"
	JIDCliShareQuickCheckRsp         = "cli_share_quick_check_rsp"
	JIDCliShareList                  = "cli_share_list"
	JIDCliShareListRsp               = "cli_share_list_rsp"
	JIDCliFeedbackQuestionList       = "cli_feedback_question_list"
	JIDCliFeedbackQuestionListRsp    = "cli_feedback_question_list_rsp"
	JIDCliFeedbackPut                = "cli_feedback_put"
	JIDCliFeedbackPutRsp             = "cli_feedback_put_rsp"
	JIDCliFeedbackList               = "cli_feedback_list"
	JIDCliFeedbackListRsp            = "cli_feedback_list_rsp"
	JIDCliDevGroupAdd                = "cli_dev_group_add"
	JIDCliDevGroupAddRsp             = "cli_dev_group_add_rsp"
	JIDCliDevGroupDel                = "cli_dev_group_del"
	JIDCliDevGroupDelRsp             = "cli_dev_group_del_rsp"
	JIDCliDevGroupEdit               = "cli_dev_group_edit"
	JIDCliDevGroupEditRsp            = "cli_dev_group_edit_rsp"
	JIDCliDevGroupList               = "cli_dev_group_list"
	JIDCliDevGroupListRsp            = "cli_dev_group_list_rsp"
	JIDCliDevUpdate                  = "cli_dev_update"
	JIDCliDevUpdateRsp               = "cli_dev_update_rsp"
	JIDCliDevAdd                     = "cli_dev_add"
	JIDCliDevAddRsp                  = "cli_dev_add_rsp"
	JIDCliDevAddMuti                 = "cli_dev_add_muti"
	JIDCliDevAddMutiRsp              = "cli_dev_add_muti_rsp"
	JIDCliDevQrAdd                   = "cli_dev_qr_add"
	JIDCliDevQrAddRsp                = "cli_dev_qr_add_rsp"
	JIDCliDevDel                     = "cli_dev_del"
	JIDCliDevDelRsp                  = "cli_dev_del_rsp"
	JIDCliDevDelMuti                 = "cli_dev_del_muti"
	JIDCliDevDelMutiRsp              = "cli_dev_del_muti_rsp"
	JIDCliDevAddRandom               = "cli_dev_add_random"
	JIDCliDevAddRandomRsp            = "cli_dev_add_random_rsp"
	JIDCliDevList                    = "cli_dev_list"
	JIDCliDevListRsp                 = "cli_dev_list_rsp"
	JIDCliDevListUngroup             = "cli_dev_list_ungroup"
	JIDCliDevListUngroupRsp          = "cli_dev_list_ungroup_rsp"
	JIDCliDevButtonDefineList        = "cli_dev_button_define_list"
	JIDCliDevButtonDefineListRsp     = "cli_dev_button_define_list_rsp"
	JIDCliDevButtonDefineSet         = "cli_dev_button_define_set"
	JIDCliDevButtonDefineSetRsp      = "cli_dev_button_define_set_rsp"
	JIDCliDevVoiceDefineList         = "cli_dev_voice_define_list"
	JIDCliDevVoiceDefineListRsp      = "cli_dev_voice_define_list_rsp"
	JIDCliDevAddressBookSet          = "cli_dev_address_book_set"
	JIDCliDevAddressBookSetRsp       = "cli_dev_address_book_set_rsp"
	JIDCliDevAddressBookList         = "cli_dev_address_book_list"
	JIDCliDevAddressBookListRsp      = "cli_dev_address_book_list_rsp"
	JIDCliDevAddressBookDel          = "cli_dev_address_book_del"
	JIDCliDevAddressBookDelRsp       = "cli_dev_address_book_del_rsp"
	JIDCliAttrDevList                = "cli_attr_dev_list"
	JIDCliAttrDevListRsp             = "cli_attr_dev_list_rsp"
	JIDCliAccountInfoGet             = "cli_account_info_get"
	JIDCliAccountInfoGetRsp          = "cli_account_info_get_rsp"
	JIDCliAccountInfoSet             = "cli_account_info_set"
	JIDCliAccountInfoSetRsp          = "cli_account_info_set_rsp"
	JIDCliPushTokenSet               = "cli_push_token_set"
	JIDCliPushTokenSetRsp            = "cli_push_token_set_rsp"
	JIDCliAwsCredentialGet           = "cli_aws_credential_get"
	JIDCliAwsCredentialGetRsp        = "cli_aws_credential_get_rsp"
	JIDCliAwsCredentialGetMuti       = "cli_aws_credential_get_muti"
	JIDCliAwsCredentialGetMutiRsp    = "cli_aws_credential_get_muti_rsp"
	JIDCliOssCredentialGet           = "cli_oss_credential_get"
	JIDCliOssCredentialGetRsp        = "cli_oss_credential_get_rsp"
	JIDCliOssCredentialGetMuti       = "cli_oss_credential_get_muti"
	JIDCliOssCredentialGetMutiRsp    = "cli_oss_credential_get_muti_rsp"
	JIDCliCosCredentialGet           = "cli_cos_credential_get"
	JIDCliCosCredentialGetRsp        = "cli_cos_credential_get_rsp"
	JIDCliCosCredentialGetMuti       = "cli_cos_credential_get_muti"
	JIDCliCosCredentialGetMutiRsp    = "cli_cos_credential_get_muti_rsp"
	JIDCliOsAttrGet                  = "cli_os_attr_get"
	JIDCliOsAttrGetRsp               = "cli_os_attr_get_rsp"
	JIDCliOsRuleGet                  = "cli_os_rule_get"
	JIDCliOsRuleGetRsp               = "cli_os_rule_get_rsp"
	JIDCliOsAttrWebGet               = "cli_os_attr_web_get"
	JIDCliOsAttrWebGetRsp            = "cli_os_attr_web_get_rsp"
	JIDCliAppUpgradeGet              = "cli_app_upgrade_get"
	JIDCliAppUpgradeGetRsp           = "cli_app_upgrade_get_rsp"
	JIDCliMsgCount                   = "cli_msg_count"
	JIDCliMsgCountRsp                = "cli_msg_count_rsp"
	JIDCliCameraPresetSet            = "cli_camera_preset_set"
	JIDCliCameraPresetSetRsp         = "cli_camera_preset_set_rsp"
	JIDCliCameraPresetList           = "cli_camera_preset_list"
	JIDCliCameraPresetListRsp        = "cli_camera_preset_list_rsp"
	JIDCliCloudStorageGet            = "cli_cloud_storage_get"
	JIDCliCloudStorageGetRsp         = "cli_cloud_storage_get_rsp"
	JIDCliBellCallStatus             = "cli_bell_call_status"
	JIDCliBellCallStatusRsp          = "cli_bell_call_status_rsp"
	JIDCliQrLoginGetWebsocketList    = "cli_qr_login_get_websocket_list"
	JIDCliQrLoginGetWebsocketListRsp = "cli_qr_login_get_websocket_list_rsp"
	JIDCliQrLoginRandom              = "cli_qr_login_random"
	JIDCliQrLoginRandomRsp           = "cli_qr_login_random_rsp"
	JIDCliQrLoginAppScan             = "cli_qr_login_app_scan"
	JIDCliQrLoginAppScanRsp          = "cli_qr_login_app_scan_rsp"
	JIDCliBaseConfigGet              = "cli_base_config_get"
	JIDCliBaseConfigGetRsp           = "cli_base_config_get_rsp"
	JIDCliOsVideoList                = "cli_os_video_list"
	JJIDCliOsVideoListRsp            = "cli_os_video_list_rsp"
	JIDCliUseQuestionList            = "cli_use_question_list"
	JIDCliUseQuestionListRsp         = "cli_use_question_list_rsp"
	JIDCliCommonQuestionList         = "cli_common_question_list"
	JIDCliCommonQuestionListRsp      = "cli_common_question_list_rsp"
	JIDCliBannerGet                  = "cli_banner_get"
	JIDCliBannerGetRsp               = "cli_banner_get_rsp"
	JIDCliBurnLogin                  = "cli_burn_login"
	JIDCliBurnLoginRsp               = "cli_burn_login_rsp"
	JIDCliBurnDevList                = "cli_burn_dev_list"
	JIDCliBurnDevListRsp             = "cli_burn_dev_list_rsp"
	JIDCliKoreaLogin                 = "cli_korea_login"
	JIDCliKoreaLoginRsp              = "cli_korea_login_rsp"
	JIDCliKoreaDevList               = "cli_korea_dev_list"
	JIDCliKoreaDevListRsp            = "cli_korea_dev_list_rsp"
	JIDCliMiruLogin                  = "cli_miru_login"
	JIDCliMiruLoginRsp               = "cli_miru_login_rsp"
	// 上报类接口
	JIDCliBellConnected = "cli_bell_connected"
	// 推送类接口
	JIDCliPushRefresh = "cli_push_refresh"
	JIDCliPushData    = "cli_push_data"
	JIDCliPushNet     = "cli_push_net"
	JIDCliPushReboot  = "cli_push_reboot"
)

/*
小程序消息类
*/
const (
	// 小程序
	JIDCliTencentMiniprogramCheck    = "cli_tencent_miniprogram_check"
	JIDCliTencentMiniprogramCheckRsp = "cli_tencent_miniprogram_check_rsp"
	JIDCliTencentMiniprogramLogin    = "cli_tencent_miniprogram_login"
	JIDCliTencentMiniprogramLoginRsp = "cli_tencent_miniprogram_login_rsp"
	// iotvideo
	JIDCliTencentIotvideoDeviceStatusGet    = "cli_tencent_iotvideo_device_status_get"
	JIDCliTencentIotvideoDeviceStatusGetRsp = "cli_tencent_iotvideo_device_status_get_rsp"
	JIDCliTencentIotvideoDeviceDataGet      = "cli_tencent_iotvideo_device_data_get"
	JIDCliTencentIotvideoDeviceDataGetRsp   = "cli_tencent_iotvideo_device_data_get_rsp"
	JIDCliTencentIotvideoSnTicketGet        = "cli_tencent_iotvideo_sn_ticket_get"
	JIDCliTencentIotvideoSnTicketGetRsp     = "cli_tencent_iotvideo_sn_ticket_get_rsp"
	// 其他业务
	JIDCliTencentDevButtonDefineList    = "cli_tencent_dev_button_define_list"
	JIDCliTencentDevButtonDefineListRsp = "cli_tencent_dev_button_define_list_rsp"
	JIDCliTencentDevVoiceDefineList     = "cli_tencent_dev_voice_define_list"
	JIDCliTencentDevVoiceDefineListRsp  = "cli_tencent_dev_voice_define_list_rsp"
)

var JIDFuncMap = map[string]string{
	JIDPubHeartBeat:     "pub_forward",
	JIDPubHeartBeatRsp:  "pub_forward",
	JIDPubP2pForward:    "pub_forward",
	JIDPubP2pForwardRsp: "pub_forward",
}

//为了符合go规范。消息注册的默认名
var JIDFuncList = []string{
	// public消息注册
	JIDPubDpUnreadMuti,
	JIDPubDpDel,
	JIDPubDpDelMuti,
	JIDPubDpAct,
	JIDPubDpActRsp,
	JIDPubDpGet,
	JIDPubDpGetList,
	JIDPubDpGetMuti,
	JIDPubDpGetMutiCount,
	JIDPubDpSet,
	JIDPubDpMidData,
	JIDPubReportOffline,
	JIDPubReportOnline,
	JIDPubHeartBeat,
	JIDPubHeartBeatRsp,
	JIDPubP2pForward,
	JIDPubP2pForwardRsp,
	JIDPubRecordGet,
	JIDPubRecordSet,
	JIDPubRecordIn,

	// device消息注册
	JIDDevLogin,
	JIDDevReset,
	JIDDevGetSecretNo,
	JIDDevRandom,
	JIDDevCheckVersion,
	//JIDDevGetUrlByOsVersion  // 暂不实现
	JIDDevCheckVersionParts,
	JIDDevCheckVersionPartsV2,
	JIDDevGetOssApiUrl,
	JIDDevGetDst,
	JIDDevGetServiceToken,
	JIDDevGetStrategy,
	JIDDevGetAiApiUrl,
	JIDDevGetLoginAddr,
	JIDDevGetOssConfig,
	JIDDevGetCloudStorageConfig,
	JIDDevCameraPreset,
	JIDDevBurnLogin,
	JIDDevKoreaLogin,
	JIDDevMiruLogin,
	// 上报类接口
	JIDDevReportWarn,
	JIDDevBellCall,
	JIDDevBellCallCancel,
	JIDDevReportBellCallStatus,

	// client消息注册
	JIDCliRegister,
	JIDCliCodeGet,
	JIDCliVoiceCodeGet,
	JIDCliCodeCheck,
	JIDCliPasswdSet,
	JIDCliPasswdChange,
	JIDCliForgetPasswdEmail,
	JIDCliUnregisterCodeGet,
	JIDCliUnregister,
	JIDCliLogout,
	JIDCliLogin,
	JIDCliOpenCheck,
	JIDCliOpenLogin,
	JIDCliGetLoginAddr,
	JIDCliShareOperate,
	JIDCliShareQuickGet,
	JIDCliShareQuickSet,
	JIDCliShareQuickCheck,
	JIDCliShareList,
	JIDCliFeedbackQuestionList,
	JIDCliFeedbackPut,
	JIDCliFeedbackList,
	JIDCliDevGroupAdd,
	JIDCliDevGroupDel,
	JIDCliDevGroupEdit,
	JIDCliDevGroupList,
	JIDCliDevUpdate,
	JIDCliDevAdd,
	JIDCliDevAddMuti,
	JIDCliDevQrAdd,
	JIDCliDevDel,
	JIDCliDevDelMuti,
	JIDCliDevAddRandom,
	JIDCliDevList,
	JIDCliDevListUngroup,
	JIDCliDevButtonDefineList,
	JIDCliDevButtonDefineSet,
	JIDCliDevVoiceDefineList,
	JIDCliDevAddressBookSet,
	JIDCliDevAddressBookList,
	JIDCliDevAddressBookDel,
	JIDCliAttrDevList,
	JIDCliAccountInfoGet,
	JIDCliAccountInfoSet,
	JIDCliPushTokenSet,
	JIDCliAwsCredentialGet,
	JIDCliAwsCredentialGetMuti,
	JIDCliOssCredentialGet,
	JIDCliOssCredentialGetMuti,
	JIDCliCosCredentialGet,
	JIDCliCosCredentialGetMuti,
	JIDCliOsAttrGet,
	JIDCliOsRuleGet,
	JIDCliOsAttrWebGet,
	JIDCliAppUpgradeGet,
	JIDCliMsgCount,
	JIDCliCameraPresetSet,
	JIDCliCameraPresetList,
	JIDCliCloudStorageGet,
	JIDCliBellCallStatus,
	JIDCliQrLoginGetWebsocketList,
	JIDCliQrLoginRandom,
	JIDCliQrLoginAppScan,
	JIDCliBaseConfigGet,
	JIDCliOsVideoList,
	JIDCliUseQuestionList,
	JIDCliCommonQuestionList,
	JIDCliBannerGet,
	JIDCliBurnLogin,
	JIDCliBurnDevList,
	JIDCliKoreaLogin,
	JIDCliKoreaDevList,
	JIDCliMiruLogin,
	JIDCliPushReboot,
	// 上报类
	JIDCliBellConnected,

	// 小程序
	JIDCliTencentMiniprogramCheck,
	JIDCliTencentMiniprogramLogin,
	// iotvideo
	JIDCliTencentIotvideoDeviceStatusGet,
	JIDCliTencentIotvideoDeviceDataGet,
	JIDCliTencentIotvideoSnTicketGet,
	// 其他业务
	JIDCliTencentDevButtonDefineList,
	JIDCliTencentDevVoiceDefineList,
}

var JIDWantCallList = []string{
	JIDPubDpUnread,
	JIDPubDpUnreadRsp,
	// JIDPubDpUnreadMuti,
	// JIDPubDpUnreadMutiRsp,
	JIDPubDpDel,
	// JIDPubDpDelMuti,
	JIDPubDpDelRsp,
	JIDPubDpAct,
	JIDPubDpActRsp,
	// JIDPubDpGetMuti,
	// JIDPubDpGetMutiCount,
	// JIDPubDpGetMutiRsp,
	// JIDPubDpGetMutiCountRsp,
	// JIDPubDpGet,
	// JIDPubDpGetRsp,
	JIDPubDpSet,
	JIDPubDpSetRsp,
	JIDPubDpPushError,
	JIDPubDpPushData,
	JIDPubDpMidData,
	JIDPubDpMidDataRsp,
	JIDPubP2pForward,
	JIDPubP2pForwardRsp,
	//client端
	JIDCliDevAddRsp,
	JIDCliOssCredentialGetRsp,
	JIDCliBellConnected,
}

func IsWantCall(id string) bool {
	for _, val := range JIDWantCallList {
		if id == val {
			return true
		}
	}
	return false
}

var JIDNotWantLoginList = []string{
	JIDCliRegister,
	JIDCliCodeGet,
	JIDCliVoiceCodeGet,
	JIDCliCodeCheck,
	JIDCliPasswdSet,
	JIDCliForgetPasswdEmail,
	JIDCliLogin,
	JIDCliOpenCheck,
	JIDCliOpenLogin,
	JIDCliGetLoginAddr,
	JIDCliAppUpgradeGet,
	JIDCliQrLoginGetWebsocketList,
	JIDCliQrLoginRandom,
	JIDCliBaseConfigGet,
	JIDCliBellCallStatus,
	JIDCliBurnLogin,
	JIDCliKoreaLogin,
	JIDDevLogin,
	JIDDevReset,
	JIDDevRandom,
	JIDDevCheckVersion,
	JIDDevCheckVersionParts,
	JIDDevGetDst,
	JIDDevGetServiceToken,
	JIDDevGetLoginAddr,
	JIDPubHeartBeat,
	JIDDevBurnLogin,
	JIDDevKoreaLogin,
	JIDDevMiruLogin,
	JIDCliMiruLogin,
	JIDCliTencentMiniprogramCheck,
	JIDCliTencentMiniprogramLogin,
}

func IsNotWantLogin(id string) bool {
	for _, val := range JIDNotWantLoginList {
		if id == val {
			return true
		}
	}
	return false
}
