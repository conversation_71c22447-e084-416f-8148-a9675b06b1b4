package main

import (
	"encoding/json"
	"fmt"
	"mirulib"
	"mirulib/consul"
	"mirulib/worker"
	"time"

	"golang.org/x/net/websocket"
)

const (
	connSize     = 50000
	invalidSeq   = 0
	invalidMsgID = -1
	// 每个发送队列暂存多少条消息,超出之后直接丢弃
	sendChannelCap = 20
	// 一个客户端最多连接多少个摄像头
	maxConnectedPeers = 10
	N                 = 97
	G                 = 5
	// ws接受数据大小
	WsRecvSize = 1024 * 1024
)

// ConnRoutine 关键字
const (
	// 关闭连接
	ConnCmdClose = iota
	// 关闭重复Session连接
	ConnCmdCloseDupSess
	// 更新session
	ConnCmdUpdateSess
	// 登陆成功
	ConnCmdUpdateLoginOk
	// 登出
	ConnCmdUpdateLogout
	// 发送消息到客户端
	ConnCmdSendMsg
	// 客户端接收消息
	ConnCmdRecvMsg
	// 登陆更新新旧版本状态
	//ConnCmdUpdateLoginMsgIDFlag
	// 验证通过，可以开始交换密钥
	//ConnCmdHandleExchange
	// 强制使用不加密方式发送（使用ConnCmdSendMsg会自动选择加密或者不加密）
	//ConnCmdSendMsgUnEncrypted
	// 更新debug字符串
	ConnCmdUpdateDebugStr
	// 更新心跳时间
	ConnCmdUpdateHBTime
	// 心跳超时
	ConnCmdHBTimeout
	// 心跳探测
	ConnCmdOperateTick
	//
	ConnCmdSlidingExpiration
)

type ConnCmd struct {
	Cmd      uint8
	Arg      interface{}
	IsJson   bool
	Net      int
	Debug    bool
	UpdateHB bool
}

var (
	windowLen  int64 = 60
	bucketLen  int64 = 6
	operateMax int64 = 100
)

// WsService WS服务
type WsService struct {
	Conn    *websocket.Conn
	ID      mirulib.SEQ
	Net     int
	Session string
	Addr    string
	Closed  bool
	CmdCh   chan ConnCmd
	//OutCh        chan int
	loginOk bool
	//exchangeSeq  int64
	//randoms      []int32
	//exchangeDone bool
	loginTime int64 // 正常登录的时间
	hbTime    int64
	closed    bool
	// operateSW mirulib.SlidingWindow
	// 心跳探测
	tickClosed      bool
	tick            *time.Ticker
	UpdateHeartBeat bool
}

var tickTime int = 30

// NewWsService 初始化WS服务
func NewWsService(conn *websocket.Conn, addr string) *WsService {
	ws := WsService{
		Conn:       conn,
		Addr:       addr,
		CmdCh:      make(chan ConnCmd, 100),
		tick:       time.NewTicker(time.Second * time.Duration(tickTime)),
		tickClosed: true,
		// 60S窗口内，手动操作次数不能超过100次
		// operateSW: mirulib.NewSlidingWindow(windowLen, bucketLen, 0, operateMax, mirulib.SlidingTypeSmall),
	}
	ws.tick.Stop()
	return &ws
}

// Recv WS接收消息
func (p *WsService) Run() {
	defer mirulib.LogPanic()

	static.updateConnAccept()

	//closeCh := make(chan bool)
	go p.Recv()

	// 检测是否收到login返回
	// 时间180s，规定时间内没有登录成功，则踢除
	timer := time.NewTicker(time.Second * time.Duration(conf.App.MiruServer.KeepAlive.Login))
	tickClosed := false
	defer func() {
		static.updateConnDisconn()
		if !tickClosed {
			timer.Stop()
		}
		tickClosed = true
		p.Close()
	}()

	//CONNLOOP:
	for {
		select {
		case msg := <-p.CmdCh:
			// 控制指令
			switch msg.Cmd {
			case ConnCmdHBTimeout, ConnCmdClose:
				logger.Info("cmd[%d] close conn [%d][%s]", msg.Cmd, p.ID, p.Session)
				if p.loginOk && p.Session != "" {
					offlineMsg := worker.JsonMarshal(worker.GetJsonHeader(worker.JIDPubReportOffline, ""))
					SendQClientMsg2MQ(worker.JIDPubReportOffline, p.ID, p.Session, p.Addr, "", offlineMsg)
				}
				return
			case ConnCmdCloseDupSess:
				logger.Info("%d, %s, close duplicate session", p.ID, p.Session)
				return
			// case ConnCmdClose:
			// 	logger.Info("%d, %s, close on command", p.ID, p.Session)
			// 	p.Close()
			// 	return
			case ConnCmdUpdateHBTime:
				logger.Info("cmd[%d] update heart beat time [%d][%s][%v]", msg.Cmd, p.ID, p.Session, p.UpdateHeartBeat)
				p.UpdateHBTime()
				if p.UpdateHeartBeat {
					logger.Info("cmd[%d] send", msg.Cmd)
					offlineMsg := worker.JsonMarshal(worker.GetJsonHeader(worker.JIDPubReportOnline, ""))
					SendQClientMsg2MQ(worker.JIDPubReportOnline, p.ID, p.Session, p.Addr, "", offlineMsg)
				}
				break
			case ConnCmdUpdateSess:
				sessid := msg.Arg.(string)
				logger.Info("%d, %s, update sess", p.ID, sessid)
				p.UpdateSession(sessid, msg.Net)
				break
			case ConnCmdUpdateLoginOk:
				logger.Info("%d, %s, update login ok", p.ID, p.Session)
				p.UpdateLogin()
				break
			case ConnCmdSlidingExpiration:
				logger.Info("%d, %s, update hb status ok", p.ID, p.Session)
				p.UpdateHBStatus()
			case ConnCmdUpdateLogout:
				/*logger.Info("%d, %s, update logout", p.ID, p.Session)
				p.updateLogout()
				if !tickClosed {
					timer.Stop()
				}
				tickClosed = false
				timer = time.NewTicker(time.Second * time.Duration(60))
				incoming = false*/
				return
			case ConnCmdSendMsg:
				p.Send(msg.Arg, msg.IsJson, msg.Debug)
				break
			case ConnCmdOperateTick:
				p.updateTick(msg.Arg.(bool))
				break
				//case ConnCmdRecvMsg:
				/*msgID, err := c.recvMsg(msg.arg)
				if err != nil {
					logger.Errorf("%s", err)
					closeConn(conn)
				}
				// 大部分时候tickClosed,减少计算
				if !tickClosed && !incoming && msgID != mirulib.IDKeepAlive {
					incoming = true
				}*/
				// case ConnCmdUpdateLoginMsgIDFlag:
				// 	logger.Info("%d, %s, update msg id flag %v", p.ID, p.Session, msg.Arg)
				//c.updateMsgIDFlag(msg.arg)
				// case ConnCmdHandleExchange:
				// 	ex := msg.Arg.(*mirulib.MsgKeyExchange)
				// 	p.handleKeyExchange(ex)
				// case ConnCmdSendMsgUnEncrypted:
				// 	p.Send(msg.Arg)
			}
		case <-timer.C:
			if !p.loginOk {
				logger.Errorf("%d, not login, close", p.ID)
				p.Close()
			}
			tickClosed = true
			timer.Stop()
			break
		/*case <-closeCh:
		break CONNLOOP */
		case <-p.tick.C:
			//如果检测心跳超时，该设备将被视为离线，关闭
			if !p.tickClosed {
				logger.Info("Check heart beat exceed %d, close conn [%d][%s]", tickTime, p.ID, p.Session)
				p.tick.Stop()
				p.tickClosed = true
				p.Close()
			}
		}
	}
}

// Close 关闭WS连接
func (p *WsService) Close() {
	if p.Closed {
		return
	}
	p.Closed = true
	close(p.CmdCh)
	p.Conn.Close()

	if !p.tickClosed {
		logger.Debug("stop timer %v", p.Session)
		p.tick.Stop()
		p.tickClosed = true
	}

	srvm.closeDelete(p.ID, p.Session)
}

// UpdateSession 更新session id
func (p *WsService) UpdateSession(sessid string, net int) {
	p.Session = sessid
	if net > 0 {
		p.Net = net
	} else {
		p.Net = mirulib.NetNewWifi
	}

	srvm.addSessionKey(sessid, p)
}

// UpdateSession 更新session id
func (p *WsService) UpdateHBStatus() {
	p.UpdateHeartBeat = true
}

// UpdateLogin 更新登录状态
func (p *WsService) UpdateLogin() {
	p.loginOk = true
	p.loginTime = time.Now().Unix()
}

func (p *WsService) UpdateHBTime() {
	p.hbTime = time.Now().Unix()
}

// Recv WS接收消息
func (p *WsService) Recv() {
	var err error

	defer func() {
		mirulib.LogPanic()
		p.ChanSendMsg(ConnCmd{
			Cmd: ConnCmdClose,
		})
	}()

	for {
		if p.Closed {
			break
		}

		hbCheck := time.Now().Add(time.Duration(conf.App.MiruServer.KeepAlive.Idle) * time.Second)
		err = p.Conn.SetReadDeadline(hbCheck)
		logger.Debug("Conn ID: [%d] err:%v set time:%v", p.ID, err, hbCheck)

		var buf []byte
		err = websocket.Message.Receive(p.Conn, &buf) //p.Conn.Read(buf)
		if err != nil {
			logger.Info("Conn ID: [%d] Receive Error: %s, break", p.ID, err)
			return
		}

		if err = p.Conn.SetReadDeadline(time.Time{}); err != nil {
			return
		}

		printBuf := buf
		lenBuf := len(buf)
		if lenBuf > 1024 {
			printBuf = buf[:1024]
		}
		logger.Debug("Conn ID: [%d]  Receive: %s", p.ID, printBuf)
		if buf == nil || len(buf) == 0 {
			logger.Info("Conn ID: [%d] buf is null return", p.ID)
			continue
		}

		var header worker.JsonHeader
		err = json.Unmarshal(buf, &header)
		if err != nil {
			logger.Info("Conn ID: [%d] Unmarshal err:%v return", p.ID, err)
			return
		}

		// 如果有消息接收，该定时器也需要清除
		if !p.tickClosed {
			logger.Debug("stop timer %v", p.Session)
			operateTick(p, invalidSeq, "", false)
		}

		// 收到消息都更新下心跳时间
		p.ChanSendMsg(ConnCmd{
			Cmd: ConnCmdUpdateHBTime,
		})

		if p.handleLocalMsg(header, buf) {
			continue
		}

		// 更新debugstr，方便追踪消息
		p.ChanSendMsg(ConnCmd{
			Cmd: ConnCmdUpdateDebugStr,
			Arg: header.Headers.ReqID,
		})

		static.updateMsgIDReceived(header.Headers.ID)

		if err := SendQClientMsg2MQ(header.Headers.ID, p.ID, p.Session, p.Addr, header.Headers.ReqID, buf); err != nil {
			logger.Errorf("sendQClientMsg failed:%s", err)
		}
	}
}

func (p *WsService) handleLocalMsg(header worker.JsonHeader, buf []byte) bool {
	if header.Headers.ID == "" {
		header.Headers.ID = worker.JIDPubHeaderError
		p.ChanSendMsg(ConnCmd{
			Cmd:    ConnCmdSendMsg,
			Arg:    worker.GetJsonRspHeader(header),
			IsJson: false,
		})
		return true
	}

	if header.Headers.ID == worker.JIDPubHeartBeat {
		p.HandleHeartBeat(header, buf, false)
		return true
	}

	if header.Headers.ID == worker.JIDPubHeartBeatRsp {
		p.HandleHeartBeatRsp(header, buf)
		return true
	}

	// if p.operateSW.IsExceedMaxEx(operateMax) {
	// 	p.ChanSendMsg(ConnCmd{
	// 		Cmd:    ConnCmdSendMsg,
	// 		Arg:    worker.GetJsonRspHeaderEx(header.Headers.ID, header.Headers.ReqID, worker.ErrorTooFrequenly),
	// 		IsJson: false,
	// 	})
	// 	return true
	// }
	// p.operateSW.Increment(1)

	notWantLoginIDS, _ := consul.GetNotWantLoginReq()
	if !p.loginOk && !worker.IsNotWantLogin(header.Headers.ID) && !mirulib.IsInArray(header.Headers.ID, notWantLoginIDS) {
		p.ChanSendMsg(ConnCmd{
			Cmd:    ConnCmdSendMsg,
			Arg:    worker.GetJsonRspHeaderEx(header.Headers.ID, header.Headers.ReqID, worker.ErrorNotLogin),
			IsJson: false,
		})
		return true
	}

	// 如果在同一个服务器，直接透传
	if header.Headers.ID[:worker.P2PForwardPrefixLen] == worker.P2PForwardPrefix {
		if p.HandleForward(header, buf) {
			return true
		}
	}

	// qos drop of this msgid
	if qosDrop(header.Headers.ID) {
		logger.Info("%d, %s, drop:%s", p.ID, p.Session, header.Headers.ID)
		static.updateQosDrop(header.Headers.ID)
		// TODO: need to send a suppress to peer
		loginWaitMsg := worker.JLoginWaitRsp{}
		loginWaitMsg.JsonRspHeader = worker.GetJsonRspHeaderEx(header.Headers.ID, header.Headers.ReqID, worker.ErrorWaitLogin)
		loginWaitMsg.Body.WaitLoginTime = srvm.loginPolicy.GetWaitTime()

		p.ChanSendMsg(ConnCmd{
			Cmd:    ConnCmdSendMsg,
			Arg:    loginWaitMsg,
			IsJson: false,
		})

		return true
	}

	return false
}

// Send WS发送消息
func (p *WsService) Send(msg interface{}, isJson, isDebug bool) {
	if !isJson {
		msg = worker.JsonMarshal(msg)
	}

	reqID := ""
	var data map[string]interface{}
	worker.JsonUnmarshal(&data, msg)
	headers, ok := data["headers"].(map[string]interface{})
	if ok && !worker.IsWantCall(headers["id"].(string)) {
		if data["body"] == nil {
			delete(data, "body")
		}

		if headers["callee"].(string) == "" {
			delete(headers, "caller")
			delete(headers, "callee")
		}

		data["headers"] = headers
		msg = worker.JsonMarshal(data)
		reqID = headers["req_id"].(string)
	}

	err := websocket.Message.Send(p.Conn, msg)
	if err != nil {
		logger.InfoDx(reqID, "ws send error: %s, SEQ: %d, sesssion: %s", err, p.ID, p.Session)
		return
	}

	printFunc := logger.InfoDx
	if isDebug {
		printFunc = logger.DebugDx
	}

	printFunc(reqID, "---Send: [%d][%s] %v", p.ID, p.Session, msg)

}

func (p *WsService) ChanSendMsg(msg ConnCmd) {
	defer mirulib.LogPanic()

	if len(p.CmdCh) == cap(p.CmdCh) {
		logger.Errorf("%d, %s, send channel full, total:%d", p.ID, p.Session, len(p.CmdCh))
		return
	}

	p.CmdCh <- msg
}

func (p *WsService) HandleHeartBeat(header worker.JsonHeader, buf []byte, isDetect bool) bool {
	callee := header.Headers.Callee
	if callee != "" || isDetect {
		logger.InfoDx(header.Headers.ReqID, "ppp send check %v", header)
		if p.HandleForward(header, buf) {
			operateTick(nil, invalidSeq, callee, true)
		} else {
			if err := SendQClientMsg2MQ(header.Headers.ID, p.ID, p.Session, p.Addr, header.Headers.ReqID, buf); err != nil {
				logger.Errorf("ppp sendQClientMsg failed:%s", err)
			}
		}
		return true
	}

	p.ChanSendMsg(ConnCmd{
		Cmd:    ConnCmdSendMsg,
		Arg:    worker.GetJsonRspHeader(header),
		IsJson: false,
		Debug:  true,
	})
	return true
}

func (p *WsService) HandleHeartBeatRsp(header worker.JsonHeader, buf []byte) bool {
	operateTick(p, p.ID, "", false)
	callee := header.Headers.Callee
	if callee != "" {
		logger.DebugDx(header.Headers.ReqID, "ppp send check rsp %v", header)
		if p.HandleForward(header, buf) {
		} else {
			if err := SendQClientMsg2MQ(header.Headers.ID, p.ID, p.Session, p.Addr, header.Headers.ReqID, buf); err != nil {
				logger.Errorf("ppp sendQClientMsg failed:%s", err)
			}
		}
	}
	return true
}

func (p *WsService) HandleForward(header worker.JsonHeader, buf []byte) bool {
	srv := srvm.GetSrvBySession(header.Headers.Callee)
	if srv == nil {
		return false
	}

	var msg interface{} = string(buf)
	if header.Headers.Caller == "" {
		var data map[string]interface{}
		worker.JsonUnmarshal(&data, buf)
		headers, ok := data["headers"].(map[string]interface{})
		if ok {
			headers["caller"] = p.Session
			data["headers"] = headers
		}

		msg = worker.JsonMarshal(data)
	}

	srv.ChanSendMsg(ConnCmd{
		Cmd:    ConnCmdSendMsg,
		Arg:    msg,
		IsJson: true,
	})
	return true
}

// 处理转发的来自其他组件的消息
func handleRemoteMsg(srv *WsService, msg interface{}, rspHeader *worker.JsonRspHeader, dStr string) bool {
	switch rspHeader.Headers.ID {
	case worker.JIDPubHeartBeat:
		srv.Send2Client(msg, rspHeader)
		operateTick(srv, invalidSeq, rspHeader.Headers.Callee, true)
		return true
	case worker.JIDPubHeartBeatRsp:
		srv.Send2Client(msg, rspHeader)
		return true
	}

	return false
}

// Send2Client 给客户端发送消息
func (p *WsService) Send2Client(msg interface{}, rspHeader *worker.JsonRspHeader) {
	if rspHeader != nil {
		id := rspHeader.Headers.ID
		// 是否是登录响应?
		if !p.loginOk && rspHeader.Ret == worker.CodeOK {
			if isLogined(id) {
				p.ChanSendMsg(ConnCmd{
					Cmd: ConnCmdUpdateLoginOk,
				})
			}

			if conf.App.MiruServer.FanoutSendEnable && isLoginBroadcast(id) {
				sendRefreshConnBroadcast(rspHeader.Headers.Callee)
			}
		}

		static.updateMsgIDSent(id)
	}

	p.ChanSendMsg(ConnCmd{
		Cmd:    ConnCmdSendMsg,
		Arg:    msg,
		IsJson: true,
	})
}

func (p *WsService) updateTick(start bool) {
	if start {
		if p.tickClosed {
			logger.Debug("start timer %v", p.Session)
			p.tick = time.NewTicker(time.Second * time.Duration(tickTime))
			p.tickClosed = false
		}
	} else {
		if !p.tickClosed {
			logger.Debug("stop timer %v", p.Session)
			p.tick.Stop()
			p.tickClosed = true
		}
	}
}

func SendQClientMsg2MQ(id string, seq mirulib.SEQ, sess, addr, dStr string, msg interface{}) error {
	if len(id) < 4 {
		logger.Info("SendQClientMsg2MQ------------id:%s seq:%d sess:%s is failed", id, seq, sess)
		return nil
	}
	logger.Debug("SendQClientMsg2MQ------------id:%s seq:%d sess:%s", id, seq, sess)
	msgsend := mirulib.QClientMsg{
		MqHeader: mirulib.MqHeader{
			ID: mirulib.MQIDClient,
		},
		Seq:       seq,
		Sess:      sess,
		Route:     listenMQRoute,
		RmtAddr:   addr,
		DStr:      dStr,
		ClientMsg: msg,
		RecvTime:  time.Now().Unix(), // 增加收取时间，服务器有压力的时候，开启超时丢弃策略
	}

	// decide where to push the message
	// it matches the first and break
	var match = false
	for _, v := range conf.App.MiruServer.MsgRoute {
		if id[:4] == v.Prefix {
			worker.MutiQueueSend(mirulib.MqManager, msgRoute[v.Prefix].ExchangeName, msgRoute[v.Prefix].Route, msgsend, dStr)
			//mirulib.MqManager.SendDx(msgRoute[v.Prefix].ExchangeName, msgRoute[v.Prefix].Route, msgsend, false, dStr)
			match = true
			break
		}
	}
	if !match {
		return fmt.Errorf("%d, %s, unhandled msgid:%s", seq, sess, id)
	}
	return nil
}

var validLoginRspID = []string{
	worker.JIDDevLoginRsp,
	worker.JIDCliLoginRsp,
	worker.JIDDevBurnLoginRsp,
	worker.JIDCliBurnLoginRsp,
	worker.JIDDevMiruLoginRsp,
	worker.JIDCliMiruLoginRsp,
}

func isLogined(id string) bool {
	for _, v := range validLoginRspID {
		if v == id {
			return true
		}
	}
	return false
}

var validLoginBroadcastRspID = []string{
	worker.JIDDevLoginRsp,
	worker.JIDCliLoginRsp,
	worker.JIDDevBurnLoginRsp,
	worker.JIDCliBurnLoginRsp,
	worker.JIDDevMiruLoginRsp,
	worker.JIDCliMiruLoginRsp,
}

func isLoginBroadcast(id string) bool {
	for _, v := range validLoginBroadcastRspID {
		if v == id {
			return true
		}
	}
	return false
}

func operateTick(srv *WsService, seq mirulib.SEQ, sess string, start bool) {
	if srv == nil {
		if srv = srvm.GetSrvBySeqSession(seq, sess); srv == nil {
			return
		}
	}

	srv.ChanSendMsg(ConnCmd{
		Cmd: ConnCmdOperateTick,
		Arg: start,
	})
}
