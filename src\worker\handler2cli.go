package main

import (
	"encoding/json"
	"fmt"
	"html"
	"mirulib"
	"mirulib/aws3"
	"mirulib/dp"
	"mirulib/redislib"
	"mirulib/tencent"
	"mirulib/worker"
	"sort"
	"strconv"
	"strings"
	"time"
)

const sessionTimeout = 7 * 24 * 3600
const smsTimeout = 60 * 10 // 十分钟超时，测试先为一分钟
const smsMaxCount = 3      //3 十分钟内3次，测试先为60次

func getFirstData(account, cid string) []mirulib.JDPData {
	kvsCObj := mirulib.GetKVSCam(cid)
	kvsCObj.Get()

	var firstData = []mirulib.JDPData{}
	var warnData []mirulib.JDPData
	warnData = worker.DPGet("", cid, worker.DPIDCameraWarnMsg, 0, 1, false)

	accWarnData := worker.DPGet(account, cid, worker.DPIDAccountCameraWarnMsg, 0, 1, false)
	// bodyWarnData := worker.DPGet("", cid, worker.DPIDCameraWarnBodyMsg, 0, 1, false)
	// carWarnData := worker.DPGet("", cid, worker.DPIDCameraWarnCarMsg, 0, 1, false)
	// bikeWarnData := worker.DPGet("", cid, worker.DPIDCameraWarnBikeMsg, 0, 1, false)
	//warnVideoData := mirulib.DPGet("", cid, mirulib.DPIDCameraWarnVideoMsg, 0, 1, false)

	firstData = append(firstData, warnData...)
	firstData = append(firstData, accWarnData...)
	// firstData = append(firstData, bodyWarnData...)
	// firstData = append(firstData, carWarnData...)
	// firstData = append(firstData, bikeWarnData...)
	//firstData = append(firstData, bindData...)
	//firstData = append(firstData, sdData...)
	//firstData = append(firstData, warnVideoData...)
	//if mirulib.IsRuleActByCid(mirulib.RulePushBellAndWarn, cid) {
	//bellData := worker.DPGet("", cid, worker.DPIDBellCallMsg, 0, 1, false)
	//firstData = append(firstData, bellData...)
	//}
	if len(firstData) > 0 {
		worker.SortDPData(firstData, 0, len(firstData)-1)
		firstData = firstData[:1]
	}
	return firstData
}

func getOtherRegionMsgCountData(pucObjs []mirulib.TBPhoneUserCamera) map[string]worker.MsgCountData {
	var otherRegionData = make(map[string]worker.MsgCountData)
	if !worker.OpenRegion {
		return otherRegionData
	}
	var otherRegionReq = make(map[string][]string)
	for i := range pucObjs {
		if region := worker.GetSessionRgn(pucObjs[i].Cid); region != "" && region != worker.Region {
			otherRegionReq[region] = append(otherRegionReq[region], pucObjs[i].Cid)
		}
	}
	if len(otherRegionReq) > 0 {
		channel := worker.ChanClient.CreateChannel()
		defer channel.Delete()
		for rgn, cids := range otherRegionReq {
			channel.SendWork(rgn, worker.WorkRoute, worker.RegionQClientMsg("", "",
				worker.JRegionCidMsgCount{
					JsonHeader: worker.JsonHeader{
						Headers: worker.Headers{
							ID: worker.JIDRegionMsgCount,
						},
					},
					Cids: cids,
				}))
		}
		rsps := channel.RecvFull(len(otherRegionReq), (*worker.JRegionCidMsgCountRsp)(nil))
		for _, rsp := range rsps {
			if r, ok := rsp.ClientMsg.(worker.JRegionCidMsgCountRsp); ok {
				for cid, data := range r.CountDatas {
					otherRegionData[cid] = data
				}
			}
		}
	}
	return otherRegionData
}

/*
report消息
*/
// 门铃已接听	JIDCliBellConnected
func (this *MQHandler) JIdCliBellConnected(ctx *worker.JsonWorkerContext) {
	cliBellConnectedEx(ctx, true)
}

/*
request消息
*/

// 注册	JIDCliRegister
func (this *MQHandler) JIdCliRegister(ctx *worker.JsonWorkerContext) {
	var data worker.JCliRegister
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	if !checkVKEY(data.Body.Vid, data.Body.Vkey, ctx.ClientMsg.DStr) {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidVKey)
		return
	}

	if data.Body.Account == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	if isMatch, retCode := checkAccount(data.Body.Account, data.Body.RegisterType); !isMatch {
		worker.MQSendErrorReply(ctx, retCode)
		return
	}

	kvsPuObj := mirulib.GetKVSPU(data.Body.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() == nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountAlreadyExist)
		return
	}

	if data.Body.RegisterType == mirulib.RegisterTypeCheckAccountExist {
		worker.MQSendNormalReply(ctx)
		return
	}

	kvsPuObj.Password = data.Body.Passwd
	kvsPuObj.RegisterTime = time.Now().Unix()
	kvsPuObj.CompanyVid = data.Body.Vid
	kvsPuObj.CountryCode = data.Body.CountryCode
	// 默认设置或者旧版本APP没有国家代码，需要提示未设置
	if data.Body.DefaultSet == 1 || data.Body.CountryCode == "" {
		kvsPuObj.ModifyTimes = -1
	}

	switch data.Body.RegisterType {
	case mirulib.RegisterTypePhone:
		if data.Body.Passwd == "" {
			worker.MQSendNormalReply(ctx)
			return
		}
		if checkSMSToken(ctx, data.Body.Account, data.Body.Token) != nil {
			return
		}
		kvsPuObj.RegisterType = mirulib.RegisterTypePhone
		kvsPuObj.SmsPhone = data.Body.Account
		kvsPuObj.Alias = data.Body.Account
	case mirulib.RegisterTypeEmail:
		worker.SavePhoneuserTemp(data.Body.Account, data.Body.Passwd, data.Body.Vid, data.Body.Account, data.Body.LanguageType, mirulib.MailTypeAccountActive)
		//kvsPuObj.RegisterType = mirulib.RegisterTypeEmail
		//kvsPuObj.Email = data.Body.Account
		//kvsPuObj.Alias = strings.Split(data.Body.Account, "@")[0]
		var types int = 2
		if ctx.Headers.ID == worker.JIDCliRegister {
			types = 3
		}
		worker.SendEmailEx(data.Body.Account, data.Body.LanguageType, mirulib.MailTypeAccountActive, data.Body.Vid, "", types, nil, data.Body.NotifyType, ctx.ClientMsg.DStr)
		//worker.LogPuEx(data.Body.Account, ctx.Headers.ID, fmt.Sprintf("注册[%s]", data.Body.Account))
		worker.MQSendNormalReply(ctx)
		return
	case mirulib.RegisterTypeEmailByCode:
		if data.Body.Passwd == "" {
			worker.MQSendNormalReply(ctx)
			return
		}
		if checkSMSToken(ctx, data.Body.Account, data.Body.Token) != nil {
			return
		}
		kvsPuObj.RegisterType = mirulib.RegisterTypeEmail
		kvsPuObj.Email = data.Body.Account
		kvsPuObj.Alias = data.Body.Account
	case mirulib.RegisterTypeEmailWelcome:
		if data.Body.Passwd == "" {
			worker.MQSendNormalReply(ctx)
			return
		}
		worker.SendEmailEx(data.Body.Account, data.Body.LanguageType, mirulib.MailTypeWelcome, data.Body.Vid, "", 0, nil, data.Body.NotifyType, ctx.ClientMsg.DStr)
		//worker.LogPuEx(data.Body.Account, ctx.Headers.ID, fmt.Sprintf("注册[%s]", data.Body.Account))
		kvsPuObj.RegisterType = mirulib.RegisterTypeEmail
		kvsPuObj.Email = data.Body.Account
		kvsPuObj.Alias = data.Body.Account
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidMsg)
		return
	}

	// 生成account_code的账号ID存储
	registerAccount := worker.CreateAccountID(data.Body.Account, ctx.ClientMsg.DStr)
	kvsPuObj.Account = registerAccount
	kvsPuObj.SetPk(registerAccount)
	kvsPuObj.Save(true)

	// 防错处理，注册时，清除账号下面的场景分组
	kvsPusObj := mirulib.GetKVSPUS(0)
	kvsPusObj.SetDebugStr(ctx.ClientMsg.DStr)
	var pusObjs []mirulib.TBPhoneUserScene
	mirulib.DB.QueryDx(mirulib.GPhoneUserScene, ctx.ClientMsg.DStr).Filter("account", registerAccount).All(&pusObjs, "scene_id")
	for _, obj := range pusObjs {
		kvsPusObj.SetPk(obj.SceneId)
		kvsPusObj.Delete(true)
	}

	// 创建一个默认分组
	sceneName := worker.GetMutiLanguageSceneName(data.Body.LanguageType)
	kvsPusObj.SetPk(0)
	kvsPusObj.Account = registerAccount
	kvsPusObj.SceneName = sceneName
	// kvsPusObj.Enable = 1
	// kvsPusObj.Mode = mirulib.ModeStandard
	// kvsPusObj.ImageId = int16(mirulib.EnableImageID)
	kvsPusObj.Save(true)

	// worker.LogPuEx(data.Body.Account, ctx.Headers.ID, fmt.Sprintf("注册[%s]", data.Body.Account))

	workerMsg := worker.JCliRegisterRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Url = worker.GetServerUrlByCode(kvsPuObj.CountryCode)
	workerMsg.Body.SwitchRegion = isSwitchRegion(workerMsg.Body.Url[0])
	worker.MQSendWorkMsg(ctx, workerMsg)
	// innerAddr, _, _ := mirulib.GetAiAddr(conf.App.Worker.AiAddr, mirulib.PT_JFG_NEW)
	// mirulib.CreateDeviceGroup(data.Body.Account, "", innerAddr+"/aiservice")

	// err := mirulib.UpdateAccountDayStats("register", mirulib.GetAlexaRegionString(conf.App.Worker.AlexaRegion))
	// if err != nil {
	// 	logger.Info("update register account data error: %s", err)
	// }
}

// 获取验证码 	JIDCliCodeGet
func (this *MQHandler) JIdCliCodeGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCodeGet
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	/*if ctx.Headers.ID == mirulib.MIDClientGetCodeReq {
		if !checkVKEY(data.Oem, data.Vkey, ctx.ClientMsg.DStr) {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidVKey)
			return
		}
	}*/

	checkType, isEmail := mirulib.RegisterTypePhone, false
	if data.Body.Type >= mirulib.ClientEmailTypeRegister {
		checkType, isEmail = mirulib.RegisterTypeEmail, true
	}
	if isMatch, retCode := checkAccount(data.Body.Account, checkType); !isMatch {
		worker.MQSendErrorReply(ctx, retCode)
		return
	}

	smsPhone := data.Body.Account
	email := data.Body.Account
	kvsPuObj := mirulib.GetKVSPU(data.Body.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	err := kvsPuObj.Get()
	switch data.Body.Type {
	case mirulib.ClientSmsTypeRegister,
		mirulib.ClientEmailTypeRegister:
		if err == nil {
			worker.MQSendErrorReply(ctx, worker.ErrorAccountAlreadyExist)
			return
		}
	case mirulib.ClientSmsTypeForgetPass:
		if err != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidPhoneNumber)
			return
		}
		smsPhone = kvsPuObj.SmsPhone
	case mirulib.ClientSmsTypeEditUserInfo:
		if err == nil {
			// if kvsPuObj.Account != smsPhone || kvsSObj.Account != smsPhone {
			// 	worker.MQSendErrorReply(ctx, worker.ErrorPhoneExist)
			// 	return
			// }
			kvsSObj := mirulib.GetKVSSession(ctx.Headers.Caller)
			kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
			if err := kvsSObj.Get(); err != nil {
				logger.Errorf("session:%s, err:%s timeout", ctx.Headers.Caller, err)
				worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
				return
			}
			// 需要设置的邮箱对应的账号，跟通过缓存查到的账号不一致，则失败
			if kvsPuObj.Account != kvsSObj.Account {
				worker.MQSendErrorReply(ctx, worker.ErrorPhoneExist)
				return
			}

			if kvsPuObj.SmsPhone != "" && kvsPuObj.SmsPhone == smsPhone {
				worker.MQSendErrorReply(ctx, worker.ErrorSamePhone)
				return
			}
		}
	case mirulib.ClientEmailTypeForgetPass:
		if err != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorIsNotEmail)
			return
		}
		email = kvsPuObj.Email
	case mirulib.ClientEmailTypeEditUserInfo:
		if err == nil {
			// 需要设置的邮箱对应的账号，跟通过缓存查到的账号不一致，则失败
			kvsSObj := mirulib.GetKVSSession(ctx.Headers.Caller)
			kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
			if err := kvsSObj.Get(); err != nil {
				logger.Errorf("session:%s, err:%s timeout", ctx.Headers.Caller, err)
				worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
				return
			}
			if kvsPuObj.Account != kvsSObj.Account {
				worker.MQSendErrorReply(ctx, worker.ErrorEmailExist)
				return
			}

			if kvsPuObj.Email != "" && kvsPuObj.Email == email {
				worker.MQSendErrorReply(ctx, worker.ErrorSameEmail)
				return
			}
		}
	case mirulib.ClientSmsTypeOpenLoginBindPhone,
		mirulib.ClientEmailTypeOpenLoginBindEmail:
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	kvsSmsCountObj := mirulib.GetKVSSmsCount(data.Body.Account)
	kvsSmsCountObj.SetDebugStr(ctx.ClientMsg.DStr)
	e := kvsSmsCountObj.Get()
	if e != nil {
		kvsSmsCountObj.Count = 1
		kvsSmsCountObj.Time = time.Now().Unix()
		kvsSmsCountObj.Save()
	} else {
		if time.Now().Unix()-kvsSmsCountObj.Time < smsTimeout {
			if kvsSmsCountObj.Count == smsMaxCount {
				worker.MQSendErrorReply(ctx, worker.ErrorGetCodeTooFrequent)
				return
			} else {
				kvsSmsCountObj.Count += 1
				kvsSmsCountObj.Save()
			}
		} else {
			kvsSmsCountObj.Count = 1
			kvsSmsCountObj.Time = time.Now().Unix()
			kvsSmsCountObj.Save()
		}
	}
	// 不传或者0获取6位验证码，4-8有效
	if data.Body.CodeLen < 4 || data.Body.CodeLen > 8 {
		data.Body.CodeLen = 6
	}
	code := mirulib.GetRandomStringEx(mirulib.RandomCodeEx, data.Body.CodeLen)
	smsToken := mirulib.GetRandomString(mirulib.RandomSignature)
	kvsSmsObj := mirulib.GetKVSSmsEx(data.Body.Account, isEmail)
	kvsSmsObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsSmsObj.Get() == nil {
		if kvsSmsObj.Type == data.Body.Type {
			code = kvsSmsObj.Code
			smsToken = kvsSmsObj.Token
		} else { // 若是不是一种类型的验证码，则重新初始化
			kvsSmsObj = mirulib.GetKVSSmsEx(data.Body.Account, isEmail)
		}
	}

	var recvAccount string
	switch data.Body.Type {
	case mirulib.ClientSmsTypeRegister, mirulib.ClientSmsTypeForgetPass, mirulib.ClientSmsTypeEditUserInfo, mirulib.ClientSmsTypeOpenLoginBindPhone:
		if !sendSMS(smsPhone, data.Body.Vid, data.Body.Type, data.Body.NotifyType, code) {
			worker.MQSendErrorReply(ctx, worker.ErrorSendSmsFailed)
			return
		}
		recvAccount = worker.SecretAccount(smsPhone, worker.SecretPhone)
	case mirulib.ClientEmailTypeRegister, mirulib.ClientEmailTypeEditUserInfo, mirulib.ClientEmailTypeOpenLoginBindEmail:
		worker.SendEmailEx(email, data.Body.LanguageType, mirulib.MailTypeRegisterByCode, data.Body.Vid, "", 0, code, data.Body.NotifyType, ctx.ClientMsg.DStr)
		recvAccount = worker.SecretAccount(email, worker.SecretEmail)
	case mirulib.ClientEmailTypeForgetPass:
		worker.SendEmailEx(email, data.Body.LanguageType, mirulib.MailTypeForgetPass, data.Body.Vid, "", 0, code, data.Body.NotifyType, ctx.ClientMsg.DStr)
		recvAccount = worker.SecretAccount(email, worker.SecretEmail)
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorCodeTimeout)
		return
	}

	kvsSmsObj.Code = code
	kvsSmsObj.Token = smsToken
	kvsSmsObj.Step = 0
	kvsSmsObj.Time = time.Now().Unix()
	kvsSmsObj.Type = data.Body.Type
	kvsSmsObj.Save()

	workerMsg := worker.JCliCodeGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Token = smsToken
	workerMsg.Body.RecvAccount = recvAccount
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 验证验证码	JIDCliCodeCheck
func (this *MQHandler) JIdCliCodeCheck(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCodeCheck
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	/*if data.Id == mirulib.MIDClientCheckCodeReq {
		if !checkVKEY(data.Vid, data.Vkey, ctx.ClientMsg.DStr) {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidVKey)
			return
		}
	}*/

	_, err := checkSMS(ctx, data.Body.Account, data.Body.Code)
	if err != nil {
		return
	}

	worker.MQSendNormalReply(ctx)
}

// 忘记密码	JIDCliPasswdSet
func (this *MQHandler) JIdCliPasswdSet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliPasswdSet
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	if !checkVKEY(data.Body.Vid, data.Body.Vkey, ctx.ClientMsg.DStr) {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidVKey)
		return
	}

	kvsPuObj := mirulib.GetKVSPU(data.Body.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidPhoneNumber)
		return
	}

	//data.Body.Account = kvsPuObj.Account
	if data.Body.Passwd != "" && kvsPuObj.Password != data.Body.Passwd {
		if checkSMSToken(ctx, data.Body.Account, data.Body.Token) != nil {
			return
		}
		kvsPuObj.Password = data.Body.Passwd
		kvsPuObj.Save(false)
	} else {
		worker.MQSendErrorReply(ctx, worker.ErrorSamePass)
		return
	}

	// worker.LogPuEx(data.Body.Account, ctx.Headers.ID, fmt.Sprintf("修改密码[%s]", data.Body.Account))
	//clientSyncLogout(ctx, ctx.Headers.ID, data.Body.Account, "")
	worker.SyncCliPushRefreshEx(kvsPuObj.Account, "", worker.JIDCliLogout, true, dp.DeleteSystemMsgGap, ctx.ClientMsg.DStr)
	worker.MQSendNormalReply(ctx)
}

// 修改密码 	JIDCliPasswdChange
func (this *MQHandler) JIdCliPasswdChange(ctx *worker.JsonWorkerContext) {
	var data worker.JCliPasswdChange
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	kvsPuObj := mirulib.GetKVSPU(data.Body.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
		// 使用小程序注册的账号没有密码，然后使用微信登录的时候就不需要再验证原密码
	} else if kvsPuObj.Password != "" && kvsPuObj.Password != data.Body.Passwd {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidPass)
		return
	} else if data.Body.Passwd != "" && data.Body.Passwd == data.Body.Newpass {
		worker.MQSendErrorReply(ctx, worker.ErrorSamePass)
		return
	}

	// kvsSObj := mirulib.GetKVSSession(ctx.Headers.Caller)
	// kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
	// if kvsSObj.Get() != nil {
	// 	worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
	// 	return
	// }

	kvsPuObj.Password = data.Body.Newpass
	kvsPuObj.Save(false)

	//clientSyncLogout(ctx, ctx.Headers.ID, data.Body.Account, ctx.Headers.Caller)
	worker.SyncCliPushRefreshEx(kvsPuObj.Account, ctx.Headers.Caller, worker.JIDCliLogout, true, dp.DeleteSystemMsgGap, ctx.ClientMsg.DStr)
	worker.MQSendNormalReply(ctx)
}

// 邮箱找回密码	JIDCliForgetPasswdEmail
func (this *MQHandler) JIdCliForgetPasswdEmail(ctx *worker.JsonWorkerContext) {
	var data worker.JCliForgetPasswdEmail
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	kvsPuObj := mirulib.GetKVSPU(data.Body.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidPhoneNumber)
		return
	}

	email := data.Body.Account
	if kvsPuObj.Email != "" {
		email = kvsPuObj.Email
	}

	var types int = 2
	if ctx.Headers.ID == worker.JIDCliForgetPasswdEmail {
		types = 3
	}

	worker.SendEmailEx(email, data.Body.LanguageType, mirulib.MailTypeChangePass, data.Body.Vid, "", types, nil, data.Body.NotifyType, ctx.ClientMsg.DStr)
	workerMsg := worker.JCliForgetPasswdEmailRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Email = email
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取注销验证码	JIDCliUnregisterCodeGet
func (this *MQHandler) JIdCliUnregisterCodeGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliUnregisterCodeGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if !checkVKEY(data.Body.Vid, data.Body.Vkey, ctx.ClientMsg.DStr) {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidVKey)
		return
	}

	kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	err = kvsPuObj.Get()
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}

	var recver = -1
	if isMatch, _ := checkAccount(kvsPuObj.SmsPhone, mirulib.RegisterTypePhone); isMatch {
		recver = mirulib.RegisterTypePhone
	} else if isMatch, _ := checkAccount(kvsPuObj.Email, mirulib.RegisterTypeEmail); isMatch {
		recver = mirulib.RegisterTypeEmail
	} else {
		worker.MQSendErrorReply(ctx, worker.ErrorNotEmailOrPhone)
		return
	}

	kvsSmsCountObj := mirulib.GetKVSSmsCount(kvsSObj.Account)
	kvsSmsCountObj.SetDebugStr(ctx.ClientMsg.DStr)
	e := kvsSmsCountObj.Get()
	if e != nil {
		kvsSmsCountObj.Count = 1
		kvsSmsCountObj.Time = time.Now().Unix()
		kvsSmsCountObj.Save()
	} else {
		if time.Now().Unix()-kvsSmsCountObj.Time < smsTimeout {
			if kvsSmsCountObj.Count == smsMaxCount {
				worker.MQSendErrorReply(ctx, worker.ErrorGetCodeTooFrequent)
				return
			} else {
				kvsSmsCountObj.Count += 1
				kvsSmsCountObj.Save()
			}
		} else {
			kvsSmsCountObj.Count = 1
			kvsSmsCountObj.Time = time.Now().Unix()
			kvsSmsCountObj.Save()
		}
	}

	// 不传或者0获取6位验证码，4-8有效
	if data.Body.CodeLen < 4 || data.Body.CodeLen > 8 {
		data.Body.CodeLen = 6
	}
	code := mirulib.GetRandomStringEx(mirulib.RandomCodeEx, data.Body.CodeLen)
	smsToken := mirulib.GetRandomString(mirulib.RandomSignature)
	kvsSmsObj := mirulib.GetKVSSmsEx(kvsSObj.Account, recver == mirulib.RegisterTypeEmail)
	kvsSmsObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsSmsObj.Get() == nil {
		code = kvsSmsObj.Code
		smsToken = kvsSmsObj.Token
	}

	kvsSmsObj.Code = code
	kvsSmsObj.Token = smsToken
	kvsSmsObj.Step = 0
	kvsSmsObj.Time = time.Now().Unix()
	kvsSmsObj.Save()
	var recvAccount string
	if recver == mirulib.RegisterTypePhone {
		if !sendSMS(kvsPuObj.SmsPhone, data.Body.Vid, mirulib.ClientSmsTypeDeactivateAccount, mirulib.NotifyTypeNormal, code) {
			worker.MQSendErrorReply(ctx, worker.ErrorSendSmsFailed)
			return
		}
		recvAccount = worker.SecretAccount(kvsPuObj.SmsPhone, worker.SecretPhone)
	} else if recver == mirulib.RegisterTypeEmail {
		worker.SendEmail(kvsPuObj.Email, kvsSObj.LanguageType, mirulib.MailTypeDeactivateAccount, data.Body.Vid, kvsSObj.Account, 0, code, ctx.ClientMsg.DStr)
		recvAccount = worker.SecretAccount(kvsPuObj.Email, worker.SecretEmail)
	}

	workerMsg := worker.JCliUnregisterCodeGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Token = smsToken
	workerMsg.Body.RecvAccount = recvAccount
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 注销	JIDCliUnregister
func (this *MQHandler) JIdCliUnregister(ctx *worker.JsonWorkerContext) {
	var data worker.JCliUnregister
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}

	qs := mirulib.DB.Query(mirulib.GPhoneUserCamera).Filter("account", kvsSObj.Account)
	if qs.Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorHasDevice)
		return
	}

	if kvsPuObj.RegisterType == mirulib.RegisterTypeEmail || kvsPuObj.RegisterType == mirulib.RegisterTypePhone {
		if checkSMSToken(ctx, kvsPuObj.Account, data.Body.Token) != nil {
			return
		}
	}

	innerAddr, _, _ := mirulib.GetAiAddr(conf.App.Worker.AiAddr, mirulib.PT_JFG_NEW)
	go deactivateAccount(kvsPuObj, innerAddr+"/aiservice", conf.Base.Oss.PriMinio)

	worker.MQSendNormalReply(ctx)
	//clientSyncLogout(ctx, data.Headers.ID, kvsSObj.Account, data.Headers.Caller)
	worker.SyncCliPushRefreshEx(kvsSObj.Account, "", worker.JIDCliLogout, true, dp.DeleteSystemMsgGap, ctx.ClientMsg.DStr)
	kvsSObj.Delete()
	dp.DeleteSystemMsgGap(kvsSObj.UDID, worker.GetLoginUDIDParam(kvsSObj.Account, kvsSObj.Sessid), worker.DPIDSystemLoginUDID, 0, 0)

	worker.DPClearEx("", kvsSObj.Account, ctx.ClientMsg.DStr, true, true)
	// err = mirulib.UpdateAccountDayStats("delete", mirulib.GetAlexaRegionString(conf.App.Worker.AlexaRegion))
	// if err != nil {
	// 	logger.Info("update delete account data error: %s", err)
	// }
}

// 登出 JIDCliLogout
// JCliLogout  JsonHeader
// JCliLogoutRsp JsonRspHeader
func (this *MQHandler) JIdCliLogout(ctx *worker.JsonWorkerContext) {
	sessid := ctx.Sess
	if sessid != "" {
		kvsObj := mirulib.GetKVSSession(sessid)
		kvsObj.SetDebugStr(ctx.ClientMsg.DStr)
		kvsObj.Get()
		mirulib.DelAccountSession(kvsObj.Account, "")
		dp.DeleteSystemMsgGap(kvsObj.UDID, worker.GetLoginUDIDParam(kvsObj.Account, sessid), worker.DPIDSystemLoginUDID, 0, 0)
		kvsObj.Delete()
	}

	worker.MQSendNormalReply(ctx)
}

// 登录 JIDCliLogin
func (this *MQHandler) JIdCliLogin(ctx *worker.JsonWorkerContext) {
	var data worker.JCliLogin
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	account := strings.Replace(data.Body.Account, " ", "", -1)
	if account == "" {
		logger.Errorf("invalid param, Account:%s, Password:%s", account, data.Body.Passwd)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}
	kvsPuObj := mirulib.GetKVSPU(account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}

	// 密码为空，则可能是通过小程序注册的，需要补充密码，才能使用看家王APP登录
	if kvsPuObj.Password == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorPleaseCompletePassword)
		return
	}

	registerAccount := kvsPuObj.Account
	data.Body.Account = kvsPuObj.Account // 需要赋值，保存到缓存中是账号
	sessid := strings.Replace(data.Body.Sessid, " ", "", -1)
	oldKvsSObj := worker.GetLastSession(sessid)
	if data.Body.Passwd != "" { //密码不为空，使用账号密码登录
		if data.Body.Os == mirulib.OSAndroidPhone && data.Body.BundleID != "" && data.Body.BundleID != conf.App.Worker.AndroidToken && data.Body.Vid == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			logger.ErrorfDx(ctx.ClientMsg.DStr, "%s login Error: ANDROID Token mismatch", registerAccount)
			return
		}

		if !checkVKEYAndBundleIDDx(data.Body.Vid, data.Body.Vkey, data.Body.BundleID, ctx.ClientMsg.DStr) {
			worker.MQSendErrorReply(ctx, worker.ErrorLoginInvalidVKey)
			return
		}

		if kvsPuObj.Password != data.Body.Passwd {
			worker.MQSendErrorReply(ctx, worker.ErrorLoginInvalidPass)
			return
		}

		sessid = ""
		if oldKvsSObj != nil && oldKvsSObj.Account == registerAccount {
			if (time.Now().Unix() - oldKvsSObj.LastLoginTime) < sessionTimeout {
				sessid = oldKvsSObj.Sessid
			} else {
				oldKvsSObj.Delete()
			}
		}
		if sessid == "" {
			sessid = newSessid(registerAccount, data.Body.Vid, ctx)
			worker.SaveLoginMsg(registerAccount, data.Body.Model, data.Body.Version, data.Body.SysVersion, data.Body.UDID, mirulib.IOT_VIDEO_CYLAN)
		}
		addLoginSession(ctx, data, nil, sessid, true, mirulib.IOT_VIDEO_CYLAN)
	} else if sessid != "" { // 使用sessid重登
		if oldKvsSObj == nil || (time.Now().Unix()-oldKvsSObj.LastLoginTime) > sessionTimeout {
			logger.Errorf("session timeout:%s", sessid)
			worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
			return
		}
		if oldKvsSObj.Account != registerAccount {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		addLoginSession(ctx, data, nil, sessid, true, mirulib.IOT_VIDEO_CYLAN)
	} else {
		logger.Errorf("invalid param, Account:%s, Password:%s sessid:%s", account, data.Body.Passwd, sessid)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	worker.IosClearSession(data.Body.Os, registerAccount, data.Body.DeviceToken, sessid, ctx.ClientMsg.DStr)
	go updateAccountLastLoginTime(registerAccount)

	ctx.Headers.Caller = sessid
	//data.JsonHeader.Caller = sessid
	ctx.UpdateSess = true
	ctx.Net = data.Body.Net

	workerMsg := worker.JCliLoginRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body.Account = registerAccount
	workerMsg.Body.Sessid = sessid
	workerMsg.Body.CountryCode = kvsPuObj.CountryCode
	workerMsg.Body.Url = worker.GetServerUrlByCode(kvsPuObj.CountryCode)
	workerMsg.Body.SwitchRegion = isSwitchRegion(workerMsg.Body.Url[0])
	workerMsg.Body.HasPasswd = kvsPuObj.Password != ""
	worker.MQSendWorkMsg(ctx, workerMsg)

	// 该账号有绑定的设备，并且进行了区域切换，则触发账号下面所有的跟账号不在一个区域的设备进行数据迁移
	saveRegion := true // 若是还有没有迁移完成的，账号的区域不能设置为本区域，否则不能触发迁移
	if worker.OpenRegion && !workerMsg.Body.SwitchRegion && kvsPuObj.Region != "" && worker.Region != kvsPuObj.Region {
		for _, obj := range worker.PucGetAdminCids(registerAccount, mirulib.PhoneUserCameraTypeAdmin, ctx.ClientMsg.DStr) {
			sendCidRegionMoveReq(nil, obj.Cid, registerAccount, &saveRegion, ctx.ClientMsg.DStr)
		}
	}

	if saveRegion && kvsPuObj.Region != worker.Region {
		kvsPuObj.Region = worker.Region
		kvsPuObj.Save(false)
	}

	// if ctx.Headers.ID < mirulib.MIDDefBegin {
	// 	clientPushOSSConfig(&conf.Base.Oss, sessid, ctx.Seq, ctx.Route, ctx.ClientMsg.DStr)
	// }
	// sendSystemMessage(kvsSObj)
}

// 登录 JIdCliMiruLogin
func (this *MQHandler) JIdCliMiruLogin(ctx *worker.JsonWorkerContext) {
	var data worker.JCliLogin
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	account := strings.Replace(data.Body.Account, " ", "", -1)
	if account == "" {
		logger.Errorf("invalid param, Account:%s, Password:%s", account, data.Body.Passwd)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}
	kvsPuObj := mirulib.GetKVSPU(account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}

	registerAccount := kvsPuObj.Account
	data.Body.Account = kvsPuObj.Account // 需要赋值，保存到缓存中是账号
	sessid := strings.Replace(data.Body.Sessid, " ", "", -1)
	oldKvsSObj := worker.GetLastSession(sessid)
	oldSessID := mirulib.GetAccountSession(registerAccount, "")
	if oldSessID != "" && oldSessID != sessid {
		worker.MQSendErrorReply(ctx, worker.ErrorLoginDenied)
		return
	}
	if data.Body.Passwd != "" { //密码不为空，使用账号密码登录

		if kvsPuObj.Password != data.Body.Passwd {
			worker.MQSendErrorReply(ctx, worker.ErrorLoginInvalidPass)
			return
		}

		sessid = ""
		if oldKvsSObj != nil && oldKvsSObj.Account == registerAccount {
			if (time.Now().Unix() - oldKvsSObj.LastLoginTime) < sessionTimeout {
				sessid = oldKvsSObj.Sessid
			} else {
				oldKvsSObj.Delete()
			}
		}
		if sessid == "" {
			sessid = newSessid(registerAccount, data.Body.Vid, ctx)
			worker.SaveLoginMsg(registerAccount, data.Body.Model, data.Body.Version, data.Body.SysVersion, data.Body.UDID, mirulib.IOT_VIDEO_CYLAN)
		}
		addLoginSessionMiru(ctx, data, nil, sessid, true, mirulib.IOT_VIDEO_CYLAN, kvsPuObj.AccountType)
		if kvsPuObj.AccountType == 1 {
			var dbMOLObj mirulib.TBMiruOperateLog
			dbMOLObj.Account = registerAccount
			dbMOLObj.CreateTime = time.Now().Unix()
			contentMap := map[string]string{
				"account": registerAccount,
				"alias":   kvsPuObj.Alias,
			}
			bs, _ := json.Marshal(&contentMap)
			dbMOLObj.OperateContent = string(bs)
			dbMOLObj.OperateType = 1
			mirulib.DB.Insert(&dbMOLObj)
		}
	} else if sessid != "" { // 使用sessid重登
		// if oldKvsSObj == nil || (time.Now().Unix()-oldKvsSObj.LastLoginTime) > sessionTimeout {
		// 	logger.Errorf("session timeout:%s", sessid)
		// 	worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
		// 	return
		// }
		// if oldKvsSObj.Account != registerAccount {
		// 	worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		// 	return
		// }

		addLoginSessionMiru(ctx, data, nil, sessid, true, mirulib.IOT_VIDEO_CYLAN, kvsPuObj.AccountType)
	} else {
		logger.Errorf("invalid param, Account:%s, Password:%s sessid:%s", account, data.Body.Passwd, sessid)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	//worker.IosClearSession(data.Body.Os, registerAccount, data.Body.DeviceToken, sessid, ctx.ClientMsg.DStr)
	go updateAccountLastLoginTime(registerAccount)

	ctx.Headers.Caller = sessid
	//data.JsonHeader.Caller = sessid
	ctx.UpdateSess = true
	ctx.UpdateHeartBeat = true
	ctx.Net = data.Body.Net

	workerMsg := worker.JCliMiruLoginRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body.Account = registerAccount
	workerMsg.Body.Sessid = sessid
	workerMsg.Body.CountryCode = kvsPuObj.CountryCode
	workerMsg.Body.Url = worker.GetServerUrlByCode(kvsPuObj.CountryCode)
	workerMsg.Body.SwitchRegion = isSwitchRegion(workerMsg.Body.Url[0])
	workerMsg.Body.HasPasswd = kvsPuObj.Password != ""
	workerMsg.Body.AccountType = kvsPuObj.AccountType
	workerMsg.Body.Alias = kvsPuObj.Alias
	workerMsg.Body.SrsHttp = worker.GetCircleStrings(&srsHttpSeed, 5, conf.Base.Srs.Http)
	worker.MQSendWorkMsg(ctx, workerMsg)

	// 该账号有绑定的设备，并且进行了区域切换，则触发账号下面所有的跟账号不在一个区域的设备进行数据迁移
	// saveRegion := true // 若是还有没有迁移完成的，账号的区域不能设置为本区域，否则不能触发迁移
	// if worker.OpenRegion && !workerMsg.Body.SwitchRegion && kvsPuObj.Region != "" && worker.Region != kvsPuObj.Region {
	// 	for _, obj := range worker.PucGetAdminCids(registerAccount, mirulib.PhoneUserCameraTypeAdmin, ctx.ClientMsg.DStr) {
	// 		sendCidRegionMoveReq(nil, obj.Cid, registerAccount, &saveRegion, ctx.ClientMsg.DStr)
	// 	}
	// }

	// if saveRegion && kvsPuObj.Region != worker.Region {
	// 	kvsPuObj.Region = worker.Region
	// 	kvsPuObj.Save(false)
	// }

	// if ctx.Headers.ID < mirulib.MIDDefBegin {
	// 	clientPushOSSConfig(&conf.Base.Oss, sessid, ctx.Seq, ctx.Route, ctx.ClientMsg.DStr)
	// }
	// sendSystemMessage(kvsSObj)
}

// 第三方登录检查 JIDCliOpenCheck
func (this *MQHandler) JIdCliOpenCheck(ctx *worker.JsonWorkerContext) {
	var data worker.JCliOpenCheck
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	workerMsg := worker.JCliOpenCheckRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	switch data.Body.RegisterType {
	case mirulib.RegisterTypeWeixin:
		if data.Body.JsCode == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		rspToken, err := tencent.Weixin.GetUserAccessToken(data.Body.JsCode)
		if err != nil {
			logger.InfoDx(ctx.ClientMsg.DStr, "weixin js code err:%v rsp:%v", err, rspToken)
			worker.MQSendErrorReply(ctx, worker.ErrorTencentInvalidJsCode)
			return
		}

		weixinOpenID, unionID := rspToken.OpenID, rspToken.UnionID
		workerMsg.Body.OpenID = weixinOpenID
		workerMsg.Body.UnionID = unionID
		// 1、这里使用unionid来查询关联关系，若是小程序先跟手机号关联起来了，就直接使用此账号登录了
		var puObj mirulib.TBPhoneUser
		query := mirulib.DB.Query(mirulib.GPhoneUser).Filter("union_open_id", unionID)
		if query.First(&puObj) == nil {
			workerMsg.Body.IsRegistered = true
			workerMsg.Body.Account = puObj.Account
			workerMsg.Body.Phone = puObj.SmsPhone
			workerMsg.Body.Email = puObj.Email
			workerMsg.Body.IsCompleted = (puObj.SmsPhone != "" || puObj.Email != "")
			worker.MQSendWorkMsg(ctx, workerMsg)
			if puObj.WeixinOpenID == "" {
				query.Update("weixin_open_id", weixinOpenID)
				mirulib.GetKVSPU(puObj.Account).Delete(false)
			}
			return
		}
		mirulib.SetOpenLoginInfo(weixinOpenID, rspToken.AccessToken, ctx.ClientMsg.DStr)
	case mirulib.RegisterTypeApple:
		if data.Body.OpenID == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		var puObj mirulib.TBPhoneUser
		query := mirulib.DB.Query(mirulib.GPhoneUser).Filter("apple_id", data.Body.OpenID)
		if query.First(&puObj) == nil {
			workerMsg.Body.IsRegistered = true
			workerMsg.Body.Account = puObj.Account
			workerMsg.Body.Phone = puObj.SmsPhone
			workerMsg.Body.Email = puObj.Email
			workerMsg.Body.IsCompleted = (puObj.SmsPhone != "" || puObj.Email != "")
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}
	// case mirulib.RegisterTypeFacebook:
	// case mirulib.RegisterTypeTwitter:
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorOpenLoginTypeNotSupport)
		logger.InfoDx(ctx.ClientMsg.DStr, "unknown register type: %d", data.Body.RegisterType)
		return
	}

	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 第三方登录 JIDCliOpenLogin
func (this *MQHandler) JIdCliOpenLogin(ctx *worker.JsonWorkerContext) {
	var data worker.JCliLogin
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	if data.Body.Os == mirulib.OSAndroidPhone && data.Body.BundleID != "" && data.Body.BundleID != conf.App.Worker.AndroidToken && data.Body.Vid == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		logger.ErrorfDx(ctx.ClientMsg.DStr, "%s open login Error: ANDROID Token mismatch", data.Body.BundleID)
		return
	}

	if !checkVKEYAndBundleIDDx(data.Body.Vid, data.Body.Vkey, data.Body.BundleID, ctx.ClientMsg.DStr) {
		worker.MQSendErrorReply(ctx, worker.ErrorLoginInvalidVKey)
		return
	}

	registerAccount := ""
	//hasPasswd := true
	isCompleted := false
	var kvsPuObj *mirulib.KVSPhoneUser
	registerType := mirulib.RegisterTypePhone
	if strings.Contains(data.Body.Account, "@") {
		registerType = mirulib.RegisterTypeEmail
	}
	switch data.Body.RegisterType {
	case mirulib.RegisterTypeWeixin:
		if data.Body.JsCode == "" && data.Body.UnionID == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		// 1、若是code不为空，则校验一下，可以获取到unionID
		accessToken, weixinOpenID := "", ""
		var apiErr error
		if data.Body.JsCode != "" {
			rspToken, err := tencent.Weixin.GetUserAccessToken(data.Body.JsCode)
			if err == nil {
				if rspToken.UnionID != data.Body.UnionID {
					logger.InfoDx(ctx.ClientMsg.DStr, "weixin UnionID error:[%s]!=[%s]", rspToken.UnionID, data.Body.UnionID)
				}
				data.Body.UnionID = rspToken.UnionID // 以code获取到的为准
				accessToken = rspToken.AccessToken
				weixinOpenID = rspToken.OpenID
			}
			apiErr = err
		}

		if accessToken == "" {
			mirulib.GetOpenLoginInfo(weixinOpenID, &accessToken, ctx.ClientMsg.DStr)
		}

		// UnionID为空，则返回失败
		if data.Body.UnionID == "" {
			logger.InfoDx(ctx.ClientMsg.DStr, "weixin API error:%v", apiErr)
			worker.MQSendErrorReply(ctx, worker.ErrorTencentAPICall)
			return
		}

		if weixinOpenID == "" {
			weixinOpenID = data.Body.OpenID
		}
		// 2、这里使用unionid来查询关联关系，若是小程序先跟手机号关联起来了，微信第三方登录就直接使用此账号登录了，不需要再次注册
		var puObj mirulib.TBPhoneUser
		query := mirulib.DB.Query(mirulib.GPhoneUser).Filter("union_open_id", data.Body.UnionID)
		err := query.First(&puObj)
		wantInsertDB := true
		if err == nil {
			wantInsertDB = false
			//hasPasswd = puObj.Password != ""
			registerAccount = puObj.Account
			data.Body.RegisterType = puObj.RegisterType

			if puObj.WeixinOpenID == "" {
				query.Update("weixin_open_id", weixinOpenID)
				mirulib.GetKVSPU(registerAccount).Delete(false)
			}

			// 短信手机号完善了，可以直接登录，否则需要走验证码流程
			if puObj.SmsPhone != "" {
				isCompleted = true
				goto LoginOK
			}
		}

		if data.Body.Account == "" || data.Body.Passwd == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorTencentCompleteInfo)
			return
		}

		// 还要验证手机号和验证码正确才能注册
		if isMatch, retCode := checkAccount(data.Body.Account, registerType); !isMatch {
			worker.MQSendErrorReply(ctx, retCode)
			return
		}

		kvsPuObj = mirulib.GetKVSPU(data.Body.Account)
		kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		puErr := kvsPuObj.Get()
		if puErr == nil {
			wantInsertDB = false
			// 通过UnionID查询到的账号和通过登录带上来的account查到的数据对应的账号不一样。说明accout里面的手机号或者邮箱被重复使用了
			if puObj.Account != "" && kvsPuObj.Account != puObj.Account {
				worker.MQSendErrorReply(ctx, worker.ErrorPhoneExist)
				return
			}

			if kvsPuObj.Password != "" && kvsPuObj.Password != data.Body.Passwd {
				worker.MQSendErrorReply(ctx, worker.ErrorInvalidPass)
				return
			}

			kvsPuObj.Password = data.Body.Passwd
			registerAccount = kvsPuObj.Account
		}

		if data.Body.Token != "" && checkSMSToken(ctx, data.Body.Account, data.Body.Token) != nil {
			return
		}

		// 账号不存在，需要多保存这些信息， 若是用小程序注册了，需要把数据获取到kvs缓存中，不然后面保存会失败
		if puErr != nil {
			kvsPuObj = mirulib.GetKVSPU(registerAccount)
			if kvsPuObj.Get() != nil {
				// 3、注册一个unionid+account的账号
				registerAccount = worker.CreateAccountID(data.Body.Account, ctx.ClientMsg.DStr)
				nickName := data.Body.Account
				if accessToken != "" {
					rspUserInfo, err := tencent.Weixin.GetUserInfo(accessToken, registerAccount, "")
					if err == nil {
						// 同步上传头像
						if imageByte, err := httpGetImage(rspUserInfo.HeadImgURL); err == nil && len(imageByte) > 0 {
							fileName := worker.GetFileNameByDirType(registerAccount, "", "", worker.ForeverHeadPhoto, 0)
							mirulib.PutObject(fileName, imageByte, worker.RegionType, true)
						}
						nickName = rspUserInfo.Nickname
						kvsPuObj.HeadPhotoRegion = worker.RegionType
					}
				}

				kvsPuObj.RegisterType = registerType
				kvsPuObj.RegisterTime = time.Now().Unix()
				kvsPuObj.CompanyVid = data.Body.Vid
				kvsPuObj.CountryCode = WeixinCountryCode
				kvsPuObj.Alias = nickName
			} else {
				wantInsertDB = false
			}
		}

		if kvsPuObj.SmsPhone == "" && data.Body.Account != "" {
			kvsPuObj.SmsPhone = data.Body.Account
		}
		if kvsPuObj.Password == "" && data.Body.Passwd != "" {
			kvsPuObj.Password = data.Body.Passwd
		}
		kvsPuObj.WeixinOpenID = weixinOpenID
		kvsPuObj.UnionOpenID = data.Body.UnionID
		kvsPuObj.Account = registerAccount
		kvsPuObj.SetPk(registerAccount)
		kvsPuObj.Save(wantInsertDB) // 不存在账号，才需要插入
	case mirulib.RegisterTypeApple:
		if data.Body.OpenID == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		var puObj mirulib.TBPhoneUser
		query := mirulib.DB.Query(mirulib.GPhoneUser).Filter("apple_id", data.Body.OpenID)
		err := query.First(&puObj)
		if err == nil {
			//hasPasswd = puObj.Password != ""
			registerAccount = puObj.Account
			data.Body.RegisterType = puObj.RegisterType
			// 短信手机号完善了，可以直接登录，否则需要走验证码流程
			if puObj.SmsPhone != "" || puObj.Email != "" {
				isCompleted = true
			}
			goto LoginOK
		}

		if data.Body.Account == "" || data.Body.Passwd == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorTencentCompleteInfo)
			return
		}

		// 还要验证手机号和验证码正确才能注册
		if isMatch, retCode := checkAccount(data.Body.Account, registerType); !isMatch {
			worker.MQSendErrorReply(ctx, retCode)
			return
		}

		kvsPuObj = mirulib.GetKVSPU(data.Body.Account)
		kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsPuObj.Get() == nil {
			worker.MQSendErrorReply(ctx, worker.ErrorAccountAlreadyExist)
			return
		}

		if checkSMSToken(ctx, data.Body.Account, data.Body.Token) != nil {
			return
		}

		kvsPuObj.RegisterType = registerType
		if registerType == mirulib.RegisterTypePhone {
			kvsPuObj.SmsPhone = data.Body.Account
		} else {
			kvsPuObj.Email = data.Body.Account
		}

		// 3、注册一个unionid+account的账号
		kvsPuObj.Password = data.Body.Passwd
		kvsPuObj.RegisterTime = time.Now().Unix()
		kvsPuObj.CompanyVid = data.Body.Vid
		kvsPuObj.CountryCode = data.Body.CountryCode
		kvsPuObj.RegisterType = registerType
		kvsPuObj.Alias = data.Body.Account
		kvsPuObj.AppleID = data.Body.OpenID

		// 生成account_code的账号ID存储
		registerAccount = worker.CreateAccountID(data.Body.Account, ctx.ClientMsg.DStr)
		kvsPuObj.Account = registerAccount
		kvsPuObj.SetPk(registerAccount)
		kvsPuObj.Save(true)
	// case mirulib.RegisterTypeFacebook:
	// case mirulib.RegisterTypeTwitter:
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorOpenLoginTypeNotSupport)
		logger.InfoDx(ctx.ClientMsg.DStr, "unknown register type: %d", data.Body.RegisterType)
		return
	}

	// 创建一个默认分组
	{
		kvsPusObj := mirulib.GetKVSPUS(0)
		kvsPusObj.SetPk(0)
		kvsPusObj.Account = registerAccount
		kvsPusObj.SceneName = worker.GetMutiLanguageSceneName(data.Body.LanguageType)
		kvsPusObj.Save(true)
	}

LoginOK:
	data.Body.Account = registerAccount
	oldKvsSObj := worker.GetLastSession(data.Body.Sessid)
	var sessid string
	if oldKvsSObj != nil && oldKvsSObj.Account == registerAccount {
		if (time.Now().Unix() - oldKvsSObj.LastLoginTime) < sessionTimeout {
			worker.IosClearSession(data.Body.Os, registerAccount, data.Body.DeviceToken, oldKvsSObj.Sessid, ctx.ClientMsg.DStr)
			sessid = oldKvsSObj.Sessid
			addLoginSession(ctx, data, oldKvsSObj, sessid, true, mirulib.IOT_VIDEO_CYLAN)
		} else {
			oldKvsSObj.Delete()
			worker.IosClearSession(data.Body.Os, registerAccount, data.Body.DeviceToken, "", ctx.ClientMsg.DStr)
			sessid = newSessid(registerAccount, data.Body.Vid, ctx)
			addLoginSession(ctx, data, oldKvsSObj, sessid, true, mirulib.IOT_VIDEO_CYLAN)
		}
	} else {
		worker.IosClearSession(data.Body.Os, registerAccount, data.Body.DeviceToken, "", ctx.ClientMsg.DStr)
		sessid = newSessid(registerAccount, data.Body.Vid, ctx)
		addLoginSession(ctx, data, oldKvsSObj, sessid, true, mirulib.IOT_VIDEO_CYLAN)
	}

	go updateAccountLastLoginTime(registerAccount)

	ctx.Headers.Caller = sessid
	//data.JsonHeader.Caller = sessid
	ctx.UpdateSess = true
	ctx.UpdateLoginOK = true
	ctx.Net = data.Body.Net

	workerMsg := worker.JCliOpenLoginRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body.RegisterType = data.Body.RegisterType
	workerMsg.Body.Account = registerAccount
	workerMsg.Body.Sessid = sessid
	workerMsg.Body.CountryCode = data.Body.CountryCode
	workerMsg.Body.Url = worker.GetServerUrlByCode(data.Body.CountryCode)
	workerMsg.Body.SwitchRegion = isSwitchRegion(workerMsg.Body.Url[0])
	workerMsg.Body.IsCompleted = isCompleted || (kvsPuObj != nil && (kvsPuObj.SmsPhone != "" || kvsPuObj.Email != ""))
	workerMsg.Body.HasPasswd = workerMsg.Body.IsCompleted
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取和设置地区登录服务器地址 JIDCliGetLoginAddr
func (this *MQHandler) JIdCliGetLoginAddr(ctx *worker.JsonWorkerContext) {
	var data worker.JCliGetLoginAddr
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	account := strings.Replace(data.Body.Account, " ", "", -1)
	if account == "" {
		logger.Errorf("invalid param, Account:%s, Password:%s", account, data.Body.Passwd)
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}

	kvsPuObj := mirulib.GetKVSPU(account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}
	if data.Body.Passwd != "" && kvsPuObj.Password != data.Body.Passwd {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidPass)
		return
	}

	oldUrl := worker.GetServerUrlByCode(kvsPuObj.CountryCode)
	newUrl := worker.GetServerUrlByCode(data.Body.CountryCode)
	serverUrl := oldUrl

	if kvsPuObj.CountryCode == "" { // 平台升级的第一调用，都认为是默认
		data.Body.Flag = 0
	}

	switch data.Body.Flag {
	case 0:
		// 第一次设置默认区域，若是为空，则设置。不为空时，不设置。返回当前国家代码，服务器地址即可
		if kvsPuObj.CountryCode == "" && data.Body.CountryCode != "" {
			kvsPuObj.CountryCode = data.Body.CountryCode
			kvsPuObj.ModifyTimes = -1
			kvsPuObj.Save(false)
			serverUrl = newUrl
		}
	case 1:
		// 修正未设置-1提示
		if kvsPuObj.ModifyTimes == -1 { //&& data.Body.CountryCode != kvsPuObj.CountryCode {
			kvsPuObj.ModifyTimes = 0
			kvsPuObj.Save(false)
		}
		if oldUrl[0] == newUrl[0] { // 同一个地区切换国家，允许修改。不需要同步
			if data.Body.CountryCode != kvsPuObj.CountryCode {
				kvsPuObj.CountryCode = data.Body.CountryCode
				kvsPuObj.Save(false)
			}
		} else { // 切换到不同区域服务器，只允许一次操作
			if kvsPuObj.ModifyTimes < 1 {
				kvsPuObj.CountryCode = data.Body.CountryCode
				kvsPuObj.ModifyTimes++
				kvsPuObj.Save(false)
				serverUrl = newUrl
			} else {
				worker.MQSendErrorReply(ctx, worker.ErrorExceedLimit)
				return
			}
		}
	}
	workerMsg := worker.JCliGetLoginAddrRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Url = serverUrl
	workerMsg.Body.CountryCode = kvsPuObj.CountryCode
	workerMsg.Body.ModifyTimes = kvsPuObj.ModifyTimes
	worker.MQSendWorkMsg(ctx, workerMsg)

	if serverUrl[0] != oldUrl[0] { // 同步到其他设备端，客户端
		sessArr := worker.GetSessidByAccountDx(account, mirulib.OnlineOK, ctx.ClientMsg.DStr)
		for _, obj := range sessArr {
			if ctx.Headers.Caller != obj.Sessid {
				logger.Debug("-----sync service url send to [%s]:%s", account, obj.Sessid)
				workerMsg.Headers.Callee = obj.Sessid
				worker.MQSendWorkerMsgKVS(obj, workerMsg, ctx.ClientMsg.DStr)
			}
		}

		var pucObjs []mirulib.TBPhoneUserCamera
		num, err := mirulib.DB.Query(mirulib.GPhoneUserCamera).Filter("account", account, "type", mirulib.PhoneUserCameraTypeAdmin).All(&pucObjs, "cid")
		if err != nil || num == 0 {
			return
		}

		cidMsg := worker.JDevGetLoginAddrRsp{}
		cidMsg.JsonRspHeader = worker.GetJsonRspHeaderEx(worker.JIDDevGetLoginAddrRsp, data.Headers.ReqID, worker.CodeOK)
		cidMsg.Body.Url = serverUrl
		for _, obj := range pucObjs {
			kvsSess := mirulib.GetKVSSession(obj.Cid)
			if kvsSess.Get() == nil && kvsSess.Net != mirulib.NetOffline {
				logger.Debug("-----sync service url send to cid [%s]:%s", account, obj.Cid)
				//cidMsg.Callee = obj.Cid
				worker.MQSendWorkerMsgKVS(kvsSess, cidMsg, ctx.ClientMsg.DStr)
			}
		}
	}
}

// 分享操作 JIDCliShareOperate
func (this *MQHandler) JIdCliShareOperate(ctx *worker.JsonWorkerContext) {
	var data worker.JCliShareOperate
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliShareOperateRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body = data.Body
	if data.Body.Sn == "" || data.Body.ToAccount == "" {
		workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}

	bDelButtonKvs := true
	switch data.Body.Type {
	case 0: // 增加分享
		if worker.MQCheckPermision(ctx, data.Body.Sn, kvsSObj.Account, true, false, ctx.ClientMsg.DStr) != nil {
			return
		}
		kvsPuObj := mirulib.GetKVSPU(data.Body.ToAccount)
		kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsPuObj.Get() != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorAccountNotExist
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorAccountNotExist)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		data.Body.ToAccount = kvsPuObj.Account
		if kvsSObj.Account == data.Body.ToAccount {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareToSelf
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareToSelf)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		if _, err := worker.PucGet(data.Body.ToAccount, data.Body.Sn); err == nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareAlready
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareAlready)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		shareCount, _ := mirulib.DB.QueryDx(mirulib.GPhoneUserCamera, ctx.ClientMsg.DStr).Filter("cid", data.Body.Sn, "type", mirulib.PhoneUserCameraTypeShare).Count()
		if int(shareCount) >= mirulib.MaxShareCount {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareExceedsLimit
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareExceedsLimit)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		// 是否在同一个区域
		kvsAdminPuObj := mirulib.GetKVSPU(kvsSObj.Account)
		kvsAdminPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsAdminPuObj.Get() != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorAccountNotExist
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorAccountNotExist)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		shareUrl := worker.GetServerUrlByCode(kvsPuObj.CountryCode)
		adminUrl := worker.GetServerUrlByCode(kvsAdminPuObj.CountryCode)
		if shareUrl[0] != adminUrl[0] {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareNotSameArea
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareNotSameArea)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		// 支持语音的呼叫设备，属性表判断
		if worker.DeviceSupportAIChat(data.Body.Sn, 0, kvsSObj.LanguageType) {
			if data.Body.VoiceRole > 0 {
				var voiceInfo worker.DPCameraVoiceCallDefineMsg
				if dpVal, err := worker.DPGetOne(data.Body.Sn, worker.DPIDCameraVoiceCallDefine, 0); err == nil {
					worker.JsonUnmarshal(&voiceInfo, dpVal.Value)
				}

				checkFun := func(toAccount, accOrCid string) (canSet bool, account string) {
					if data.Body.IsForce || toAccount == accOrCid || accOrCid == "" {
						return true, toAccount
					}

					return false, accOrCid
				}

				canSet := true
				switch data.Body.VoiceRole {
				case 1:
					canSet, voiceInfo.Papa = checkFun(data.Body.ToAccount, voiceInfo.Papa)
				case 2:
					canSet, voiceInfo.Mama = checkFun(data.Body.ToAccount, voiceInfo.Mama)
				case 3:
					canSet, voiceInfo.GrandPapa = checkFun(data.Body.ToAccount, voiceInfo.GrandPapa)
				case 4:
					canSet, voiceInfo.GrandMama = checkFun(data.Body.ToAccount, voiceInfo.GrandMama)
				case 5:
					canSet, voiceInfo.GrandFather = checkFun(data.Body.ToAccount, voiceInfo.GrandFather)
				case 6:
					canSet, voiceInfo.GrandMother = checkFun(data.Body.ToAccount, voiceInfo.GrandMother)
				case 7:
					canSet, voiceInfo.Son = checkFun(data.Body.ToAccount, voiceInfo.Son)
				case 8:
					canSet, voiceInfo.Daughter = checkFun(data.Body.ToAccount, voiceInfo.Daughter)
				}
				if canSet {
					val := worker.JsonMarshal(voiceInfo)
					worker.DPSave(data.Body.Sn, worker.DPIDCameraVoiceCallDefine, time.Now().Unix(), val)
					worker.PushDogDPData(data.Body.Sn, "", nil, []mirulib.JDPData{{
						ID:    worker.DPIDCameraVoiceCallDefine,
						Time:  time.Now().Unix() * 1000,
						Value: val,
					}}, "", nil)
				} else {
					workerMsg.JsonRspHeader.Ret = worker.ErrorShareVoiceRoleIsSet
					workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareVoiceRoleIsSet)
					worker.MQSendWorkMsg(ctx, workerMsg)
					return
				}
			}
		}

		worker.PucAdd(data.Body.ToAccount, data.Body.Sn, mirulib.PhoneUserCameraTypeShare, kvsSObj.Account, "", "", data.Body.Mark, mirulib.PT_JFG_NEW, data.Body.Remark, mirulib.IOT_VIDEO_CYLAN, ctx.ClientMsg.DStr)
		//mirulib.UpdateBellAES(data.Body.Sn, data.Body.ToAccount, true)

		worker.SaveShareMsgBoth(kvsSObj.Account, data.Body.Sn, "", data.Body.Sn, data.Body.ToAccount, data.Body.Mark, true, kvsSObj.IotVideo)
		worker.SyncCliPushRefresh(data.Body.ToAccount, ctx.Headers.Caller, worker.JIDCliDevList, ctx.ClientMsg.DStr)
		// 大屏设备需要通知
		if worker.IsAddressBookDev(data.Body.Sn, ctx.ClientMsg.DStr) {
			worker.SyncDogPushRefresh(data.Body.Sn, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
		}

		// 对应分享账号下面所有已绑定设备都要清空缓存，这样才可以重新关联此分享的设备
		worker.ClearAdminKvsDeviceButtonDefineMsg(data.Body.ToAccount, "", ctx.ClientMsg.DStr)
	case 1: // 取消分享
		kvsPucObj := mirulib.GetKVSPUC(data.Body.Sn, data.Body.ToAccount)
		//mirulib.UpdateBellAES(data.Body.Sn, data.Body.ToAccount, false)
		kvsPucObj.Get()
		kvsPucObj.Delete(true)

		worker.SaveShareMsgBoth(kvsSObj.Account, data.Body.Sn, "", kvsPucObj.Alias, data.Body.ToAccount, kvsPucObj.Mark, false, kvsSObj.IotVideo)
		//worker.MQSyncCidlist(ctx, data.Body.ToAccount, data.Body.Sn)
		worker.SyncCliPushRefresh(data.Body.ToAccount, "", worker.JIDCliDevList, ctx.ClientMsg.DStr)
		// 大屏设备需要通知
		if worker.IsAddressBookDev(data.Body.Sn, ctx.ClientMsg.DStr) {
			worker.SyncDogPushRefresh(data.Body.Sn, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
		}

		// 所有者账号主动删除分享，只同步到被分享的账号
		//worker.ServerPushOneDPByAccount(data.Body.ToAccount, worker.DPIDPushCidUnShare, 0, data.Body.Sn, ctx.Headers.Caller, ctx.ClientMsg.DStr)

		// content := fmt.Sprintf("DStr:[%s] 取消分享[%s]给[%s]", ctx.ClientMsg.DStr, data.Body.Sn, data.Body.ToAccount)
		// worker.LogPu(kvsSObj, "", ctx.Headers.ID, content)

		worker.UnbindShare2ClearButtonDefineMsg(data.Body.Sn, data.Body.ToAccount, ctx.ClientMsg.DStr)
		worker.UnbindShare2ClearVoiceCallDefineMsg(data.Body.Sn, data.Body.ToAccount, ctx.ClientMsg.DStr)
	default: // 修改权限 和 修改备注
		kvsPucObj := mirulib.GetKVSPUC(data.Body.Sn, data.Body.ToAccount)
		if kvsPucObj.Get() != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorCIDNotBind
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorCIDNotBind)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		if data.Body.Type&2 > 0 { // 修改权限
			// bDelButtonKvs = false
			if data.Body.Mark >= 0 && kvsPucObj.Mark != data.Body.Mark {
				kvsPucObj.Mark = data.Body.Mark
				worker.SaveShareMsgBoth(kvsSObj.Account, data.Body.Sn, "", kvsPucObj.Alias, data.Body.ToAccount, kvsPucObj.Mark, true, kvsSObj.IotVideo)
				worker.SyncCliPushRefresh(data.Body.ToAccount, "", worker.JIDCliDevList, ctx.ClientMsg.DStr)
			}
			if worker.OpenShareDeviceCall {
				switch data.Body.Mark {
				case worker.ShareMarkAll, worker.ShareMarkViewCall, worker.ShareMarkOnlyCall:
					worker.ClearAdminKvsDeviceButtonDefineMsg(data.Body.ToAccount, "", ctx.ClientMsg.DStr)
				case worker.ShareMarkOnlyView: // 取消权限，对应的关联关系也要清除
					worker.UnbindShare2ClearButtonDefineMsg(data.Body.Sn, data.Body.ToAccount, ctx.ClientMsg.DStr)
				default:
				}
			}
		}

		// 修改备注
		if data.Body.Type&4 > 0 && kvsPucObj.Remark != data.Body.Remark {
			kvsPucObj.Remark = data.Body.Remark
			// bDelButtonKvs = true // 备注是给自己看的，方便按钮关联亲戚朋友，需要清理按钮功能定义的缓存
		}

		kvsPucObj.Save(false)
	}

	worker.MQSendWorkMsg(ctx, workerMsg)
	mirulib.SetCidListTime(data.Body.ToAccount, time.Now().Unix(), ctx.ClientMsg.DStr)
	if bDelButtonKvs {
		mirulib.DelKvsDeviceButtonDefine(data.Body.Sn, ctx.ClientMsg.DStr)
	}
}

// 快捷分享 JIDCliShareQuickGet
func (this *MQHandler) JIdCliShareQuickGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliShareQuickGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliShareQuickGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Type = data.Body.Type
	switch data.Body.Type {
	case mirulib.ShareQuickDev: // APP-A 请求分享二维码
		var shareData mirulib.KVSShareDevData
		err := worker.JsonUnmarshal(&shareData, data.Body.Data)
		if err != nil || shareData.Sn == "" {
			workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
			worker.MQSendWorkMsg(ctx, workerMsg)
		}

		if worker.MQCheckPermision(ctx, shareData.Sn, kvsSObj.Account, true, false, ctx.ClientMsg.DStr) != nil {
			return
		}

		shareCount, _ := mirulib.DB.QueryDx(mirulib.GPhoneUserCamera, ctx.ClientMsg.DStr).Filter("cid", shareData.Sn, "type", mirulib.PhoneUserCameraTypeShare).Count()
		if int(shareCount) >= mirulib.MaxShareCount {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareExceedsLimit
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareExceedsLimit)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		code := mirulib.GetRandomString(mirulib.RandomShareQuickCode)
		var shareQuickData mirulib.KVSShareQuickData
		shareQuickData.Type = data.Body.Type
		shareQuickData.Sn = shareData.Sn
		shareQuickData.Account = kvsSObj.Account // 保存主账号
		shareQuickData.Mark = shareData.Mark
		shareQuickData.Remark = shareData.Remark
		mirulib.SetShareQuickInfo(code, &shareQuickData, ctx.ClientMsg.DStr)

		workerMsg.Body.Code = code
		workerMsg.Body.Expiration = time.Now().Unix() + int64(mirulib.ShareQuickExtime)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return

	case mirulib.ShareQuickAddressBook: // APP-A 请求分享二维码
		var shareData mirulib.KVSShareAddressBookData
		err := worker.JsonUnmarshal(&shareData, data.Body.Data)
		if err != nil || shareData.Sn == "" {
			workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
			worker.MQSendWorkMsg(ctx, workerMsg)
		}

		if worker.MQCheckPermision(ctx, shareData.Sn, kvsSObj.Account, true, true, ctx.ClientMsg.DStr) != nil {
			return
		}

		code := mirulib.GetRandomString(mirulib.RandomShareQuickCode)
		var shareQuickData mirulib.KVSShareQuickData
		shareQuickData.Type = data.Body.Type
		shareQuickData.Sn = shareData.Sn
		shareQuickData.Account = kvsSObj.Account // 保存主账号
		mirulib.SetShareQuickInfo(code, &shareQuickData, ctx.ClientMsg.DStr)

		workerMsg.Body.Code = code
		workerMsg.Body.Expiration = time.Now().Unix() + int64(mirulib.ShareQuickExtime)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	default:
		workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}
}

// 使用快捷分享 JIDCliShareQuickSet
func (this *MQHandler) JIdCliShareQuickSet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliShareQuickSet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliShareQuickSetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	if data.Body.Code == "" {
		workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}

	var shareQuickData mirulib.KVSShareQuickData
	if mirulib.GetShareQuickInfo(data.Body.Code, &shareQuickData, ctx.ClientMsg.DStr) != nil {
		workerMsg.JsonRspHeader.Ret = worker.ErrorShareQuickRandomTimeout
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareQuickRandomTimeout)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}

	workerMsg.Body.Type = shareQuickData.Type
	switch shareQuickData.Type {
	case mirulib.ShareQuickDev: // APP-B 请求添加分享
		// kvsSObj.Account 是被分享的账号
		if kvsSObj.Account == shareQuickData.Account {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareToSelf
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareToSelf)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		if _, err := worker.PucGet(kvsSObj.Account, shareQuickData.Sn); err == nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareAlready
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareAlready)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		shareCount, _ := mirulib.DB.QueryDx(mirulib.GPhoneUserCamera, ctx.ClientMsg.DStr).Filter("cid", shareQuickData.Sn, "type", mirulib.PhoneUserCameraTypeShare).Count()
		if int(shareCount) >= mirulib.MaxShareCount {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareExceedsLimit
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareExceedsLimit)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		// 是否在同一个区域
		kvsAdminPuObj := mirulib.GetKVSPU(shareQuickData.Account)
		kvsAdminPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsAdminPuObj.Get() != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorAccountNotExist
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorAccountNotExist)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		// 分享账号信息
		kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
		kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsPuObj.Get() != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorAccountNotExist
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorAccountNotExist)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		shareUrl := worker.GetServerUrlByCode(kvsPuObj.CountryCode)
		adminUrl := worker.GetServerUrlByCode(kvsAdminPuObj.CountryCode)
		if shareUrl[0] != adminUrl[0] {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareNotSameArea
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareNotSameArea)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		mirulib.DelShareQuickInfo(data.Body.Code, ctx.ClientMsg.DStr)
		toAccount := kvsSObj.Account
		adminAccount := shareQuickData.Account
		worker.PucAdd(toAccount, shareQuickData.Sn, mirulib.PhoneUserCameraTypeShare, adminAccount, "", "", shareQuickData.Mark, mirulib.PT_JFG_NEW, shareQuickData.Remark, kvsSObj.IotVideo, ctx.ClientMsg.DStr)

		worker.SaveShareMsgBoth(adminAccount, shareQuickData.Sn, "", shareQuickData.Sn, toAccount, shareQuickData.Mark, true, kvsSObj.IotVideo)
		worker.SyncCliPushRefresh(toAccount, ctx.Headers.Caller, worker.JIDCliDevList, ctx.ClientMsg.DStr)
		// 对应分享账号下面所有已绑定设备都要清空缓存，这样才可以重新关联此分享的设备
		worker.ClearAdminKvsDeviceButtonDefineMsg(toAccount, "", ctx.ClientMsg.DStr)

		// 大屏设备需要通知
		if worker.IsAddressBookDev(shareQuickData.Sn, ctx.ClientMsg.DStr) {
			worker.SyncDogPushRefresh(shareQuickData.Sn, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
		}

		mirulib.DelKvsDeviceButtonDefine(shareQuickData.Sn, ctx.ClientMsg.DStr)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return

	case mirulib.ShareQuickAddressBook: // APP-B 请求添加分享
		var shareData mirulib.KVSShareAddressBookData
		err := worker.JsonUnmarshal(&shareData, data.Body.Data)
		if err != nil || shareData.Sn == "" {
			workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
			worker.MQSendWorkMsg(ctx, workerMsg)
		}
		// kvsSObj.Account 是被分享的账号
		if kvsSObj.Account == shareQuickData.Account {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareToSelf
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareToSelf)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		if shareData.Sn == "" {
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		if _, err := worker.PucGet(kvsSObj.Account, shareData.Sn); err != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorCIDNotBind
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorCIDNotBind)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		if mirulib.DB.QueryDx(mirulib.GCameraCamera, ctx.ClientMsg.DStr).Filter("cid", shareData.Sn, "share_cid", shareQuickData.Sn).Exist() {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareAlready
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareAlready)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		if !worker.IsAddressBookDev(shareData.Sn, ctx.ClientMsg.DStr) {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareNoAddressBookDev
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareNoAddressBookDev)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		// 是否在同一个区域
		kvsAdminPuObj := mirulib.GetKVSPU(shareQuickData.Account)
		kvsAdminPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsAdminPuObj.Get() != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorAccountNotExist
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorAccountNotExist)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		// 分享账号信息
		kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
		kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsPuObj.Get() != nil {
			workerMsg.JsonRspHeader.Ret = worker.ErrorAccountNotExist
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorAccountNotExist)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		shareUrl := worker.GetServerUrlByCode(kvsPuObj.CountryCode)
		adminUrl := worker.GetServerUrlByCode(kvsAdminPuObj.CountryCode)
		if shareUrl[0] != adminUrl[0] {
			workerMsg.JsonRspHeader.Ret = worker.ErrorShareNotSameArea
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareNotSameArea)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		mirulib.DelShareQuickInfo(data.Body.Code, ctx.ClientMsg.DStr)
		cid, shareCid := shareData.Sn, shareQuickData.Sn
		account, shareAccount := kvsSObj.Account, shareQuickData.Account
		worker.CamCamAdd(cid, shareCid, account, shareAccount, shareData.Remark, mirulib.PT_JFG_NEW, 0, kvsSObj.IotVideo, ctx.ClientMsg.DStr)
		worker.CamCamAdd(shareCid, cid, shareAccount, account, "", mirulib.PT_JFG_NEW, 0, kvsSObj.IotVideo, ctx.ClientMsg.DStr)
		worker.SaveAddressBookShareMsgBoth(cid, shareCid, account, shareAccount, true, kvsSObj.IotVideo)

		worker.SyncCliPushRefresh(account, ctx.Headers.Caller, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
		worker.SyncCliPushRefresh(shareAccount, "", worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
		worker.SyncDogPushRefresh(cid, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
		worker.SyncDogPushRefresh(shareCid, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	default:
		workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}
}

// 检查快捷分享类型 JIDCliShareQuickCheck
func (this *MQHandler) JIdCliShareQuickCheck(ctx *worker.JsonWorkerContext) {
	var data worker.JCliShareQuickCheck
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	workerMsg := worker.JCliShareQuickCheckRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	if data.Body.Code == "" {
		workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}

	var shareQuickData mirulib.KVSShareQuickData
	if mirulib.GetShareQuickInfo(data.Body.Code, &shareQuickData, ctx.ClientMsg.DStr) != nil {
		workerMsg.JsonRspHeader.Ret = worker.ErrorShareQuickRandomTimeout
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorShareQuickRandomTimeout)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}

	workerMsg.Body.Type = shareQuickData.Type
	workerMsg.Body.Data = worker.JsonMarshal(mirulib.KVSShareDataRsp{Sn: shareQuickData.Sn})
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 分享列表 JIDCliShareList
func (this *MQHandler) JIdCliShareList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliShareList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if data.Body.Sn == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	var shareAccountInfo = make([]worker.ShareAccountInfo, 0)
	var pucObjs []mirulib.TBPhoneUserCamera
	mirulib.DB.Query(mirulib.GPhoneUserCamera).FilterEx("cid", data.Body.Sn, "type", mirulib.PhoneUserCameraTypeShare).All(&pucObjs)
	for _, obj := range pucObjs {
		shareAccountInfo = append(shareAccountInfo, worker.ShareAccountInfo{obj.Account, obj.Mark, obj.Remark})
	}

	workerMsg := worker.JCliShareListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Sn = data.Body.Sn
	workerMsg.Body.Account = kvsSObj.Account
	workerMsg.Body.ShareList = shareAccountInfo
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取反馈分类问题列表 JIDCliFeedbackQuestionList
func (this *MQHandler) JIdCliFeedbackQuestionList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliFeedbackQuestionList
	_, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	// 查询是否创建了待查询语种的分类问题，没有就返回英文的
	lang := data.Body.LanguageType
	if lang == "" {
		langType := data.Body.Language
		lang = mirulib.GetLanguageByType(langType)
	}
	if !mirulib.DB.Query(mirulib.GFeedbackQuestion).Filter("language_type", lang).Exist() {
		lang = mirulib.TLanguageEN
	}
	// 查询所有反馈分类
	var dbFTObjs []mirulib.TBFeedbackType
	mirulib.DB.Query(mirulib.GFeedbackType).Filter("language_type", lang).OrderBy("-create_time").All(&dbFTObjs)
	var datas = []worker.FeedbackType{}
	for _, dbFTObj := range dbFTObjs {
		var data worker.FeedbackType
		data.TypeID = dbFTObj.TypeID
		data.Content = dbFTObj.Content
		// 查询分类下所有反馈问题
		var dbFQObjs []mirulib.TBFeedbackQuestion
		mirulib.DB.Query(mirulib.GFeedbackQuestion).Filter("type_id", dbFTObj.TypeID,
			"language_type", lang).OrderBy("-create_time").All(&dbFQObjs)
		var ques = []worker.FeedbackQuestion{}
		for _, dbFQObj := range dbFQObjs {
			var q worker.FeedbackQuestion
			q.QuestionID = dbFQObj.QuestionID
			q.Content = dbFQObj.Content
			ques = append(ques, q)
		}
		data.Question = ques
		datas = append(datas, data)
	}

	workerMsg := worker.JCliFeedbackQuestionListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body = datas
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 上传用户反馈 JIDCliFeedbackPut
func (this *MQHandler) JIdCliFeedbackPut(ctx *worker.JsonWorkerContext) {
	var data worker.JCliFeedbackPut
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	isLog := 0
	if data.Body.IsLog {
		isLog = 1
		logName := worker.GetFileNameByDirType(kvsSObj.Account, "", kvsSObj.Sessid, worker.ForeverAPPlog, data.Body.Time)
		data.Body.Photos = append(data.Body.Photos, logName)
	}

	if data.Body.RegionType <= 0 {
		data.Body.RegionType = mirulib.GetRegionTypeByRoute(kvsSObj.Route)
	}
	var questionID string = data.Body.QuestionID
	if data.Body.QuestionID == "" {
		var dbFObj mirulib.TBFeedback
		mirulib.DB.Query(mirulib.GFeedback).Filter("account", kvsSObj.Account, "feedback_type", 1).First(&dbFObj)
		questionID = dbFObj.QuestionID
	}
	dbObj := mirulib.TBFeedback{
		Account:    kvsSObj.Account,
		QuestionID: questionID,
		Os:         kvsSObj.Os,
		Version:    kvsSObj.Version,
		Content:    html.EscapeString(data.Body.Content),
		Time:       time.Now().Unix(),
		Status:     mirulib.FeedbackStatusNo,
		IsLog:      isLog,
		Photos:     strings.Join(data.Body.Photos, ","),
		Model:      kvsSObj.Model,
		SysVersion: kvsSObj.SysVersion,
		Sessid:     kvsSObj.Sessid,
		RegionType: data.Body.RegionType,
		Type:       1,
	}
	mirulib.DB.InsertDx(&dbObj, ctx.ClientMsg.DStr)
	mirulib.DB.Query(mirulib.GFeedback).Filter("account", kvsSObj.Account, "feedback_type", 1).Update("status", mirulib.FeedbackStatusNo)

	worker.MQSendNormalReply(ctx)
}

// 下面为结构体排序
type FeedbackDatas []worker.FeedbackData

func (d FeedbackDatas) Len() int      { return len(d) }
func (d FeedbackDatas) Swap(i, j int) { d[i], d[j] = d[j], d[i] }

func (d FeedbackDatas) Less(i, j int) bool {

	if d[i].Time < d[j].Time {
		return true
	}
	return false
}

// 用户反馈列表 JIDCliFeedbackList
func (this *MQHandler) JIdCliFeedbackList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliFeedbackList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}
	var limit = 20
	if data.Body.Limit > 0 && data.Body.Limit <= 1000 {
		limit = data.Body.Limit
	}
	var dbObjs []mirulib.TBFeedback
	qs := mirulib.DB.QueryDx(mirulib.GFeedback, ctx.ClientMsg.DStr).Filter("account", kvsSObj.Account)
	if data.Body.Timestamp != 0 {
		qs.Filter("feedback_time__lt", data.Body.Timestamp)
	}
	qs.Limit(limit).OrderBy("-feedback_time").All(&dbObjs)
	feedbackList := []worker.FeedbackData{}
	for _, dbObj := range dbObjs {
		var feedback worker.FeedbackData
		feedback.Time = dbObj.Time
		feedback.Content = dbObj.Content
		if strings.Contains(dbObj.Photos, "zip") {
			arr := strings.Split(dbObj.Photos, ",")
			if len(arr) >= 1 {
				dbObj.Photos = strings.Join(arr[:len(arr)-1], ",")
			}
		}
		feedback.Images = dbObj.Photos
		feedback.RegionType = dbObj.RegionType
		feedback.Type = dbObj.Type
		feedbackList = append(feedbackList, feedback)
	}

	sort.Sort(FeedbackDatas(feedbackList))
	workerMsg := worker.JCliFeedbackListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body = feedbackList
	worker.MQSendWorkMsg(ctx, workerMsg)
	mirulib.DB.Query(mirulib.GFeedback).Filter("account", kvsSObj.Account, "feedback_type", 2, "status", 3).Update("status", 4)
}

// 添加组 JIDCliDevGroupAdd
func (this *MQHandler) JIdCliDevGroupAdd(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevGroupAdd
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if mirulib.DB.Query(mirulib.GPhoneUserScene).Filter("account", kvsSObj.Account, "scene_name", data.Body.Alias).Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorNameExist)
		return
	}

	kvsPusObj := mirulib.GetKVSPUS(0)
	kvsPusObj.Account = kvsSObj.Account
	kvsPusObj.SceneName = data.Body.Alias
	kvsPusObj.Save(true)

	worker.MQSendNormalReply(ctx)
}

// 删除组 JIDCliDevGroupDel
func (this *MQHandler) JIdCliDevGroupDel(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevGroupDel
	if _, err := worker.JsonUnmarshalHandleEx(&data, ctx, true); err != nil {
		return
	}

	kvsPusObj := mirulib.GetKVSPUS(data.Body.GroupID)
	if err := kvsPusObj.Get(); err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorRecordNotExist)
		return
	}

	if mirulib.DB.Query(mirulib.GPhoneUserCamera).Filter("scene_id", data.Body.GroupID).Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorHaveDevice)
		return
	}
	kvsPusObj.Delete(true)

	worker.MQSendNormalReply(ctx)
}

// 编辑组 JIDCliDevGroupEdit
func (this *MQHandler) JIdCliDevGroupEdit(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevGroupEdit
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	kvsPusObj := mirulib.GetKVSPUS(data.Body.GroupID)
	if err := kvsPusObj.Get(); err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorRecordNotExist)
		logger.Info("edit scene error")
		return
	}

	if data.Body.Alias != "" {
		if data.Body.Alias == kvsPusObj.SceneName {
			worker.MQSendNormalReply(ctx)
			return
		}

		if mirulib.DB.Query(mirulib.GPhoneUserScene).Filter("account", kvsSObj.Account, "scene_name", data.Body.Alias).Exist() {
			worker.MQSendErrorReply(ctx, worker.ErrorNameExist)
			return
		}
		kvsPusObj.SceneName = data.Body.Alias
	}

	if data.Body.SortNum > 0 {
		kvsPusObj.SortNum = data.Body.SortNum
	}
	kvsPusObj.Save(false)

	worker.MQSendNormalReply(ctx)
}

// 分组列表 JIDCliDevGroupList
func (this *MQHandler) JIdCliDevGroupList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevGroupList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliDevGroupListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body = make([]worker.DevGroupData, 0)

	switch data.Body.Type {
	case 0: //get list
	case 1:
		if len(data.Body.GroupIDs) == 0 {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		kvsPusObj := mirulib.GetKVSPUS(0)
		kvsPusObj.SetDebugStr(ctx.ClientMsg.DStr)
		for i, id := range data.Body.GroupIDs {
			kvsPusObj.SetPk(id)
			kvsPusObj.Get()
			if kvsPusObj.SortNum != i+1 {
				kvsPusObj.SortNum = i + 1
				kvsPusObj.Save(false)
			}
		}
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	var pusObjs []mirulib.TBPhoneUserScene
	mirulib.DB.Query(mirulib.GPhoneUserScene).Filter("account", kvsSObj.Account).OrderBy("sort_num").All(&pusObjs)

	for _, obj := range pusObjs {
		workerMsg.Body = append(workerMsg.Body, worker.DevGroupData{GroupID: obj.SceneId, Alias: obj.SceneName, SortNum: obj.SortNum})
	}

	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 更新组 JIDCliDevUpdate
func (this *MQHandler) JIdCliDevUpdate(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevUpdate
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	cid := data.Body.Sn
	kvsCamObj := mirulib.GetKVSCam(cid)
	kvsCamObj.SetDebugStr(ctx.ClientMsg.DStr)
	if cid == "" || !kvsCamObj.Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotExist)
		return
	}

	account := data.Body.Account
	if account == "" {
		account = kvsSObj.Account
	}
	kvsPUCObj := mirulib.GetKVSPUC(cid, account)
	kvsPUCObj.SetDebugStr(ctx.ClientMsg.DStr)
	err = kvsPUCObj.Get()
	if err != nil {
		logger.InfoDx(ctx.ClientMsg.DStr, "can't find PUC account:[%s]--cid:[%s]", account, data.Body.Sn)
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotBind)
		return
	}

	// 主账号下绑定的设备才可以切换分组
	if data.Body.GroupID > 0 && account == kvsSObj.Account {
		kvsPusObj := mirulib.GetKVSPUS(data.Body.GroupID)
		kvsPusObj.SetDebugStr(ctx.ClientMsg.DStr)
		err = kvsPusObj.Get()
		if err != nil {
			logger.InfoDx(ctx.ClientMsg.DStr, "can't find PUS account:[%s]--GroupID:[%d]", account, data.Body.GroupID)
			worker.MQSendErrorReply(ctx, worker.ErrorRecordNotExist)
			return
		}

		if kvsPusObj.Account != kvsSObj.Account {
			logger.InfoDx(ctx.ClientMsg.DStr, "GroupID:[%d] is at account:[%s]", data.Body.GroupID, kvsPusObj.Account)
			worker.MQSendErrorReply(ctx, worker.ErrorPermissionDenied)
			return
		}
	}

	refresh := false
	delButtonDefineMsg := false
	if data.Body.GroupID >= 0 && kvsPUCObj.SceneId != data.Body.GroupID && account == kvsSObj.Account {
		kvsPUCObj.SceneId = data.Body.GroupID
		refresh = true
	}
	if data.Body.Alias != "" && kvsPUCObj.Alias != data.Body.Alias && account == kvsSObj.Account {
		kvsPUCObj.Alias = data.Body.Alias
		refresh = true
		delButtonDefineMsg = true
	}
	if data.Body.Remark != "" && kvsPUCObj.Remark != data.Body.Remark {
		kvsPUCObj.Remark = data.Body.Remark
		refresh = true
		delButtonDefineMsg = true
	}
	if data.Body.PicRegion > 0 && kvsPUCObj.PicRegion != data.Body.PicRegion && account == kvsSObj.Account {
		kvsPUCObj.PicRegion = data.Body.PicRegion
		refresh = true
	}

	if refresh {
		kvsPUCObj.Save(false)
		mirulib.SetCidListTime(kvsSObj.Account, time.Now().Unix(), ctx.ClientMsg.DStr)
		go worker.SyncCliPushRefresh(kvsSObj.Account, ctx.Headers.Caller, worker.JIDCliDevList, ctx.ClientMsg.DStr)
	}

	if delButtonDefineMsg {
		// mirulib.DelKvsDeviceButtonDefine(cid, ctx.ClientMsg.DStr)
		worker.ClearAdminKvsDeviceButtonDefineMsg(account, "", ctx.ClientMsg.DStr)
	}

	// if data.Body.Enable == 1 {
	// 	worker.PusEnable(kvsPUCObj.SceneId, kvsSObj.Account)
	// }

	worker.MQSendNormalReply(ctx)
}

// 添加设备 JIDCliDevAdd
func (this *MQHandler) JIdCliDevAdd(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevAdd
	data.Body.Offset = worker.UseSrvOffsetFlag
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	cid := data.Body.Sn
	// 第三方绑定执行此处逻辑
	// if mirulib.Is3rdDog(data.Body.Vid, cid) {
	// 	bindCid3rd(ctx, &data)
	// 	return
	// }

	// 查询CID是否被绑定，备用
	// if cid != "" {
	// 	kvsPucObj := mirulib.GetKVSPUC(cid, kvsSObj.Account)
	// 	// 已经在自己的账号下面了
	// 	if kvsPucObj.Get() == nil && kvsPucObj.Type == mirulib.PhoneUserCameraTypeAdmin {
	// 		worker.MQSendErrorReply(ctx, worker.ErrorCIDBindSelf)
	// 		return
	// 	}
	// }

	//CID优先，如果camera表格可以找到CID，则使用CID绑定
	canBindNow := false
	waitOnline := false
	//cid := data.Body.Callee
	kvsCamObj := mirulib.GetKVSCam(cid)
	kvsCamObj.SetDebugStr(ctx.ClientMsg.DStr)
	if cid != "" && kvsCamObj.Get() == nil {
		typ, err := mirulib.GetCidTypeForBind(cid, ctx.ClientMsg.DStr)
		if err != nil {
			// 若是上报了随机数，则需要等待设备上报随机数，没有随机数只有cid的情况，则需要等待设备上线
			if data.Body.Random == "" {
				canBindNow = true
				waitOnline = true
			}
			// 屏蔽多次绑定请求
		} else if err == nil && typ == mirulib.BindForWait {
			logger.Info("main Wait bind for cid[%s] cid is binding now!", cid)
			if kvsSObj.Os != mirulib.OSAndroidPhone {
				worker.MQSendErrorReply(ctx, worker.ErrorCIDBinding)
			}
			return
		}
	}

	kvsRandObj := mirulib.GetKVSDataCidRandom(data.Body.Random)
	kvsRandObj.Random = data.Body.Random
	kvsRandObj.BindSessid = ctx.Headers.Caller
	kvsRandObj.Mac = data.Body.Mac
	kvsRandObj.Cid = cid
	kvsRandObj.IsRebind = data.Body.IsRebind
	kvsRandObj.MutiDevice = data.Body.MutiDev
	kvsRandObj.ReqID = data.Headers.ReqID
	kvsRandObj.AppOK = true
	kvsRandObj.Timezone = data.Body.Timezone
	kvsRandObj.Offset = data.Body.Offset
	kvsRandObj.Alias = data.Body.Alias
	kvsRandObj.IotVideo = data.Body.IotVideo
	if kvsCamObj.IotVideo == mirulib.IOT_VIDEO_TENCENT {
		kvsRandObj.IotVideo = mirulib.IOT_VIDEO_TENCENT
	}
	if !canBindNow && data.Body.Random != "" {
		kvsRandObj.SetDebugStr(ctx.ClientMsg.DStr)
		err := kvsRandObj.Get() // 获取成功，则会被覆盖原先的数据
		if err != nil {         // 设备还没有上报随机数，则等待设备上报随机数
			kvsRandObj.Save()
			if kvsSObj.Os != mirulib.OSAndroidPhone {
				worker.MQSendErrorReply(ctx, worker.ErrorCIDBinding)
			}
			return
		} else if err == nil && (kvsRandObj.Cid == "" || !kvsRandObj.DevOK) {
			logger.Info("--------bind sessid:%s caller:%s", kvsRandObj.BindSessid, ctx.Headers.Caller)
			if kvsRandObj.BindSessid == ctx.Headers.Caller {
				if kvsSObj.Os != mirulib.OSAndroidPhone {
					worker.MQSendErrorReply(ctx, worker.ErrorCIDBinding)
				}
			} else if kvsRandObj.BindSessid != "" && kvsRandObj.BindSessid != ctx.Headers.Caller {
				worker.MQSendErrorReply(ctx, worker.ErrorCIDBindForOtherClient)
			}

			return
		}

		// 前面被覆盖了，重新赋值
		kvsRandObj.BindSessid = ctx.Headers.Caller
		kvsRandObj.IsRebind = data.Body.IsRebind
		kvsRandObj.MutiDevice = data.Body.MutiDev
		kvsRandObj.ReqID = data.Headers.ReqID
		kvsRandObj.AppOK = true
		kvsRandObj.Timezone = data.Body.Timezone
		kvsRandObj.Offset = data.Body.Offset
		kvsRandObj.Alias = data.Body.Alias
		kvsRandObj.IotVideo = data.Body.IotVideo
		if kvsCamObj.IotVideo == mirulib.IOT_VIDEO_TENCENT {
			kvsRandObj.IotVideo = mirulib.IOT_VIDEO_TENCENT
		}
		cid = kvsRandObj.Cid
		kvsCamObj.SetPk(cid)
		if kvsCamObj.Get() != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorCIDNotExist)
			return
		}
		canBindNow = true
	}

	if !canBindNow {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotExist)
		return
	}

	if kvsRandObj.MutiDevice == 0 {
		kvsRandObj.Delete()
	}
	kvsRandObj.Cid = cid
	//innerAddr, _, _ := mirulib.GetAiAddr(conf.App.Worker.AiAddr, mirulib.PT_JFG_NEW)
	worker.MQBindCidRandom(ctx, kvsCamObj, kvsSObj, kvsRandObj, "", conf.App.Worker.BeforeBindTime, conf.App.Worker.WaitBindTime,
		conf.App.Worker.BindIfMacConflictAndNotUsed, waitOnline, &conf.App.Worker.AlexaServer)
	// err = worker.UpdateAccountDayStats("bind", mirulib.GetAlexaRegionString(conf.App.Worker.AlexaRegion))
	// if err != nil {
	// 	logger.Info("update bind device data error: %s", err)
	// }
}

// 批量添加设备 JIDCliDevAddMuti
func (this *MQHandler) JIdCliDevAddMuti(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevAddMuti
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	lenCid := len(data.Body.Sn)
	if lenCid == 0 || lenCid != len(data.Body.Key) || lenCid != len(data.Body.Alias) {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	workerMsg := worker.JCliDevAddMutiRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)

	for i, _ := range data.Body.Sn {
		ret := worker.BindCid(data.Body.Sn[i], kvsSObj.Account, data.Body.Key[i], data.Body.Alias[i],
			"", data.Body.Timezone, mirulib.PT_JFG_NEW, mirulib.SCENE_STRATEGY_0, false, false, 0, 0, data.Body.GroupID, mirulib.IOT_VIDEO_CYLAN)
		workerMsg.Body.Sn = append(workerMsg.Body.Sn, data.Body.Sn[i])
		workerMsg.Body.Ret = append(workerMsg.Body.Ret, ret)
	}

	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 添加设备(扫码添加) JIDCliDevQrAdd
func (this *MQHandler) JIdCliDevQrAdd(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevQrAdd
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	cid := data.Body.Sn
	// 第三方绑定执行此处逻辑
	// if mirulib.Is3rdDog(data.Body.Vid, cid) {
	// 	bindCid3rd(ctx, &data)
	// 	return
	// }

	kvsCamObj := mirulib.GetKVSCam(cid)
	kvsCamObj.SetDebugStr(ctx.ClientMsg.DStr)
	if cid != "" && kvsCamObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotExist)
		return
	}

	//验证码验证
	if data.Body.Key != kvsCamObj.Code {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidCode)
		return
	}

	kvsRandObj := mirulib.GetKVSDataCidRandom("")
	kvsRandObj.BindSessid = ctx.Headers.Caller
	kvsRandObj.Mac = ""
	kvsRandObj.IsRebind = 0
	kvsRandObj.MutiDevice = 0
	// if !canBindNow && data.Body.Random != "" {
	// 	kvsRandObj.SetDebugStr(ctx.ClientMsg.DStr)
	// 	if kvsRandObj.Get() != nil || kvsRandObj.Cid == "" {
	// 		kvsRandObj.Save()
	// 		worker.MQSendErrorReply(ctx, worker.ErrorCIDBinding)
	// 		return
	// 	}
	// 	cid = kvsRandObj.Cid
	// 	kvsCamObj.SetPk(cid)
	// 	if kvsCamObj.Get() != nil {
	// 		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotExist)
	// 		return
	// 	}
	// 	canBindNow = true
	// }

	// if kvsRandObj.MutiDevice == 0 {
	// 	kvsRandObj.Delete()
	// }
	kvsRandObj.Cid = cid
	//innerAddr, _, _ := mirulib.GetAiAddr(conf.App.Worker.AiAddr, mirulib.PT_JFG_NEW)
	worker.MQBindCidRandom(ctx, kvsCamObj, kvsSObj, kvsRandObj, "", conf.App.Worker.BeforeBindTime, conf.App.Worker.WaitBindTime,
		false, false, &conf.App.Worker.AlexaServer)
	// err = worker.UpdateAccountDayStats("bind", mirulib.GetAlexaRegionString(conf.App.Worker.AlexaRegion))
	// if err != nil {
	// 	logger.Info("update bind device data error: %s", err)
	// }
}

// 删除设备 JIDCliDevDel
func (this *MQHandler) JIdCliDevDel(ctx *worker.JsonWorkerContext) {
	cliDevDel(ctx, true)
}

// 批量删除设备 JIDCliDevDelMuti
func (this *MQHandler) JIdCliDevDelMuti(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevDelMuti
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliDevDelMutiRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Sn = make([]string, 0)
	for _, cid := range data.Body.Sn {
		kvsPucObj := mirulib.GetKVSPUC(cid, kvsSObj.Account)
		if err != kvsPucObj.Get() {
			continue
		}

		worker.MQUnbindcidEx(ctx, kvsSObj.Account, "", cid, kvsPucObj, true, false, true)
		worker.MQCidPushLoseConn(cid)
		workerMsg.Body.Sn = append(workerMsg.Body.Sn, cid)
	}

	mirulib.SetCidListTime(kvsSObj.Account, time.Now().Unix(), ctx.ClientMsg.DStr)
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 随机数添加 JIDCliDevAddRandom
func (this *MQHandler) JIdCliDevAddRandom(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevAddRandom
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	cid := data.Body.Sn
	okFunc := func(kvsRandObj *mirulib.KVSDataCidRandom, camIotVideo int16, sendRsp bool) *mirulib.KVSDataCidRandom {
		if kvsRandObj == nil {
			kvsRandObj = mirulib.GetKVSDataCidRandom(data.Body.Random)
			kvsRandObj.Get()
		}
		kvsRandObj.BindSessid = ctx.Headers.Caller
		kvsRandObj.Mac = data.Body.Mac
		kvsRandObj.Cid = cid
		kvsRandObj.IsRebind = data.Body.IsRebind
		kvsRandObj.MutiDevice = data.Body.MutiDev
		kvsRandObj.ReqID = data.Headers.ReqID
		kvsRandObj.AppOK = true
		kvsRandObj.Timezone = data.Body.Timezone
		kvsRandObj.Offset = data.Body.Offset
		kvsRandObj.Alias = data.Body.Alias
		kvsRandObj.IotVideo = data.Body.IotVideo
		if camIotVideo == mirulib.IOT_VIDEO_TENCENT {
			kvsRandObj.IotVideo = mirulib.IOT_VIDEO_TENCENT
		}

		kvsRandObj.Save()
		if sendRsp {
			worker.MQSendErrorReply(ctx, worker.CodeOK)
		}

		return kvsRandObj
	}

	if cid == "" {
		okFunc(nil, mirulib.IOT_VIDEO_CYLAN, true)
		return
	}

	kvsCamObj := mirulib.GetKVSCam(cid)
	kvsCamObj.SetDebugStr(ctx.ClientMsg.DStr)
	if cid == "" || !kvsCamObj.Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotExist)
		return
	}

	workerMsg := worker.JCliDevAddRandomRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Sn = cid

	account := kvsSObj.Account
	var pucObj *mirulib.TBPhoneUserCamera
	pucObj, err = worker.PucGetAdminObj(cid, ctx.ClientMsg.DStr)
	idBindedAccount := pucObj.Account
	if err == nil {
		if idBindedAccount != account {
			workerMsg.Ret = worker.ErrorCIDBinded
			workerMsg.Msg = worker.GetCodeMsg(worker.ErrorCIDBinded)
			workerMsg.Body.Account = worker.GetBindedSecret(idBindedAccount)
			worker.MQSendWorkerMsgKVS(kvsSObj, workerMsg, ctx.ClientMsg.DStr)
			return
		} else {
			worker.MQSendErrorReply(ctx, worker.ErrorCIDBindSelf)
			return
		}
	}

	switch data.Body.Type {
	case 0: // App上报随机数成功后，显示wifi相关信息的二维码给设备扫
		okFunc(nil, kvsCamObj.IotVideo, true)
	case 1: // 4G设备上报随机数成功以后，显示设备相关信息的二维码给APP扫
		lockCam := redislib.Lock(redislib.RDLockCam, cid)
		typ, err := mirulib.GetCidTypeForBind(cid, ctx.ClientMsg.DStr)
		if err == nil && typ == mirulib.BindForWait {
			lockCam.Unlock()
			logger.Info("Wait bind for cid[%s] cid is binding now!", cid)
			if kvsSObj.Os != mirulib.OSAndroidPhone {
				worker.MQSendErrorReply(ctx, worker.ErrorCIDBinding)
			}
			return
		} else if err == nil && typ == mirulib.BindForCancel {
			lockCam.Unlock()
			logger.Info("Cancel bind for cid[%s] now!", cid)
			if kvsSObj.Os != mirulib.OSAndroidPhone {
				worker.MQSendErrorReply(ctx, worker.ErrorCIDBindTimeout)
			}
			return
		}

		mirulib.SetCidTypeForBind(cid, mirulib.BindForWait, mirulib.RandomBindExTime, ctx.ClientMsg.DStr)
		lockCam.Unlock()

		fromRegion := false
		kvsRandObj := mirulib.GetKVSDataCidRandom(data.Body.Random)
		err = kvsRandObj.Get()
		if worker.OpenRegion && err != nil { // 在其他区域
			channel := worker.ChanClient.CreateChannel()
			defer channel.Delete()
			for _, region := range worker.OtherRegions {
				if region == worker.Region {
					continue
				}
				worker.RegionRandomAddReq(channel, region, cid, data.Body.Random, true)
			}
			rsps := channel.RecvFull(1, (*worker.JRegionRandomAddRsp)(nil))
			logger.Debug("channel.RecvFull ,len(rsps):%d ,rsps: %+v", len(rsps), rsps)
			if len(rsps) == 1 && rsps[0].ClientMsg != nil {
				r := rsps[0].ClientMsg.(worker.JRegionRandomAddRsp)
				kvsRandObj = &r.KvsRandom
			} else {
				mirulib.DelCidTypeForBind(cid, ctx.ClientMsg.DStr)
				worker.MQSendErrorReply(ctx, worker.ErrorCIDBindTimeout)
				return
			}

			fromRegion = true
		}

		if !kvsRandObj.DevOK {
			mirulib.DelCidTypeForBind(cid, ctx.ClientMsg.DStr)
			worker.MQSendErrorReply(ctx, worker.ErrorCIDBindTimeout)
			return
		}

		okFunc(kvsRandObj, kvsCamObj.IotVideo, false)
		if kvsRandObj.MutiDevice == 0 {
			kvsRandObj.Delete()
		}
		kvsRandObj.Cid = cid
		worker.MQBindCidRandom(ctx, kvsCamObj, kvsSObj, kvsRandObj, "", conf.App.Worker.BeforeBindTime, conf.App.Worker.WaitBindTime,
			conf.App.Worker.BindIfMacConflictAndNotUsed, false, &conf.App.Worker.AlexaServer)

		// 通知设备从其他区域切过来
		if worker.OpenRegion && fromRegion {
			kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
			kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
			if kvsPuObj.Get() != nil {
				return
			}

			worker.MQCidPushLoseConnRegion(cid, worker.GetServerUrlByCode(kvsPuObj.CountryCode))
		}
	}
}

// 设备列表 JIDCliDevList
func (this *MQHandler) JIdCliDevList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	switch data.Body.Type {
	case mirulib.CidListNormal: // 兼容模式，返回所有设备列表
		data.Body.GroupID = -1
		data.Body.Page = 1
		data.Body.Num = worker.MaxCidListNum
		data.Body.Search = ""
	case mirulib.CidListPage: // 分页模式 GroupID page必传
		if data.Body.Page == 1 { // 首页的时候，场景为空
			data.Body.GroupID = -1
		} else if data.Body.Page > 1 && data.Body.GroupID <= 0 {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		data.Body.Time = 0
		if data.Body.Num < 10 {
			data.Body.Num = worker.CidListNum
		}
		data.Body.Search = ""
	case mirulib.CidListSearch: // 查询模式 search 必传
		data.Body.Time = 0
		data.Body.GroupID = -1
		data.Body.Page = 1
		data.Body.Num = worker.CidListNum
		if data.Body.Search == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	changeTime, err := mirulib.GetCidListTime(kvsSObj.Account, ctx.ClientMsg.DStr)
	if err != nil {
		changeTime = time.Now().Unix()
		mirulib.SetCidListTime(kvsSObj.Account, changeTime, ctx.ClientMsg.DStr)
	}

	if changeTime == data.Body.Time {
		logger.InfoDx(ctx.ClientMsg.DStr, "cidList is not change -- account: %s", kvsSObj.Account)
		worker.MQSendWorkMsg(ctx, *noChangeCidlist(ctx, changeTime))
		return
	}

	workerMsg := worker.Cidlist(ctx, kvsSObj.Account, kvsSObj.LanguageType, changeTime, data.Body.Type, data.Body.GroupID, data.Body.Page, data.Body.Num, data.Body.Search, true, kvsSObj.IotVideo)
	workerMsg.Body.Page = data.Body.Page
	workerMsg.Body.Num = data.Body.Num
	workerMsg.Body.Type = data.Body.Type
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 设备列表 JIDCliDevListUngroup
func (this *MQHandler) JIdCliDevListUngroup(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevListUngroup
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	changeTime, err := mirulib.GetCidListTime(kvsSObj.Account, ctx.ClientMsg.DStr)
	if err != nil {
		changeTime = time.Now().Unix()
		mirulib.SetCidListTime(kvsSObj.Account, changeTime, ctx.ClientMsg.DStr)
	}

	switch data.Body.Type {
	case mirulib.CidListNormal: // 兼容模式，返回所有设备列表
		data.Body.Page = 1
		data.Body.Num = worker.MaxCidListNum
		data.Body.Search = ""
	case mirulib.CidListPage: // 分页模式 page必传
		data.Body.Time = 0
		if data.Body.Num < 10 {
			data.Body.Num = worker.CidListNum
		}
		data.Body.Search = ""
	case mirulib.CidListSearch: // 查询模式 search 必传
		data.Body.Time = 0
		data.Body.Page = 1
		data.Body.Num = worker.CidListNum
		if data.Body.Search == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	var workerMsg *worker.JCliDevListUngroupRsp
	//workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)

	//var workerMsg *mirulib.MsgClientCidListRsp
	if changeTime == data.Body.Time {
		logger.InfoDx(ctx.ClientMsg.DStr, "cidList is not change -- account: %s", kvsSObj.Account)
		workerMsg = noChangeCidlistUngroup(ctx, changeTime)
	} else {
		workerMsg = cidlistUngroup(ctx, kvsSObj.Account, kvsSObj.LanguageType, changeTime, data.Body.Page, data.Body.Num, data.Body.Search, kvsSObj.IotVideo)
	}
	workerMsg.Body.Page = data.Body.Page
	workerMsg.Body.Num = data.Body.Num
	workerMsg.Body.Type = data.Body.Type
	workerMsg.Body.Total, _ = mirulib.DB.Query(mirulib.GPhoneUserCamera).Filter("account", kvsSObj.Account).Count()
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 按钮功能分享列表 JIDCliDevButtonDefineList
func (this *MQHandler) JIdCliDevButtonDefineList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevButtonDefineList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if data.Body.Sn == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	kvsPucObj := mirulib.GetKVSPUC(data.Body.Sn, kvsSObj.Account)
	err = kvsPucObj.Get()
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotBind)
		return
	}

	account := kvsSObj.Account
	// 分享过来的数据，要查询对应主账号的数据
	if kvsPucObj.ShareAccount != "" {
		switch kvsPucObj.Mark {
		case worker.ShareMarkAll, worker.ShareMarkViewCall, worker.ShareMarkOnlyCall:
		default:
			worker.MQSendErrorReply(ctx, worker.ErrorShareNotPermit)
			return
		}
		account = kvsPucObj.ShareAccount
	}

	workerMsg := worker.JCliDevButtonDefineListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body = worker.GetKVSButtonDefineInfo(data.Body.Sn, account, data.Body.Time, false, data.Headers.ReqID)
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 按钮功能分享列表设置 JIDCliDevButtonDefineSet
func (this *MQHandler) JIdCliDevButtonDefineSet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevButtonDefineSet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if data.Body.Sn == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	kvsPucObj := mirulib.GetKVSPUC(data.Body.Sn, kvsSObj.Account)
	err = kvsPucObj.Get()
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotBind)
		return
	}

	if kvsPucObj.ShareAccount != "" {
		switch kvsPucObj.Mark {
		case worker.ShareMarkAll, worker.ShareMarkViewCall, worker.ShareMarkOnlyCall:
		default:
			worker.MQSendErrorReply(ctx, worker.ErrorShareNotPermit)
			return
		}
	}

	// 保存到DP
	dpButtonMsg := worker.GetDpButtonDefineMsg(data.Body.Sn, data.Body, true, data.Headers.ReqID)
	worker.DPSaveButtonDefine(data.Body.Sn, dpButtonMsg)
	mirulib.DelKvsDeviceButtonDefine(data.Body.Sn, data.Headers.ReqID)
	worker.MQSendNormalReply(ctx)
}

// 语音呼叫分享列表 JIDCliDevVoiceDefineList
func (this *MQHandler) JIdCliDevVoiceDefineList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevVoiceDefineList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if data.Body.Sn == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	kvsPucObj := mirulib.GetKVSPUC(data.Body.Sn, kvsSObj.Account)
	kvsPucObj.SetDebugStr(data.Headers.ReqID)
	err = kvsPucObj.Get()
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotBind)
		return
	}

	account := kvsSObj.Account
	// 分享过来的数据，要查询对应主账号的数据
	if kvsPucObj.ShareAccount != "" {
		worker.MQSendErrorReply(ctx, worker.ErrorShareNotPermit)
		return
	}

	kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
	kvsPuObj.SetDebugStr(data.Headers.ReqID)
	err = kvsPuObj.Get()
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}

	var voiceInfo worker.DPCameraVoiceCallDefineMsg
	if dpVal, err := worker.DPGetOne(data.Body.Sn, worker.DPIDCameraVoiceCallDefine, 0); err == nil {
		worker.JsonUnmarshal(&voiceInfo, dpVal.Value)
	}
	getVoiceFlag := func(accOrCid string) worker.VoiceCallFlag {
		return worker.VoiceCallFlag{
			Papa:        accOrCid == voiceInfo.Papa,
			Mama:        accOrCid == voiceInfo.Mama,
			GrandPapa:   accOrCid == voiceInfo.GrandPapa,
			GrandMama:   accOrCid == voiceInfo.GrandMama,
			GrandFather: accOrCid == voiceInfo.GrandFather,
			GrandMother: accOrCid == voiceInfo.GrandMother,
			Son:         accOrCid == voiceInfo.Son,
			Daughter:    accOrCid == voiceInfo.Daughter,
		}
	}

	accountList, devList := make([]worker.CliVoiceInfo, 0), make([]worker.CliVoiceInfo, 0)
	accountList = append(accountList, worker.CliVoiceInfo{account, "", false, getVoiceFlag(account)})
	// 查找分享的账号
	var pucObjs []mirulib.TBPhoneUserCamera
	mirulib.DB.Query(mirulib.GPhoneUserCamera).FilterEx("cid", data.Body.Sn, "type", mirulib.PhoneUserCameraTypeShare).All(&pucObjs)
	for _, obj := range pucObjs {
		tmpPuObj := mirulib.GetKVSPU(obj.Account)
		tmpPuObj.SetDebugStr(data.Headers.ReqID)
		tmpPuObj.Get()
		accountList = append(accountList, worker.CliVoiceInfo{obj.Account, obj.Remark, true, getVoiceFlag(obj.Account)})
	}

	adminVideoCodec := worker.GetCidVideoCodec(data.Body.Sn)
	// 查找设备和分享的设备
	for _, obj := range worker.PucGetAdminCids(account, mirulib.PhoneUserCameraTypeAll, ctx.ClientMsg.DStr) {
		// 只针对看家王账号开放
		if obj.IotVideo != mirulib.IOT_VIDEO_CYLAN {
			continue
		}
		if mirulib.PhoneUserCameraTypeAdmin == obj.Type {
			// 过滤自己的SN
			if obj.Cid != data.Body.Sn && worker.IsRuleActByCidEx(worker.RuleVideoCodec, obj.Cid, int32(adminVideoCodec)) {
				devList = append(devList, worker.CliVoiceInfo{obj.Cid, obj.Alias, false, getVoiceFlag(obj.Cid)})
			}
		} else {
			// 有呼叫权限的分享设备，才能显示
			if worker.OpenShareDeviceCall {
				switch obj.Mark {
				case worker.ShareMarkAll, worker.ShareMarkViewCall, worker.ShareMarkOnlyCall:
					if worker.IsRuleActByCidEx(worker.RuleVideoCodec, obj.Cid, int32(adminVideoCodec)) {
						devList = append(devList, worker.CliVoiceInfo{obj.Cid, obj.Alias, true, getVoiceFlag(obj.Cid)})
					}
				default:
				}
			}
		}
	}

	workerMsg := worker.JCliDevVoiceDefineListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Time = time.Now().Unix()
	workerMsg.Body.Sn = data.Body.Sn
	workerMsg.Body.AccountList = accountList
	workerMsg.Body.DevList = devList
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取账号信息 JIDCliAccountInfoGet
func (this *MQHandler) JIdCliAccountInfoGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliAccountInfoGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	changeTime, err := mirulib.GetAccountTime(kvsSObj.Account, ctx.ClientMsg.DStr)
	if err != nil {
		changeTime = time.Now().Unix()
		mirulib.SetAccountTime(kvsSObj.Account, changeTime, ctx.ClientMsg.DStr)
	}

	kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorUnknown)
		return
	}

	workerMsg := worker.JCliAccountInfoGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Time = data.Body.Time
	if changeTime == data.Body.Time {
		logger.InfoDx(ctx.ClientMsg.DStr, "cidList is not change -- account: %s", kvsSObj.Account)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}

	workerMsg.Body.Version = kvsPuObj.Vid
	workerMsg.Body.RegisterTime = kvsPuObj.RegisterTime
	workerMsg.Body.RegisterType = kvsPuObj.RegisterType
	workerMsg.Body.SmsPhone = kvsPuObj.SmsPhone
	workerMsg.Body.Alias = kvsPuObj.Alias
	workerMsg.Body.PushEnable = kvsPuObj.PushEnable
	workerMsg.Body.Vibrate = kvsPuObj.Vibrate
	workerMsg.Body.Sound = kvsPuObj.Sound
	workerMsg.Body.Email = kvsPuObj.Email
	workerMsg.Body.Account = kvsPuObj.Account
	workerMsg.Body.WeixinOpenID = kvsPuObj.WeixinOpenID
	workerMsg.Body.AppleID = kvsPuObj.AppleID
	workerMsg.Body.HeadPhotoRegion = kvsPuObj.HeadPhotoRegion
	workerMsg.Body.UniqueAccount = kvsPuObj.UniqueAccount
	fileName := worker.GetFileNameByDirType(kvsSObj.Account, "", "", worker.ForeverHeadPhoto, 0)
	workerMsg.Body.HeadPhotoUrl = mirulib.GetImageSignUrl(fileName, mirulib.WeekExTime, kvsPuObj.HeadPhotoRegion, true)
	//workerMsg.Body.WeixinPushFlag = kvsPuObj.WeixinPushFlag
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 设置账号信息 JIDCliAccountInfoSet
func (this *MQHandler) JIdCliAccountInfoSet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliAccountInfoSet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPuObj.Get() != nil {
		logger.ErrorfDx(ctx.ClientMsg.DStr, "Get KVSPU not exit by %s", kvsSObj.Account)
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}

	flagList := aws3.ShiftBitEx(uint(data.Body.Flag), 16)
	// content := ""

	if flagList[0] || flagList[6] {
		checkAccount := data.Body.SmsPhone
		ret := worker.ErrorPhoneExist
		if flagList[6] {
			checkAccount = data.Body.Email
			ret = worker.ErrorEmailExist
		}

		if data.Body.Type == worker.AccountInfoTypeByPass {
			if data.Body.Passwd != kvsPuObj.Password {
				worker.MQSendErrorReply(ctx, worker.ErrorInvalidPass)
				return
			}
		} else if data.Body.Type == worker.AccountInfoTypeByCode {
			if data.Body.Passwd != "" && data.Body.Passwd != kvsPuObj.Password {
				worker.MQSendErrorReply(ctx, worker.ErrorInvalidPass)
				return
			}
			if checkSMSToken(ctx, checkAccount, data.Body.Token) != nil {
				return
			}
		} else {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		kvsCheckPuObj := mirulib.GetKVSPU(checkAccount)
		kvsCheckPuObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsCheckPuObj.Get() == nil && kvsSObj.Account != kvsCheckPuObj.Account {
			worker.MQSendErrorReply(ctx, ret)
			return
		}

		if flagList[0] {
			kvsPuObj.SmsPhone = checkAccount
		} else {
			kvsPuObj.Email = checkAccount
		}

		// if (kvsPuObj.RegisterType == mirulib.RegisterTypeCqhuiju ||
		// 	kvsPuObj.RegisterType == mirulib.RegisterTypeSinaWeibo) && (kvsPuObj.Email == "" && kvsPuObj.SmsPhone == "") {
		// 	mirulib.SetEmailOrPhone(kvsPuObj.Account, data.Body.SmsPhone, ctx.ClientMsg.DStr)
		// } else {
		// 	kvsPuObj.SmsPhone = data.Body.SmsPhone
		// }
		// content += fmt.Sprintf("短信号码[%s] ", data.Body.SmsPhone)
	}
	if flagList[2] {
		kvsPuObj.Alias = data.Body.Alias
		// content += fmt.Sprintf("昵称[%s] ", data.Body.Alias)
	}

	if flagList[3] {
		kvsPuObj.PushEnable = data.Body.PushEnable
		// content += fmt.Sprintf("消息通知[%d] ", data.Body.PushEnable)
	}

	if flagList[4] {
		kvsPuObj.Vibrate = data.Body.Vibrate
		// content += fmt.Sprintf("震动[%d] ", data.Body.Vibrate)
	}

	if flagList[5] {
		kvsPuObj.Sound = data.Body.Sound
		// content += fmt.Sprintf("声音[%d] ", data.Body.Sound)
	}

	// if flagList[1] && flagList[6] {
	// 	worker.SavePhoneuserTemp(kvsSObj.Account, "", kvsSObj.Oem, data.Body.Email, kvsSObj.LanguageType, mirulib.MailTypeChangeEmail)

	// 	if (kvsPuObj.RegisterType == mirulib.RegisterTypeCqhuiju ||
	// 		kvsPuObj.RegisterType == mirulib.RegisterTypeSinaWeibo) && (kvsPuObj.Email == "" && kvsPuObj.SmsPhone == "") {
	// 		mirulib.SetEmailOrPhone(kvsPuObj.Account, data.Body.Email, ctx.ClientMsg.DStr)
	// 	} else {
	// 		var types int = 2
	// 		if ctx.Headers.ID == worker.JIDCliAccountInfoSet {
	// 			types = 3
	// 		}
	// 		worker.SendEmail(data.Body.Email, kvsSObj.LanguageType, mirulib.MailTypeChangeEmail, kvsSObj.Oem, kvsPuObj.Account, types, nil, ctx.ClientMsg.DStr)
	// 	}

	// 	content += fmt.Sprintf("邮箱[%s] ", data.Body.Email)
	// }

	if flagList[7] {
		if data.Body.JsCode == "" {
			kvsPuObj.WeixinOpenID = ""
			if kvsPuObj.MiniOpenID == "" {
				kvsPuObj.UnionOpenID = ""
			}
		} else {
			rspToken, err := tencent.Weixin.GetUserAccessToken(data.Body.JsCode)
			if err != nil {
				logger.InfoDx(ctx.ClientMsg.DStr, "weixin js code err:%v rsp:%v", err, rspToken)
				worker.MQSendErrorReply(ctx, worker.ErrorTencentInvalidJsCode)
				return
			}

			weixinOpenID, unionID := rspToken.OpenID, rspToken.UnionID
			// 小程关联的跟微信的不是同一个账号
			if kvsPuObj.UnionOpenID != "" && unionID != kvsPuObj.UnionOpenID {
				worker.MQSendErrorReply(ctx, worker.ErrorTencentOpenID2Account)
				return
			}

			var puObj mirulib.TBPhoneUser
			err = mirulib.DB.Query(mirulib.GPhoneUser).Filter("union_open_id", unionID).First(&puObj)
			if err == nil && puObj.Account != kvsPuObj.Account {
				worker.MQSendErrorReply(ctx, worker.ErrorWeixinOpenIDIsBinded)
				return
			}

			kvsPuObj.WeixinOpenID = weixinOpenID
			kvsPuObj.UnionOpenID = unionID
			// content += fmt.Sprintf("绑定微信[%v]\n", data.Body.WeixinOpenID) //目前不适用app设置该位
		}
	}
	// if flagList[8] {
	// 	kvsPuObj.WeixinPushFlag = data.Body.WeixinPushFlag
	// 	content += fmt.Sprintf("微信推送开关[%v]\n", data.Body.WeixinPushFlag)
	// }

	if flagList[9] {
		kvsPuObj.HeadPhotoRegion = data.Body.HeadPhotoRegion
		// content += fmt.Sprintf("设置头像区域[%v]\n", data.Body.HeadPhotoRegion)
	}

	if flagList[10] {
		var puObj mirulib.TBPhoneUser
		err = mirulib.DB.Query(mirulib.GPhoneUser).Filter("apple_id", data.Body.AppleID).First(&puObj)
		if err == nil && puObj.Account != kvsPuObj.Account {
			worker.MQSendErrorReply(ctx, worker.ErrorAppleIDIsBinded)
			return
		}
		kvsPuObj.AppleID = data.Body.AppleID
	}

	if flagList[11] {
		var puObj mirulib.TBPhoneUser
		// 唯一账号还需要查询smsphone，email，account字段
		uniqueAcc := data.Body.UniqueAccount
		err = mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", uniqueAcc, "sms_phone__or", uniqueAcc, "email__or", uniqueAcc,
			"unique_account__or", uniqueAcc, "weixin_open_id__or", uniqueAcc, "mini_open_id__or", uniqueAcc, "apple_id__or", uniqueAcc).First(&puObj)
		if err == nil && puObj.Account != kvsPuObj.Account {
			worker.MQSendErrorReply(ctx, worker.ErrorUniqueAccountIsBinded)
			return
		}
		kvsPuObj.UniqueAccount = uniqueAcc
	}

	kvsPuObj.Vid += 1
	kvsPuObj.Save(false)
	// worker.LogPu(kvsSObj, ctx.Headers.Caller, ctx.Headers.ID, content)

	changeTime := time.Now().Unix()
	mirulib.SetAccountTime(kvsSObj.Account, changeTime, ctx.ClientMsg.DStr)

	worker.MQSendNormalReply(ctx)

	worker.SyncCliPushRefresh(kvsSObj.Account, ctx.Headers.Caller, worker.JIDCliAccountInfoGet, ctx.ClientMsg.DStr)
}

// 推送token设置 JIDCliPushTokenSet
func (this *MQHandler) JIdCliPushTokenSet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliPushTokenSet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	kvsSObj.DeviceToken = data.Body.Token
	if data.Body.BundleID != "" {
		kvsSObj.BundleId = data.Body.BundleID
	}
	if data.Body.ServiceType >= 0 {
		//switch len(data.Token) {
		//case 152:
		//	kvsSObj.ServiceType = mirulib.ServiceTypeAndroidXiaomi
		//case 44:
		//	kvsSObj.ServiceType = mirulib.ServiceTypeAndroidHuawei
		//case 32:
		//	kvsSObj.ServiceType = mirulib.ServiceTypeAndroidGcm
		//default:
		//	kvsSObj.ServiceType = mirulib.ServiceTypeAndroidGcm
		//}
		kvsSObj.ServiceType = data.Body.ServiceType
		logger.InfoDx(ctx.ClientMsg.DStr, "ServiceType is:%v\n", data.Body.ServiceType)
	}

	kvsSObj.Save(ctx.Seq, ctx.Route, false, false)
	worker.SaveLoginUDIDMsg(kvsSObj.Account, kvsSObj.Model, kvsSObj.Sessid, kvsSObj.UDID, kvsSObj.DeviceToken, kvsSObj.IotVideo)
	worker.IosClearSession(kvsSObj.Os, kvsSObj.Account, kvsSObj.DeviceToken, ctx.Headers.Caller, ctx.ClientMsg.DStr)
	worker.MQSendNormalReply(ctx)
}

// 获取aws访问令牌 JIDCliAwsCredentialGet
func (this *MQHandler) JIdCliAwsCredentialGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliAwsCredentialGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	input := ClientGetCredentialsFormatInput{
		Ctx:        ctx,
		RegionType: data.Body.RegionType,
		KvsSObj:    kvsSObj,
	}

	if workerMsg := ClientGetS3CredentialsFormat(input); workerMsg != nil {
		worker.MQSendWorkMsg(ctx, workerMsg)
	}
}

// 批量获取aws访问令牌 JIDCliAwsCredentialGetMuti
func (this *MQHandler) JIdCliAwsCredentialGetMuti(ctx *worker.JsonWorkerContext) {
	var data worker.JCliAwsCredentialGetMuti
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliAwsCredentialGetMutiRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body = make([]worker.CredentialData, 0)
	for _, regionType := range data.Body.RegionType {
		input := ClientGetCredentialsFormatInput{
			Ctx:        ctx,
			RegionType: regionType,
			KvsSObj:    kvsSObj,
		}

		if rspMsg := ClientGetS3CredentialsFormat(input); rspMsg != nil {
			workerMsg.Body = append(workerMsg.Body, rspMsg.Body)
		}
	}
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取oss访问令牌 JIDCliOssCredentialGet
func (this *MQHandler) JIdCliOssCredentialGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliOssCredentialGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	input := ClientGetCredentialsFormatInput{
		Ctx:        ctx,
		RegionType: data.Body.RegionType,
		KvsSObj:    kvsSObj,
	}

	if workerMsg := ClientGetAliSTSFormat(&input); workerMsg != nil {
		worker.MQSendWorkMsg(ctx, workerMsg)
	}
}

// 批量获取oss访问令牌 JIDCliOssCredentialGetMuti
func (this *MQHandler) JIdCliOssCredentialGetMuti(ctx *worker.JsonWorkerContext) {
	var data worker.JCliOssCredentialGetMuti
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliOssCredentialGetMutiRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body = make([]worker.CredentialData, 0)
	for _, regionType := range data.Body.RegionType {
		input := ClientGetCredentialsFormatInput{
			Ctx:        ctx,
			RegionType: regionType,
			KvsSObj:    kvsSObj,
		}

		if rspMsg := ClientGetAliSTSFormat(&input); rspMsg != nil {
			workerMsg.Body = append(workerMsg.Body, rspMsg.Body)
		}
	}
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取cos访问令牌 JIDCliCosCredentialGet
func (this *MQHandler) JIdCliCosCredentialGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCosCredentialGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	input := ClientGetCredentialsFormatInput{
		Ctx:        ctx,
		RegionType: data.Body.RegionType,
		KvsSObj:    kvsSObj,
	}

	if workerMsg := ClientGetCosSTSFormat(&input); workerMsg != nil {
		worker.MQSendWorkMsg(ctx, workerMsg)
	}
}

// 批量获取cos访问令牌 JIDCliCosCredentialGetMuti
func (this *MQHandler) JIdCliCosCredentialGetMuti(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCosCredentialGetMuti
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliCosCredentialGetMutiRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body = make([]worker.CredentialData, 0)
	for _, regionType := range data.Body.RegionType {
		input := ClientGetCredentialsFormatInput{
			Ctx:        ctx,
			RegionType: regionType,
			KvsSObj:    kvsSObj,
		}

		if rspMsg := ClientGetCosSTSFormat(&input); rspMsg != nil {
			workerMsg.Body = append(workerMsg.Body, rspMsg.Body)
		}
	}
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取Os配置属性表 JIDCliOsAttrGet - cli_os_attr_get
func (this *MQHandler) JIdCliOsAttrGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliOsAttrGet
	_, err := worker.JsonUnmarshalHandleEx(&data, ctx, false)
	if err != nil {
		return
	}

	/*verCnt := mirulib.GetKVSVersionCounter(mirulib.CliOsAttrKVSKey)
	verNum, err := verCnt.Get()
	if err != nil {
		verCnt.Incr(1)
		verNum = 1
	}*/
	var u string = ""
	var dbDOVObj mirulib.TBDeviceOsVersion
	mirulib.DB.Query(mirulib.GDeviceOsVersion).Filter("id", 1).First(&dbDOVObj)
	if dbDOVObj.OsVersion != 0 {
		if data.Body.Version != dbDOVObj.OsVersion {
			fileName := fmt.Sprintf("/forever/web_server/app_config/os_%d.json", dbDOVObj.OsVersion)
			u = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(fileName), 3600, worker.RegionType, true)
		}
	}

	workerMsg := worker.JCliOsAttrGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)

	workerMsg.Body.Version = dbDOVObj.OsVersion
	workerMsg.Body.Url = u
	worker.MQSendWorkMsg(ctx, workerMsg)

	// typ := data.Body.Type
	// kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
	// regionType := mirulib.GetRegionTypeByRoute(kvsSObj.Route)
	// logger.Info("MsgClientGetPidAttrReq :%v  Route : %v  RegionType : %v", data, kvsSObj.Route, regionType)

	// var cau mirulib.TBClientAttrUpgrade
	// mirulib.DB.Query(mirulib.GClientAttrUpgrade).Filter("type", typ, "region", regionType).OrderBy("-time").First(&cau)

	// workerMsg := worker.JCliOsAttrGetRsp{}
	// workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	// workerMsg.Body.Version = cau.VersionAttr
	// workerMsg.Body.Url = cau.UrlAttr
	// workerMsg.Body.Md5 = cau.Md5
	// workerMsg.Body.Type = typ
	// worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取Os配置属性表新版 JIdCliOsRuleGet - cli_os_rule_get
func (this *MQHandler) JIdCliOsRuleGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliOsAttrGet
	_, err := worker.JsonUnmarshalHandleEx(&data, ctx, false)
	if err != nil {
		return
	}

	var u string = ""
	var dbDOVObj mirulib.TBDeviceOsVersion
	mirulib.DB.Query(mirulib.GDeviceOsVersion).Filter("id", 2).First(&dbDOVObj)
	if dbDOVObj.OsVersion != 0 {
		if data.Body.Version != dbDOVObj.OsVersion {
			fileName := fmt.Sprintf("/forever/web_server/app_config/new_os_%d.json", dbDOVObj.OsVersion)
			u = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(fileName), 3600, worker.RegionType, true)
		}
	}

	workerMsg := worker.JCliOsAttrGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)

	workerMsg.Body.Version = dbDOVObj.OsVersion
	workerMsg.Body.Url = u
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取Os配置属性表简易版 JIDCliOsAttrWebGet
func (this *MQHandler) JIdCliOsAttrWebGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliOsAttrWebGet
	_, err := worker.JsonUnmarshalHandleEx(&data, ctx, false)
	if err != nil {
		return
	}
	var dbORObjs []mirulib.TBOsRuleTyh
	mirulib.DB.Query(mirulib.GOsRuleTyh).All(&dbORObjs)
	var deviceTypeName = make(map[string]string)
	var body = []worker.OsAttrData{}
	for _, dbORObj := range dbORObjs {
		var b worker.OsAttrData
		_, ok := deviceTypeName[dbORObj.DeviceType]
		if !ok {
			var dbDTObj mirulib.TBDeviceType
			qs := mirulib.DB.Query(mirulib.GDeviceType).Filter("type_id", dbORObj.DeviceType,
				"language_type", data.Body.LanguageType)
			if qs.Exist() { // 存在客户端使用语种对应的设备分类名称
				qs.First(&dbDTObj)
			} else { // 不存在客户端使用语种对应的设备分类名称，返回英文的
				mirulib.DB.Query(mirulib.GDeviceType).Filter("type_id", dbORObj.DeviceType,
					"language_type", mirulib.TLanguageEN).First(&dbDTObj)
			}
			deviceTypeName[dbORObj.DeviceType] = dbDTObj.Content
		}
		b.DeviceType = deviceTypeName[dbORObj.DeviceType]
		if dbORObj.Icon != "" {
			b.Icon = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(dbORObj.Icon), 3600, dbORObj.IconOssType+100, true)
			if b.Icon != "" {
				b.Icon = strings.Split(b.Icon, "?")[0]
			}
		}
		b.Os = dbORObj.Os
		b.Pid = dbORObj.Pid
		var dbOULObj mirulib.TBOsRuleTyhLanguage
		qs := mirulib.DB.Query(mirulib.GOsRuleTyhLanguage).Filter("os", dbORObj.Os,
			"language_type", data.Body.LanguageType)
		if qs.Exist() { // 存在客户端使用语种对应的型号名称
			qs.First(&dbOULObj)
		} else { // 不存在客户端使用语种对应的型号名称，返回英文的
			mirulib.DB.Query(mirulib.GDeviceType).Filter("os", dbORObj.Os,
				"language_type", mirulib.TLanguageEN).First(&dbOULObj)
		}
		b.PName = dbOULObj.PName
		b.SnPrefix = dbORObj.SnPrefix
		b.Ptz = dbORObj.Ptz
		body = append(body, b)
	}
	workerMsg := worker.JCliOsAttrWebGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body = body
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取App最新升级记录 JIDCliAppUpgradeGet
func (this *MQHandler) JIdCliAppUpgradeGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliAppUpgradeGet
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	logger.Info("MsgClientGetAppUpgradeReq :%v ", data)

	verInt, err := mirulib.GetAppVersionInt(data.Body.Version)
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	languageType := mirulib.GetLanguageByType(data.Body.LanguageType)
	if languageType == "" {
		languageType = mirulib.TLanguageEN
	}

	var cau mirulib.TBAppVersion
	var appStore = false
	qs := mirulib.DB.Query(mirulib.GAppVersion).Filter("os", data.Body.Os, "version_int__gt", verInt)
	if data.Body.Os == mirulib.OSAndroidPhone {
		var brand string
		if data.Body.Brand == "google_play_cylan" { // google play版
			brand = "google"
		} else {
			brand = worker.GetAndroidBrand(data.Body.Brand)
			if brand == "" { // 国内非指定的五家厂商
				brand = "qq"
			}
		}
		if brand != "" {
			qs.Filter(brand, true)
		}
		if brand == "xiaomi" || brand == "huawei" || brand == "vivo" || brand == "oppo" || brand == "meizu" || brand == "honor" {
			appStore = true
		}
	}
	if !qs.Exist() {
		workerMsg := worker.JCliAppUpgradeGetRsp{}
		workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
		workerMsg.Body.Version = ""
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}
	qs.OrderBy("-version_int").First(&cau)
	var dbAVLObj mirulib.TBAppVersionLanguages
	qs1 := mirulib.DB.Query(mirulib.GAppVersionLanguages).Filter("version_id", cau.Id, "language_type", languageType)
	if qs1.Exist() { // 存在客户端使用语种对应的更新说明
		qs1.First(&dbAVLObj)
	} else { // 不存在客户端使用语种对应的更新说明，返回英文的
		mirulib.DB.Query(mirulib.GAppVersionLanguages).Filter("version_id", cau.Id, "language_type", mirulib.TLanguageEN).First(&dbAVLObj)
	}
	// 检查一下APP当前版本和服务端版本之间有没有强制升级的版本
	var must = cau.Must
	var content = dbAVLObj.Content
	if !must {
		q := mirulib.DB.Query(mirulib.GAppVersion).Filter("os", data.Body.Os, "version_int__gt", verInt, "version_int__lt", cau.VersionInt, "must", true)
		if data.Body.Os == mirulib.OSAndroidPhone {
			brand := worker.GetAndroidBrand(data.Body.Brand)
			if brand != "" {
				qs.Filter(brand, true)
			}
		}
		if q.Exist() {
			must = true
			var dbAVObj mirulib.TBAppVersion
			q.OrderBy("-version_int").First(&dbAVObj)
			q1 := mirulib.DB.Query(mirulib.GAppVersionLanguages).Filter("version_id", dbAVObj.Id, "language_type", languageType)
			if q1.Exist() { // 存在客户端使用语种对应的更新说明
				q1.First(&dbAVLObj)
			} else { // 不存在客户端使用语种对应的更新说明，返回英文的
				mirulib.DB.Query(mirulib.GAppVersionLanguages).Filter("version_id", dbAVObj.Id, "language_type", mirulib.TLanguageEN).First(&dbAVLObj)
			}
			// 更新说明替换成强制升级版本的说明
			content = dbAVLObj.Content
		}
	}
	workerMsg := worker.JCliAppUpgradeGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Version = cau.Version
	workerMsg.Body.Box = cau.Box
	workerMsg.Body.Must = must
	workerMsg.Body.Content = content
	workerMsg.Body.Os = data.Body.Os
	workerMsg.Body.AppStore = appStore
	worker.MQSendWorkMsg(ctx, workerMsg)
}

func fmtPushData(pd *mirulib.MsgClientPushDataInner, ctx *worker.JsonWorkerContext, Time int64, count int, cid, account string) {
	pd.Time = Time
	//pd.VideoTime = Time
	pd.Count = count
	pd.Sn = cid
	pd.Account = account
	pd.Os = int32(worker.CidGetOs(cid, true))
	//pd.Type = 0
	//pd.SdcardErrno = 0
	pd.Alias = worker.PucGetAlias(account, cid, "", ctx.ClientMsg.DStr)
}

// 获取消息列表 JIDCliMsgCount
func (this *MQHandler) JIdCliMsgCount(ctx *worker.JsonWorkerContext) {
	kvsSObj, err := worker.JsonUnmarshalHandleEx(nil, ctx, true)
	if err != nil {
		return
	}

	//maxCidCount := mirulib.MaxCidCount
	account := kvsSObj.Account
	worker.MsClearApnsCount(account, "", ctx.ClientMsg.DStr)

	var list = []mirulib.MsgClientPushDataInner{}
	var pucObjs []mirulib.TBPhoneUserCamera
	query := mirulib.DB.QueryDx(mirulib.GPhoneUserCamera, ctx.ClientMsg.DStr).Filter("account", account, "mark", 0)
	if worker.DeviceIsolation && kvsSObj.IotVideo != mirulib.IOT_VIDEO_ALL {
		query.Filter("iot_video", kvsSObj.IotVideo)
	}
	query.All(&pucObjs, "cid", "share_account")

	otherRegionData := getOtherRegionMsgCountData(pucObjs)
	for _, obj := range pucObjs {
		cid := obj.Cid
		cidAccount := account
		// 分享过来的数据，要查询对应主账号的数据
		if obj.ShareAccount != "" {
			cidAccount = obj.ShareAccount
		}

		var firstData []mirulib.JDPData
		var countNum int
		countData, ok := otherRegionData[cid]
		if ok {
			firstData = countData.FirstData
			countNum = countData.Count
		} else {
			firstData = getFirstData(cidAccount, cid)
		}
		if len(firstData) <= 0 || firstData[0].Time == 0 {
			continue
		}
		if !ok {
			countNum = worker.MsCount(cid, cidAccount, ctx.ClientMsg.DStr)
		}
		pd := mirulib.MsgClientPushDataInner{}
		switch firstData[0].ID {
		case worker.DPIDCameraWarnMsg, worker.DPIDAccountCameraWarnMsg:
			pd.PushType = mirulib.PushTypeWarn
			var msg worker.DPCameraWarnMsg
			worker.JsonUnmarshal(&msg, firstData[0].Value)
			pd.Type = msg.DetectType
			// case mirulib.DPIDBellCallMsg:
			// 	var msg mirulib.DPBellCallMsg
			// 	mirulib.Decode(&msg, firstData[0].Value)
			// 	if msg.IsOK == 1 {
			// 		pd.PushType = mirulib.PushTypeBellCallAnswered
			// 	} else {
			// 		pd.PushType = mirulib.PushTypeBellCallNotAnswered
			// 	}
		}

		// 分享账号取得对应的alias
		fmtPushData(&pd, ctx, firstData[0].Time/1000, countNum, cid, account)
		pd.Account = cidAccount // 绑定的主账号
		list = append(list, pd)
	}

	alias := mirulib.MsgSystemCnt
	if kvsSObj.LanguageType > 0 {
		alias = mirulib.MsgSystemCntEnglish
	}

	systemCnt, _ := mirulib.GetKVSDPCounter(account, "cnt").Get()
	pd := mirulib.MsgClientPushDataInner{}
	pd.PushType = mirulib.PushTypeSystem
	pd.Sn = mirulib.MsgSystem
	pd.Account = account
	pd.Count = int(systemCnt) // 获取701 702 703 704 705 706 707统计数量
	pd.Alias = alias
	list = append(list, pd)
	// 未读的反馈消息计数
	fc, _ := mirulib.DB.Query(mirulib.GFeedback).Filter("account", kvsSObj.Account, "feedback_type", 2, "status", 3).Count()
	fpd := mirulib.MsgClientPushDataInner{}
	fpd.PushType = mirulib.PushTypeNewReply
	fpd.Sn = mirulib.MsgSystem
	fpd.Account = account
	fpd.Count = int(fc)
	fpd.Alias = alias
	list = append(list, fpd)
	// 未查看的体验券计数
	var couponCount int
	if worker.DeviceIsolation { // 微信小程序和APP的数据隔离
		var dbCIObjs []mirulib.TBCouponInfo
		mirulib.DB.Query(mirulib.GCouponInfo).Filter("app_account", kvsSObj.Account, "is_new", true).All(&dbCIObjs)
		for _, dbCIObj := range dbCIObjs {
			if dbCIObj.SN != "" {
				var dbCObj mirulib.TBCamera
				mirulib.DB.Query(mirulib.GCamera).Filter("cid", dbCIObj.SN).First(&dbCObj)
				if dbCObj.IotVideo == kvsSObj.IotVideo {
					couponCount += 1
				}
			} else {
				couponCount += 1
			}
		}
	} else {
		cCount, _ := mirulib.DB.Query(mirulib.GCouponInfo).Filter("app_account", kvsSObj.Account, "is_new", true).Count()
		couponCount = int(cCount)
	}
	couponPushData := mirulib.MsgClientPushDataInner{}
	couponPushData.PushType = mirulib.PushTypeNewCoupon
	couponPushData.Sn = mirulib.MsgSystem
	couponPushData.Account = account
	couponPushData.Count = couponCount
	list = append(list, couponPushData)

	// kvsPuObj := mirulib.GetKVSPU(account)
	// kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	// kvsPuObj.Get()

	// var lang int
	// if kvsSObj.LanguageType > 0 {
	// 	lang = 1
	// }
	// var sysObjs []mirulib.TBMsgSystem
	// var nowUnix = time.Now().Unix()
	// cond := orm.NewCondition()
	// cond2 := cond.AndCond(cond.And("is_pushed", 1).
	// 	And("language_type", lang).
	// 	And("begin_time__lt", nowUnix)).
	// 	AndCond(cond.Or("end_time__gt", nowUnix).
	// 		Or("end_time", 0))
	// ormer := orm.NewOrm()
	// ormer.QueryTable(mirulib.GMsgSystem).SetCond(cond2).OrderBy("-time").All(&sysObjs)
	// newOem := oldOemToNew(kvsSObj.Oem)
	// for _, obj := range sysObjs {
	// 	if kvsPuObj.RegisterTime > obj.Time {
	// 		continue
	// 	}
	// 	if len(obj.Os) == 0 || len(obj.Oem) == 0 {
	// 		continue
	// 	}
	// 	os := strings.Split(obj.Os, ",")
	// 	oem := strings.Split(obj.Oem, ",")
	// 	if !mirulib.IsInArray(strconv.Itoa(int(kvsSObj.Os)), os) || !mirulib.IsInArray(newOem, oem) {
	// 		continue
	// 	}

	// 	alias := mirulib.MsgSystemCnt
	// 	if kvsSObj.LanguageType > 0 {
	// 		alias = mirulib.MsgSystemCntEnglish
	// 	}

	// 	pd := PushFmtData
	// 	pd.PushType = 0
	// 	pd.Cid = mirulib.MsgSystem
	// 	pd.Account = account
	// 	pd.MsgSystemId = 0
	// 	pd.Count = 0
	// 	pd.Time = obj.Time
	// 	pd.Alias = alias
	// 	pd.Title = obj.Title

	// 	body := mirulib.PushFormat(pd.PushType, pd.Cid, pd.Account, pd.MsgSystemId, pd.Time, pd.Version, pd.BindingAccount,
	// 		pd.SdcardErrno, pd.Alias, pd.Count, pd.Type, pd.Title, pd.Url, pd.ShareAccount, pd.VideoTime, pd.OssType, pd.Os,
	// 		pd.Tly, pd.Objects, pd.PersonNames)
	// 	body.MsgClientPushDataInner.Os = -1 //system兼容考虑
	// 	list = append(list, body.MsgClientPushDataInner)
	// 	break
	// }

	workerMsg := worker.JCliMsgCountRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body = list
	worker.MQSendWorkMsg(ctx, workerMsg)

	// workerMsg := mirulib.MsgClientMsgCountRsp{
	// 	MsgWorkerRspHeader: mirulib.NewMsgWorkerRspHeader(ctx),
	// 	List:               list,
	// }
	// mirulib.MQSendWorkMsg(ctx, workerMsg)
}

// 预置位设置 JIDCliCameraPresetSet
func (this *MQHandler) JIdCliCameraPresetSet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCameraPresetSet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	workerMsg := worker.JCliCameraPresetSetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body.Sn = data.Body.Sn
	workerMsg.Body.Type = data.Body.Type
	workerMsg.Body.Index = data.Body.Index
	workerMsg.Body.Name = data.Body.Name
	var needSend = false
	switch data.Body.Type {
	case 1:
		num, _ := mirulib.DBNO.Query(mirulib.GDevicePreset).Filter("account", kvsSObj.Account, "cid", data.Body.Sn).Count()
		if num >= conf.App.Worker.MaxPreset {
			workerMsg.JsonRspHeader.Ret = worker.ErrorPresetOutLimit
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorPresetOutLimit)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}
		query := mirulib.DBNO.Query(mirulib.GDevicePreset).Filter("account", kvsSObj.Account, "cid", data.Body.Sn, "name", data.Body.Name)
		if query.Exist() {
			workerMsg.JsonRspHeader.Ret = worker.ErrorPresetNameExist
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorPresetNameExist)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}
		needSend = true
	case 2, 3:
		needSend = true
	case 4:
		query := mirulib.DBNO.Query(mirulib.GDevicePreset).Filter("account", kvsSObj.Account, "cid", data.Body.Sn, "name", data.Body.Name)
		if query.Exist() {
			var dbDPObj mirulib.TBDevicePreset
			query.First(&dbDPObj)
			if dbDPObj.Index != data.Body.Index {
				workerMsg.JsonRspHeader.Ret = worker.ErrorPresetNameExist
				workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorPresetNameExist)
				worker.MQSendWorkMsg(ctx, workerMsg)
				return
			}
		}
		mirulib.DBNO.Query(mirulib.GDevicePreset).Filter("account", kvsSObj.Account, "cid", data.Body.Sn, "idx", data.Body.Index).Update("name", data.Body.Name)

		worker.MQSendWorkMsg(ctx, workerMsg)
	default:
		workerMsg.JsonRspHeader.Ret = worker.ErrorInvalidParameter
		workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorInvalidParameter)
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	}

	if needSend {
		kvsCidObj := mirulib.GetKVSSession(data.Body.Sn)
		kvsCidObj.Get()
		if kvsCidObj.Net <= mirulib.NetOffline {
			workerMsg.JsonRspHeader.Ret = worker.ErrorCIDOffline
			workerMsg.JsonRspHeader.Msg = worker.GetCodeMsg(worker.ErrorCIDOffline)
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}

		dpMsg := mirulib.JDPData{
			ID:   worker.DPIDCameraPreset,
			Time: time.Now().Unix() * 1000,
			Value: worker.JsonMarshal(
				worker.DPCameraPresetMsg{
					Type:  data.Body.Type,
					Index: data.Body.Index,
					Name:  data.Body.Name,
				}),
		}

		msgSend := worker.JPubDpAct{}
		msgSend.JsonRspHeader.Headers = worker.GetJsonHeader(worker.JIDPubDpAct, data.Headers.ReqID, ctx.Sess, data.Body.Sn).Headers
		msgSend.Body.Act = 1
		msgSend.Body.DpList = []mirulib.JDPData{dpMsg}
		worker.MQSendWorkerMsgKVS(kvsCidObj, &msgSend, ctx.ClientMsg.DStr)
	}
}

// 预置位列表 JIDCliCameraPresetList
func (this *MQHandler) JIdCliCameraPresetList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCameraPresetList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	var dbDPObjs []mirulib.TBDevicePreset
	mirulib.DBNO.Query(mirulib.GDevicePreset).Filter("account", kvsSObj.Account, "cid", data.Body.Sn).All(&dbDPObjs)
	var presetList = []worker.PresetInfo{}
	for _, dbDPObj := range dbDPObjs {
		preset := worker.PresetInfo{
			Index:    dbDPObj.Index,
			Name:     dbDPObj.Name,
			Enable:   dbDPObj.Enable,
			ImageUrl: mirulib.GetImageSignUrl(dbDPObj.ImageUrl, mirulib.WeekExTime, dbDPObj.OssType, true),
		}
		presetList = append(presetList, preset)
	}

	workerMsg := worker.JCliCameraPresetListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body.Sn = data.Body.Sn
	workerMsg.Body.PresetList = presetList
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 查询设备云存储状态 JIDCliCloudStorageGet
func (this *MQHandler) JIdCliCloudStorageGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCloudStorageGet
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}
	logger.Info("JCliCloudStorageGet :%v ", data)
	var status, video int
	// 查询设备的云存储状态
	qs := mirulib.DB.Query(mirulib.GDeviceServiceInfo).Filter("sn", data.Body.Sn, "base_type", mirulib.BaseServiceCloudStorage)
	if !qs.Exist() { // 未开通过云存储服务
		status = mirulib.ServiceStatusNotUsed
		video = 0
	} else {
		var dbDSIObj mirulib.TBDeviceServiceInfo
		qs.OrderBy("-expire_time").First(&dbDSIObj)
		if dbDSIObj.ExpireTime > time.Now().Unix() { // 服务生效中
			status = mirulib.ServiceStatusUsing
			video = 1
		} else { // 服务已过期
			status = mirulib.ServiceStatusExpired
			// todo 通过查询是否存在视频报警消息或者S3的云存储目录中是否存在视频文件来决定video的值
		}
	}
	workerMsg := worker.JCliCloudStorageGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Sn = data.Body.Sn
	workerMsg.Body.ServiceStatus = status
	workerMsg.Body.Video = video
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 门铃接听状态 JIDCliBellCallStatus
func (this *MQHandler) JIdCliBellCallStatus(ctx *worker.JsonWorkerContext) {
	var data worker.JCliBellCallStatus
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	workerMsg := worker.JCliBellCallStatusRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Sn = data.Body.Sn
	if mirulib.GetBellCall(data.Body.Sn) == nil {
		workerMsg.Body.Status = 1
		workerMsg.Body.ServerTime = time.Now().Unix()
		duration := mirulib.BellCallTimeout + int(data.Body.Time-time.Now().Unix())
		if duration > 0 {
			workerMsg.Body.Duration = duration
		}
	}
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 二维码登录-pc获取websocket列表 JIDCliQrLoginGetWebsocketList
func (this *MQHandler) JIdCliQrLoginGetWebsocketList(ctx *worker.JsonWorkerContext) {

	workerMsg := worker.JCliQrLoginGetWebsocketListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	for _, data := range conf.Base.Servers {
		workerMsg.Body = append(workerMsg.Body, worker.WebsocketServer{data.Default, data.En, data.Cn, data.Ws, data.Wss})
	}
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 二维码登录-PC获取随机码 JIDCliQrLoginRandom
func (this *MQHandler) JIdCliQrLoginRandom(ctx *worker.JsonWorkerContext) {
	var data worker.JCliQrLoginRandom
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	curTime := time.Now().Unix()
	var kvsQrData mirulib.KVSQrLoginData
	var err error
	switch data.Body.Step {
	case 0: // 获取随机码
		data.Body.Random = mirulib.GetRandomString(mirulib.RandomSession)
		kvsQrData.Status = mirulib.QrLoginStatusInit
		kvsQrData.InitTime = curTime
		err = mirulib.SetQrLoginData(data.Body.Random, &kvsQrData, ctx.ClientMsg.DStr)
		if err != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorDataBase)
			return
		}
	case 1: // 获取随机码状态
		err = mirulib.GetQrLoginData(data.Body.Random, &kvsQrData, ctx.ClientMsg.DStr)
		if err != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorQrLoginRandomInvalid)
			return
		}
		if curTime-kvsQrData.InitTime > mirulib.QrLoginTimeOut {
			worker.MQSendErrorReply(ctx, worker.ErrorQrLoginRandomTimeout)
			return
		}

		if kvsQrData.Status == mirulib.QrLoginStatusConfirmed {
			kvsQrData.Status = mirulib.QrLoginStatusLogined
			//kvsQrData.KvsSObj = kvsSObj
			err = mirulib.SetQrLoginData(data.Body.Random, &kvsQrData, ctx.ClientMsg.DStr)
			if err != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorDataBase)
				return
			}

			sessid := mirulib.GetRandomString(mirulib.RandomSession)
			kvsSObj := mirulib.GetKVSSession(sessid)
			kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
			kvsSObj.Account = kvsQrData.KvsSObj.Account
			kvsSObj.LastLoginTime = time.Now().Unix()
			kvsSObj.Os = mirulib.OSPC
			kvsSObj.Net = mirulib.NetNewWifi
			kvsSObj.Name = "qr_login"
			kvsSObj.LanguageType = kvsQrData.KvsSObj.LanguageType
			kvsSObj.Save(ctx.Seq, ctx.Route, true, true)

			ctx.Headers.Caller = sessid
			ctx.UpdateSess = true
			ctx.Net = mirulib.NetNewWifi
			workerMsg := worker.JCliLoginRsp{}
			workerMsg.JsonRspHeader = worker.GetJsonRspHeaderEx(worker.JIDCliLoginRsp, data.Headers.ReqID, worker.CodeOK)
			workerMsg.Body.Account = kvsQrData.KvsSObj.Account
			workerMsg.Body.Sessid = sessid
			worker.MQSendWorkMsg(ctx, workerMsg)
			return
		}
	case 2:
		sessid := data.Body.Random
		kvsSObj := mirulib.GetKVSSession(sessid)
		kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
		if kvsSObj.Get() != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
			return
		}

		kvsSObj.LastLoginTime = time.Now().Unix()
		kvsSObj.Net = mirulib.NetNewWifi
		kvsSObj.Save(ctx.Seq, ctx.Route, true, true)

		ctx.Headers.Caller = sessid
		ctx.UpdateSess = true
		ctx.Net = mirulib.NetNewWifi
		workerMsg := worker.JCliLoginRsp{}
		workerMsg.JsonRspHeader = worker.GetJsonRspHeaderEx(worker.JIDCliLoginRsp, data.Headers.ReqID, worker.CodeOK)
		workerMsg.Body.Account = kvsSObj.Account
		workerMsg.Body.Sessid = sessid
		worker.MQSendWorkMsg(ctx, workerMsg)
		return
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	workerMsg := worker.JCliQrLoginRandomRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Step = data.Body.Step
	workerMsg.Body.Status = kvsQrData.Status
	workerMsg.Body.Random = data.Body.Random
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 二维码登录-APP扫描随机码 JIDCliQrLoginAppScan
func (this *MQHandler) JIdCliQrLoginAppScan(ctx *worker.JsonWorkerContext) {
	var data worker.JCliQrLoginAppScan
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	var kvsQrData mirulib.KVSQrLoginData
	err = mirulib.GetQrLoginData(data.Body.Random, &kvsQrData, ctx.ClientMsg.DStr)
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorQrLoginRandomInvalid)
		return
	}

	curTime := time.Now().Unix()
	if curTime-kvsQrData.InitTime > mirulib.QrLoginTimeOut {
		worker.MQSendErrorReply(ctx, worker.ErrorQrLoginRandomTimeout)
		return
	}

	switch data.Body.Step {
	case 0:
		if kvsQrData.Status == mirulib.QrLoginStatusInit {
			kvsQrData.Status = mirulib.QrLoginStatusScanned
			//kvsQrData.KvsSObj = kvsSObj
			err = mirulib.SetQrLoginData(data.Body.Random, &kvsQrData, ctx.ClientMsg.DStr)
			if err != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorDataBase)
				return
			}
		} else if kvsQrData.Status == mirulib.QrLoginStatusScanned {
			// 此状态认为正常，可能是多次扫码上报
		} else {
			worker.MQSendErrorReply(ctx, worker.ErrorQrLoginStatus)
			return
		}
	case 1:
		if kvsQrData.Status == mirulib.QrLoginStatusScanned {
			kvsQrData.Status = mirulib.QrLoginStatusConfirmed
			kvsQrData.KvsSObj = *kvsSObj
			err = mirulib.SetQrLoginData(data.Body.Random, &kvsQrData, ctx.ClientMsg.DStr)
			if err != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorDataBase)
				return
			}
		} else if kvsQrData.Status == mirulib.QrLoginStatusConfirmed {
			// 此状态认为正常，可能是多次扫码上报
		} else {
			worker.MQSendErrorReply(ctx, worker.ErrorQrLoginStatus)
			return
		}
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	workerMsg := worker.JCliQrLoginAppScanRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Step = data.Body.Step
	workerMsg.Body.Status = kvsQrData.Status
	workerMsg.Body.Random = data.Body.Random
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取app基础配置 JIDCliBaseConfigGet cli_base_config_get
func (this *MQHandler) JIdCliBaseConfigGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliBaseConfigGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	var dbACObjs []mirulib.TBAppConfig
	mirulib.DB.Query(mirulib.GAppConfig).All(&dbACObjs)
	var startPage worker.StartPageInfo
	var homeBanner worker.StartPageInfo
	var noDevice worker.NoDeviceInfo
	var community worker.CommunityInfo
	var dbPUObj mirulib.TBPhoneUser
	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", kvsSObj.Account).First(&dbPUObj)
	for _, dbACObj := range dbACObjs {
		// 启动页
		if dbACObj.ConfigType == mirulib.AppConfigStartPage {
			startPage.Enable = dbACObj.Enable
			if dbACObj.Image != "" {
				startPage.Image = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(dbACObj.Image), 3600, worker.RegionType, true)
			}
			startPage.LinkPage = dbACObj.LinkPage
			startPage.LinkType = dbACObj.LinkType
			startPage.LinkUrl = dbACObj.LinkUrl
			startPage.Version = dbACObj.CreateTime
		}
		// 主页广告栏
		if dbACObj.ConfigType == mirulib.AppConfigHomeBanner {
			nowTime := time.Now().Unix()
			if nowTime < dbACObj.EndTime && nowTime > dbACObj.StartTime {
				homeBanner.Enable = dbACObj.Enable
				if dbPUObj.CountryCode != "CN" { // 暂时不支持非中国地区
					homeBanner.Enable = false
				}
				if dbACObj.Image != "" {
					homeBanner.Image = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(dbACObj.Image), 3600, worker.RegionType, true)
				}
				homeBanner.LinkPage = dbACObj.LinkPage
				homeBanner.LinkType = dbACObj.LinkType
				homeBanner.LinkUrl = dbACObj.LinkUrl
				homeBanner.Version = dbACObj.CreateTime
			}
		}
		// 主页无设备
		if dbACObj.ConfigType == mirulib.AppConfigNoDevice {
			if dbACObj.Image != "" {
				noDevice.Image = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(dbACObj.Image), 3600, worker.RegionType, true)
			}
			noDevice.Version = dbACObj.CreateTime
		}
		// 社区
		if dbACObj.ConfigType == mirulib.AppConfigCommunity {
			if dbACObj.Image != "" {
				community.Image = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(dbACObj.Image), 3600, worker.RegionType, true)
			}
			community.Home = dbACObj.LinkUrl
			community.Version = dbACObj.CreateTime
		}
	}

	workerMsg := worker.JCliBaseConfigGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.StartPage = startPage
	workerMsg.Body.HomeBanner = homeBanner
	workerMsg.Body.NoDevice = noDevice
	workerMsg.Body.Community = community
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 绑定演示视频 JIDCliOsVideoList - cli_os_video_list
func (this *MQHandler) JIdCliOsVideoList(ctx *worker.JsonWorkerContext) {
	var req worker.JCliOsVideoList
	if worker.JsonUnmarshalHandle(&req, ctx, true) != nil {
		return
	}

	lang := req.Body.LanguageType
	if lang == "" {
		langType := req.Body.Language
		lang = mirulib.GetLanguageByType(langType)
	}

	var datas = []worker.JCliOsVideoListData{}
	var dbORTObjs []mirulib.TBOsRuleTyh
	mirulib.DB.Query(mirulib.GOsRuleTyh).Filter("os", 101).All(&dbORTObjs) // todo 临时只返回101的演示视频
	for _, dbORTObj := range dbORTObjs {
		var data worker.JCliOsVideoListData
		data.Os = dbORTObj.Os
		data.PId = dbORTObj.Pid
		if dbORTObj.Video != "" {
			data.VideoUrl = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(dbORTObj.Video), 24*3600, worker.RegionType, true)
		}
		var dbORTLObj mirulib.TBOsRuleTyhLanguage
		qs := mirulib.DB.Query(mirulib.GOsRuleTyhLanguage).Filter("os", dbORTObj.Os, "language_type", lang)
		if qs.Exist() {
			qs.First(&dbORTLObj)
		} else {
			mirulib.DB.Query(mirulib.GOsRuleTyhLanguage).Filter("os", dbORTObj.Os, "language_type", mirulib.TLanguageEN).First(&dbORTLObj)
		}
		data.PName = dbORTLObj.PName
		datas = append(datas, data)
	}

	workerMsg := worker.JCliOsVideoListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(req.JsonHeader)
	workerMsg.Body = datas
	worker.MQSendWorkMsg(ctx, workerMsg)
}

func GetImagePubUrl(fileName string) string {
	var ur string = ""
	if fileName == "" {
		return ur
	}
	fileUrl := mirulib.GetImageSignUrl(mirulib.ReplaceUrl(fileName), 3600, worker.RegionType+100, true)
	if fileUrl != "" {
		ur = strings.Split(fileUrl, "?")[0]
	}
	return ur
}

// 获取使用问题 JIDCliUseQuestionList - cli_use_question_list
func (this *MQHandler) JIdCliUseQuestionList(ctx *worker.JsonWorkerContext) {
	var req worker.JCliUseQuestionList
	if worker.JsonUnmarshalHandle(&req, ctx, true) != nil {
		return
	}

	lang := req.Body.LanguageType
	if lang == "" {
		langType := req.Body.Language
		lang = mirulib.GetLanguageByType(langType)
	}
	var datas = []worker.JCliUseQuestionListData{}
	var dbUQObjs []mirulib.TBUseQuestion
	qs := mirulib.DB.Query(mirulib.GUseQuestion).Filter("language_type", lang, "type_id", req.Body.Type)
	if qs.Exist() {
		qs.OrderBy("-create_time").All(&dbUQObjs)
	} else {
		mirulib.DB.Query(mirulib.GUseQuestion).Filter("language_type", mirulib.TLanguageEN, "type_id", req.Body.Type).OrderBy("-create_time").All(&dbUQObjs)
	}
	for _, dbUQObj := range dbUQObjs {
		var data worker.JCliUseQuestionListData
		data.QuestionId = dbUQObj.QuestionID
		data.Title = dbUQObj.Title
		data.HtmlUrl = GetImagePubUrl(dbUQObj.HtmlUrl)
		datas = append(datas, data)
	}

	workerMsg := worker.JCliUseQuestionListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(req.JsonHeader)
	workerMsg.Body = datas
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取常见问题 JIDCliCommonQuestionList - cli_common_question_list
func (this *MQHandler) JIdCliCommonQuestionList(ctx *worker.JsonWorkerContext) {
	var req worker.JCliCommonQuestionList
	if worker.JsonUnmarshalHandle(&req, ctx, true) != nil {
		return
	}

	lang := req.Body.LanguageType
	if lang == "" {
		langType := req.Body.Language
		lang = mirulib.GetLanguageByType(langType)
	}
	var datas = []worker.JCliCommonQuestionListData{}
	var dbCUQObjs []mirulib.TBCommonUseQuestion
	mirulib.DB.Query(mirulib.GCommonUseQuestion).OrderBy("q_idx").All(&dbCUQObjs)
	for _, dbCUQObj := range dbCUQObjs {
		var data worker.JCliCommonQuestionListData
		data.Index = dbCUQObj.Index
		data.QuestionId = dbCUQObj.QuestionID
		var dbUQObj mirulib.TBUseQuestion
		qs := mirulib.DB.Query(mirulib.GUseQuestion).Filter("question_id", dbCUQObj.QuestionID, "language_type", lang)
		if qs.Exist() {
			qs.First(&dbUQObj)
		} else {
			mirulib.DB.Query(mirulib.GUseQuestion).Filter("question_id", dbCUQObj.QuestionID, "language_type", mirulib.TLanguageEN).First(&dbUQObj)
		}
		data.Title = dbUQObj.Title
		data.HtmlUrl = GetImagePubUrl(dbUQObj.HtmlUrl)
		datas = append(datas, data)
	}

	workerMsg := worker.JCliCommonQuestionListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(req.JsonHeader)
	workerMsg.Body = datas
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取电话通知验证码 	JIdCliVoiceCodeGet
func (this *MQHandler) JIdCliVoiceCodeGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliCodeGet
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	checkType := mirulib.RegisterTypePhone
	if data.Body.Type >= mirulib.ClientEmailTypeRegister {
		checkType = mirulib.RegisterTypeEmail
	}
	if isMatch, retCode := checkAccount(data.Body.Account, checkType); !isMatch {
		worker.MQSendErrorReply(ctx, retCode)
		return
	}

	smsPhone := data.Body.Account
	email := data.Body.Account
	kvsPuObj := mirulib.GetKVSPU(data.Body.Account)
	kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
	err := kvsPuObj.Get()
	switch data.Body.Type {
	case mirulib.ClientSmsTypeRegister,
		mirulib.ClientEmailTypeRegister:
		if err == nil {
			worker.MQSendErrorReply(ctx, worker.ErrorAccountAlreadyExist)
			return
		}
	case mirulib.ClientSmsTypeForgetPass:
		if err != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidPhoneNumber)
			return
		}
		smsPhone = kvsPuObj.SmsPhone
	case mirulib.ClientSmsTypeEditUserInfo:
		if err == nil {
			kvsSObj := mirulib.GetKVSSession(data.Headers.Caller)
			kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
			if kvsSObj.Get() != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
				return
			}
			if kvsPuObj.Account != smsPhone || kvsSObj.Account != smsPhone {
				worker.MQSendErrorReply(ctx, worker.ErrorPhoneExist)
				return
			}
		}
	case mirulib.ClientEmailTypeForgetPass:
		if err != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorIsNotEmail)
			return
		}
		email = kvsPuObj.Email
	case mirulib.ClientEmailTypeEditUserInfo:
	case mirulib.ClientSmsTypeOpenLoginBindPhone,
		mirulib.ClientEmailTypeOpenLoginBindEmail:
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	kvsSmsCountObj := mirulib.GetKVSSmsCount(data.Body.Account)
	kvsSmsCountObj.SetDebugStr(ctx.ClientMsg.DStr)
	e := kvsSmsCountObj.Get()
	if e != nil {
		kvsSmsCountObj.Count = 1
		kvsSmsCountObj.Time = time.Now().Unix()
		kvsSmsCountObj.Save()
	} else {
		if time.Now().Unix()-kvsSmsCountObj.Time < smsTimeout {
			if kvsSmsCountObj.Count == smsMaxCount {
				worker.MQSendErrorReply(ctx, worker.ErrorGetCodeTooFrequent)
				return
			} else {
				kvsSmsCountObj.Count += 1
				kvsSmsCountObj.Save()
			}
		} else {
			kvsSmsCountObj.Count = 1
			kvsSmsCountObj.Time = time.Now().Unix()
			kvsSmsCountObj.Save()
		}
	}

	code := mirulib.GetRandomStringEx(mirulib.RandomCodeEx, 4)
	smsToken := mirulib.GetRandomString(mirulib.RandomSignature)
	kvsSmsObj := mirulib.GetKVSSms(data.Body.Account)
	kvsSmsObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsSmsObj.Get() == nil {
		code = kvsSmsObj.Code
		smsToken = kvsSmsObj.Token
	}

	var recvAccount string
	switch data.Body.Type {
	case mirulib.ClientSmsTypeRegister, mirulib.ClientSmsTypeForgetPass, mirulib.ClientSmsTypeEditUserInfo,
		mirulib.ClientSmsTypeOpenLoginBindPhone, mirulib.ClientEmailTypeOpenLoginBindEmail:
		if !sendVMS(smsPhone, code) {
			worker.MQSendErrorReply(ctx, worker.ErrorSendSmsFailed)
			return
		}
		recvAccount = worker.SecretAccount(smsPhone, worker.SecretPhone)
	case mirulib.ClientEmailTypeRegister:
		worker.SendEmailEx(email, data.Body.LanguageType, mirulib.MailTypeRegisterByCode, data.Body.Vid, "", 0, code, data.Body.NotifyType, ctx.ClientMsg.DStr)
		recvAccount = worker.SecretAccount(email, worker.SecretEmail)
	case mirulib.ClientEmailTypeForgetPass:
		worker.SendEmailEx(email, data.Body.LanguageType, mirulib.MailTypeForgetPass, data.Body.Vid, "", 0, code, data.Body.NotifyType, ctx.ClientMsg.DStr)
		recvAccount = worker.SecretAccount(email, worker.SecretEmail)
	default:
		worker.MQSendErrorReply(ctx, worker.ErrorCodeTimeout)
		return
	}

	kvsSmsObj.Code = code
	kvsSmsObj.Token = smsToken
	kvsSmsObj.Step = 0
	kvsSmsObj.Time = time.Now().Unix()
	kvsSmsObj.Save()

	workerMsg := worker.JCliCodeGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Token = smsToken
	workerMsg.Body.RecvAccount = recvAccount
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 获取app首页横幅 JIdCliBannerGet cli_banner_get
func (this *MQHandler) JIdCliBannerGet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliBannerGet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	lan := mirulib.GetLanguageByType(data.Body.LanguageType)
	var dbPUObj mirulib.TBPhoneUser
	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", kvsSObj.Account).First(&dbPUObj)
	var dbBIObjs []mirulib.TBBannerInfo
	nowTime := time.Now().Unix()
	q := mirulib.DB.Query(mirulib.GBannerInfo).Filter("start_time__lt", nowTime, "end_time__gt", nowTime)
	if data.Body.AppType == 1 {
		q.Filter("mini_enable", true)
	} else {
		q.Filter("enable", true)
	}
	q.OrderBy("-create_time").All(&dbBIObjs)
	var infos = []worker.StartPageInfo{}
	for _, dbBIObj := range dbBIObjs {
		accRegion := worker.GetServerRegionByCode(dbPUObj.CountryCode)
		// 按位表示，0b0001-中国，0b0010-美国，0b0100-德国, 0b1000-新加坡
		if (accRegion == "CN" && dbBIObj.Region&1 == 1) ||
			(accRegion == "US" && (dbBIObj.Region>>1)&1 == 1) ||
			(accRegion == "DE" && (dbBIObj.Region>>2)&1 == 1) ||
			(accRegion == "SG" && (dbBIObj.Region>>3)&1 == 1) {
			var info worker.StartPageInfo
			if data.Body.AppType == 1 {
				info.Enable = dbBIObj.MiniEnable
			} else {
				info.Enable = dbBIObj.Enable
			}
			if dbBIObj.LinkType == 1 {
				if accRegion == "CN" {
					info.LinkUrl = strings.Replace(dbBIObj.LinkUrl, "fast", "fcn", 1)
				}
				if accRegion == "US" {
					info.LinkUrl = strings.Replace(dbBIObj.LinkUrl, "fast", "fus", 1)
				}
				if accRegion == "DE" {
					info.LinkUrl = strings.Replace(dbBIObj.LinkUrl, "fast", "fde", 1)
				}
				if accRegion == "SG" {
					info.LinkUrl = strings.Replace(dbBIObj.LinkUrl, "fast", "fsg", 1)
				}
			} else {
				info.LinkUrl = dbBIObj.LinkPage
			}
			info.LinkType = dbBIObj.LinkType
			/*if lan == "" {
				lan = dbBIObj.DefaultLanguage
			}*/
			var dbBLObj mirulib.TBBannerLanguage
			qs := mirulib.DB.Query(mirulib.GBannerLanguage).Filter("banner_id", dbBIObj.Id, "language_type", lan)
			if qs.Exist() {
				qs.First(&dbBLObj)
			} else {
				if !dbBIObj.EnableDefaultLan { // 没有开启默认语言，没有配置对应语言的图片则不返回该活动
					continue
				}
				mirulib.DB.Query(mirulib.GBannerLanguage).Filter("banner_id", dbBIObj.Id, "language_type", dbBIObj.DefaultLanguage).First(&dbBLObj)
			}
			if dbBLObj.Image != "" {
				info.Image = mirulib.GetImageSignUrl(mirulib.ReplaceUrl(dbBLObj.Image), 86400*7, worker.RegionType, true)
			}
			info.Version = dbBLObj.CreateTime
			infos = append(infos, info)
		}
	}

	workerMsg := worker.JCliBannerGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.HomeBanner = infos
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 烧录登录 JIDCliBurnLogin
func (this *MQHandler) JIdCliBurnLogin(ctx *worker.JsonWorkerContext) {
	var data worker.JCliLogin
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	account := strings.Replace(data.Body.Account, " ", "", -1)
	if account == "" {
		logger.Errorf("invalid param, Account:%s, Password:%s", account, data.Body.Passwd)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	qs := mirulib.DB.Query(mirulib.G4gJobNumber).Filter("job_number", account)
	if !qs.Exist() {
		worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
		return
	}
	var db4JNObj mirulib.TB4gJobNumber
	qs.First(&db4JNObj)

	sessid := strings.Replace(data.Body.Sessid, " ", "", -1)
	oldKvsSObj := worker.GetLastSession(sessid)
	if data.Body.Passwd != "" { //密码不为空，使用账号密码登录
		if mirulib.GetMd5String(db4JNObj.Password) != data.Body.Passwd {
			worker.MQSendErrorReply(ctx, worker.ErrorLoginInvalidPass)
			return
		}

		sessid = ""
		if oldKvsSObj != nil && oldKvsSObj.Account == account {
			if (time.Now().Unix() - oldKvsSObj.LastLoginTime) < sessionTimeout {
				sessid = oldKvsSObj.Sessid
			} else {
				oldKvsSObj.Delete()
			}
		}
		if sessid == "" {
			sessid = newSessid(account, data.Body.Vid, ctx)
		}
		addLoginSession(ctx, data, nil, sessid, false, mirulib.IOT_VIDEO_CYLAN)
	} else if sessid != "" { // 使用sessid重登
		if oldKvsSObj == nil || (time.Now().Unix()-oldKvsSObj.LastLoginTime) > sessionTimeout {
			logger.Errorf("session timeout:%s", sessid)
			worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
			return
		}
		if oldKvsSObj.Account != account {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		addLoginSession(ctx, data, nil, sessid, false, mirulib.IOT_VIDEO_CYLAN)
	} else {
		logger.Errorf("invalid param, Account:%s, Password:%s sessid:%s", account, data.Body.Passwd, sessid)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	ctx.Headers.Caller = sessid
	ctx.UpdateSess = true
	ctx.UpdateLoginOK = true
	ctx.Net = data.Body.Net

	workerMsg := worker.JCliBurnLoginRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body.Account = account
	workerMsg.Body.Sessid = sessid
	workerMsg.Body.SrsHttp = worker.GetCircleStrings(&srsHttpSeed, 1, conf.Base.Srs.Http)[0]

	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 烧录设备列表 JIDCliBurnDevList
func (this *MQHandler) JIdCliBurnDevList(ctx *worker.JsonWorkerContext) {
	var data worker.JsonHeader
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	datas := make([]worker.BurnDevDataInfo, 0)
	var burnObjs []mirulib.TBCameraBurn
	mirulib.DBNO.Query(mirulib.GCameraBurn).Filter("snum", kvsSObj.Account).All(&burnObjs)
	for _, obj := range burnObjs {
		datas = append(datas,
			worker.BurnDevDataInfo{
				Sessid:           obj.Sessid,
				Sn:               obj.Cid,
				IMEI:             obj.IMEI,
				Mac:              obj.Mac,
				Os:               obj.Os,
				WebrtcPermitCode: obj.WebrtcPermitCode,
			})
	}

	workerMsg := worker.JCliBurnDevlistRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body = datas
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 韩国账号登录 JIDCliKoreaLogin
func (this *MQHandler) JIdCliKoreaLogin(ctx *worker.JsonWorkerContext) {
	var data worker.JCliLogin
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	account := strings.Replace(data.Body.Account, " ", "", -1)
	if account == "" {
		logger.Errorf("invalid param, Account:%s, Password:%s", account, data.Body.Passwd)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	sessid := strings.Replace(data.Body.Sessid, " ", "", -1)
	oldKvsSObj := worker.GetLastSession(sessid)
	if data.Body.Passwd != "" { //密码不为空，使用账号密码登录
		if mirulib.GetMd5String("a123456") != data.Body.Passwd {
			worker.MQSendErrorReply(ctx, worker.ErrorLoginInvalidPass)
			return
		}

		sessid = ""
		if oldKvsSObj != nil && oldKvsSObj.Account == account {
			if (time.Now().Unix() - oldKvsSObj.LastLoginTime) < sessionTimeout {
				sessid = oldKvsSObj.Sessid
			} else {
				oldKvsSObj.Delete()
			}
		}
		if sessid == "" {
			sessid = newSessid(account, data.Body.Vid, ctx)
		}
		addLoginSession(ctx, data, nil, sessid, false, mirulib.IOT_VIDEO_CYLAN)
	} else if sessid != "" { // 使用sessid重登
		if oldKvsSObj == nil || (time.Now().Unix()-oldKvsSObj.LastLoginTime) > sessionTimeout {
			logger.Errorf("session timeout:%s", sessid)
			worker.MQSendErrorReply(ctx, worker.ErrorSessionTimeout)
			return
		}
		if oldKvsSObj.Account != account {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}

		addLoginSession(ctx, data, nil, sessid, false, mirulib.IOT_VIDEO_CYLAN)
	} else {
		logger.Errorf("invalid param, Account:%s, Password:%s sessid:%s", account, data.Body.Passwd, sessid)
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	ctx.Headers.Caller = sessid
	ctx.UpdateSess = true
	ctx.UpdateLoginOK = true
	ctx.Net = data.Body.Net

	workerMsg := worker.JCliBurnLoginRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body.Account = account
	workerMsg.Body.Sessid = sessid
	workerMsg.Body.SrsHttp = worker.GetCircleStrings(&srsHttpSeed, 1, conf.Base.Srs.Http)[0]

	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 韩国设备列表 JIDCliKoreaDevList
func (this *MQHandler) JIdCliKoreaDevList(ctx *worker.JsonWorkerContext) {
	var data worker.JsonHeader
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	datas := make([]worker.BurnDevDataInfo, 0)
	var koreaObjs []mirulib.TBCameraKorea
	mirulib.DBNO.Query(mirulib.GCameraKorea).Filter("snum", kvsSObj.Account).All(&koreaObjs)
	for _, obj := range koreaObjs {
		datas = append(datas,
			worker.BurnDevDataInfo{
				Sessid:           obj.Sessid,
				Sn:               obj.Cid,
				IMEI:             obj.IMEI,
				Mac:              obj.Mac,
				Os:               obj.Os,
				Net:              obj.Net,
				WebrtcPermitCode: obj.WebrtcPermitCode,
			})
	}

	workerMsg := worker.JCliBurnDevlistRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(ctx.JsonHeader)
	workerMsg.Body = datas
	worker.MQSendWorkMsg(ctx, workerMsg)
}

//通讯录设置 JIDCliDevAddressBookSet
func (this *MQHandler) JIdCliDevAddressBookSet(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevAddressBookSet
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if data.Body.Sn == "" || (data.Body.ShareSn == "" && data.Body.Account == "") {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	starDataFunc := func(star bool) int16 {
		if star {
			return 1
		}
		return 0
	}

	starIsLimit := func(cid string) bool {
		accountCount, _ := mirulib.DB.QueryDx(mirulib.GPhoneUserCamera, ctx.ClientMsg.DStr).Filter("cid", cid, "star", 1).Count()
		devCount, _ := mirulib.DB.QueryDx(mirulib.GCameraCamera, ctx.ClientMsg.DStr).Filter("cid", cid, "star", 1).Count()
		if int(accountCount+devCount) >= mirulib.MaxStarCount {
			worker.MQSendErrorReply(ctx, worker.ErrorShareStarExceedsLimit)
			return true
		}

		return false
	}

	flagList := aws3.ShiftBitEx(uint(data.Body.Flag), 16)
	if data.Body.ShareSn != "" { //设置针对设备的通讯录的 alias、head_photo_region、star时，需要传参 sn + share_sn
		if data.Body.Code != "" {
			var shareQuickData mirulib.KVSShareQuickData
			if mirulib.GetShareQuickInfo(data.Body.Code, &shareQuickData, ctx.ClientMsg.DStr) != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorShareQuickRandomTimeout)
				return
			}

			// kvsSObj.Account 是被分享的账号
			if kvsSObj.Account == shareQuickData.Account {
				worker.MQSendErrorReply(ctx, worker.ErrorShareToSelf)
				return
			}

			if _, err := worker.PucGet(kvsSObj.Account, data.Body.Sn); err != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorCIDNotBind)
				return
			}

			if mirulib.DB.QueryDx(mirulib.GCameraCamera, ctx.ClientMsg.DStr).Filter("cid", data.Body.Sn, "share_cid", shareQuickData.Sn).Exist() {
				worker.MQSendErrorReply(ctx, worker.ErrorShareAlready)
				return
			}

			if !worker.IsAddressBookDev(data.Body.Sn, ctx.ClientMsg.DStr) {
				worker.MQSendErrorReply(ctx, worker.ErrorShareNoAddressBookDev)
				return
			}

			// 是否在同一个区域
			kvsAdminPuObj := mirulib.GetKVSPU(shareQuickData.Account)
			kvsAdminPuObj.SetDebugStr(ctx.ClientMsg.DStr)
			if kvsAdminPuObj.Get() != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
				return
			}

			// 分享账号信息
			kvsPuObj := mirulib.GetKVSPU(kvsSObj.Account)
			kvsPuObj.SetDebugStr(ctx.ClientMsg.DStr)
			if kvsPuObj.Get() != nil {
				worker.MQSendErrorReply(ctx, worker.ErrorAccountNotExist)
				return
			}

			shareUrl := worker.GetServerUrlByCode(kvsPuObj.CountryCode)
			adminUrl := worker.GetServerUrlByCode(kvsAdminPuObj.CountryCode)
			if shareUrl[0] != adminUrl[0] {
				worker.MQSendErrorReply(ctx, worker.ErrorShareNotSameArea)
				return
			}

			mirulib.DelShareQuickInfo(data.Body.Code, ctx.ClientMsg.DStr)
			cid, shareCid := data.Body.Sn, shareQuickData.Sn
			account, shareAccount := kvsSObj.Account, shareQuickData.Account
			worker.CamCamAdd(cid, shareCid, account, shareAccount, data.Body.Alias, mirulib.PT_JFG_NEW, 0, kvsSObj.IotVideo, ctx.ClientMsg.DStr)
			worker.CamCamAdd(shareCid, cid, shareAccount, account, "", mirulib.PT_JFG_NEW, 0, kvsSObj.IotVideo, ctx.ClientMsg.DStr)
			worker.SaveAddressBookShareMsgBoth(cid, shareCid, account, shareAccount, true, kvsSObj.IotVideo)

			worker.SyncCliPushRefresh(account, ctx.Headers.Caller, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
			worker.SyncCliPushRefresh(shareAccount, "", worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
			worker.SyncDogPushRefresh(cid, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
			worker.SyncDogPushRefresh(shareCid, worker.JIDCliDevAddressBookList, ctx.ClientMsg.DStr)
			worker.MQSendErrorReply(ctx, worker.CodeOK)
			return
		}

		query := mirulib.DB.QueryDx(mirulib.GCameraCamera, ctx.ClientMsg.DStr).Filter("cid", data.Body.Sn, "share_cid", data.Body.ShareSn)
		if !query.Exist() {
			worker.MQSendErrorReply(ctx, worker.ErrorRecordNotExist)
			return
		}
		var updateArr []interface{}
		if flagList[0] {
			updateArr = append(updateArr, "remark", data.Body.Alias)
		}
		if flagList[1] {
			updateArr = append(updateArr, "pic_region", data.Body.HeadPhoto)
		}
		if flagList[2] {
			if starIsLimit(data.Body.Sn) {
				return
			}
			updateArr = append(updateArr, "star", starDataFunc(data.Body.Star))
		}

		if len(updateArr) > 0 {
			query.Update(updateArr...)
		}
	} else if data.Body.Account != "" { //设置针对账号的通讯录的star时，需要传参 sn + account
		kvsPucObj := mirulib.GetKVSPUC(data.Body.Sn, data.Body.Account)
		if kvsPucObj.Get() != nil {
			worker.MQSendErrorReply(ctx, worker.ErrorRecordNotExist)
			return
		}

		if flagList[0] {
			kvsPucObj.Remark = data.Body.Alias
		}
		if flagList[1] {
			kvsPucObj.PicRegion = data.Body.HeadPhoto
		}

		if flagList[2] {
			if starIsLimit(data.Body.Sn) {
				return
			}
			kvsPucObj.Star = starDataFunc(data.Body.Star)
		}

		kvsPucObj.Save(false)
	}

	worker.MQSendErrorReply(ctx, worker.CodeOK)
}

//通讯录列表 JIDCliDevAddressBookList
func (this *MQHandler) JIdCliDevAddressBookList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevAddressBookList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if mirulib.IsDog(ctx.Sess) {
		data.Body.Sn = ctx.Sess
	} else {
		if data.Body.Sn == "" {
			worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
			return
		}
	}

	kvsPucObj := mirulib.GetKVSPUC(data.Body.Sn, kvsSObj.Account)
	kvsPucObj.SetDebugStr(ctx.ClientMsg.DStr)
	if kvsPucObj.Get() != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorCIDNotBind)
		return
	}

	var bookInfo worker.DevAddressBookInfo
	bookInfo.AccountList = make([]worker.AddressBookInfo, 0)
	bookInfo.DevList = make([]worker.AddressBookInfo, 0)

	bookInfo.AccountList = append(bookInfo.AccountList, worker.AddressBookInfo{kvsSObj.Account, "", 0, false})
	// 查找分享的账号和主账号
	var pucObjs []mirulib.TBPhoneUserCamera
	mirulib.DB.Query(mirulib.GPhoneUserCamera).FilterEx("cid", data.Body.Sn).OrderBy("bind_time").All(&pucObjs)
	for _, obj := range pucObjs {
		if obj.Type == mirulib.PhoneUserCameraTypeShare {
			bookInfo.AccountList = append(bookInfo.AccountList, worker.AddressBookInfo{obj.Account, obj.Remark, obj.PicRegion, obj.Star > 0})
		} else {
			bookInfo.AccountList[0].Alias = obj.Remark
			bookInfo.AccountList[0].HeadPhoto = obj.PicRegion
			bookInfo.AccountList[0].Star = obj.Star > 0
		}
	}

	// 查找设备通讯录
	var camCamObjs []mirulib.TBCameraCamera
	mirulib.DB.Query(mirulib.GCameraCamera).FilterEx("cid", data.Body.Sn).OrderBy("bind_time").All(&camCamObjs)
	for _, obj := range camCamObjs {
		bookInfo.DevList = append(bookInfo.DevList, worker.AddressBookInfo{obj.ShareCid, obj.Remark, obj.PicRegion, obj.Star > 0})
	}

	workerMsg := worker.JCliDevAddressBookListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	bookInfo.Sn = data.Body.Sn
	bookInfo.Time = time.Now().Unix()
	workerMsg.Body = bookInfo
	worker.MQSendWorkMsg(ctx, workerMsg)
}

//通讯录删除 JIDCliDevAddressBookDel
func (this *MQHandler) JIdCliDevAddressBookDel(ctx *worker.JsonWorkerContext) {
	var data worker.JCliDevAddressBookDel
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	if data.Body.Sn == "" || data.Body.ShareSn == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return
	}

	var camCamObj mirulib.TBCameraCamera
	query := mirulib.DB.QueryDx(mirulib.GCameraCamera, ctx.ClientMsg.DStr).Filter("cid", data.Body.Sn, "share_cid", data.Body.ShareSn)
	err = query.First(&camCamObj)
	if err != nil {
		worker.MQSendErrorReply(ctx, worker.ErrorRecordNotExist)
		return
	}
	query.DeleteEx()
	mirulib.DB.QueryDx(mirulib.GCameraCamera, ctx.ClientMsg.DStr).Filter("cid", data.Body.ShareSn, "share_cid", data.Body.Sn).DeleteEx()
	worker.SaveAddressBookShareMsgBoth(camCamObj.Cid, camCamObj.ShareCid, camCamObj.Account, camCamObj.ShareAccount, false, kvsSObj.IotVideo)

	worker.MQSendErrorReply(ctx, worker.CodeOK)
}

//获取特定属性的设备列表 JIDCliAttrDevList
func (this *MQHandler) JIdCliAttrDevList(ctx *worker.JsonWorkerContext) {
	var data worker.JCliAttrDevList
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	datas, _, _ := worker.PucSelectAttr(kvsSObj.Account, 0, 0, data.Body.Search, ctx.ClientMsg.DStr, kvsSObj.IotVideo, data.Body.Flag, data.Body.GetShare, data.Body.FilterSn)
	list := make([]worker.AttrDevInfo, 0)
	for _, params := range datas {
		cid := params["cid"].(string)
		os := worker.CidGetOs(cid, true)
		iotVideo, _ := strconv.Atoi(params["iot_video"].(string))
		list = append(list, worker.AttrDevInfo{
			Sn:           cid,
			SnThird:      params["sn"].(string),
			Alias:        params["alias"].(string),
			ShareAccount: params["share_account"].(string),
			Os:           os,
			IotVideo:     int16(iotVideo),
		})
	}

	workerMsg := worker.JCliAttrDevListRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Flag = data.Body.Flag
	workerMsg.Body.GetShare = data.Body.GetShare
	workerMsg.Body.List = list
	worker.MQSendWorkMsg(ctx, workerMsg)

}

// 重启设备 JIDCliPushReboot
func (this *MQHandler) JIdCliPushReboot(ctx *worker.JsonWorkerContext) {
	var data worker.JCliPushReboot
	_, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}

	pushMsg := worker.GetJsonHeader(worker.JIDDevPushReboot, "server")
	worker.SendWorkMsgBySessid(data.Body.Cid, pushMsg)
}
