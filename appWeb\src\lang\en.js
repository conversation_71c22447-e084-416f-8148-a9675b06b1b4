export default {
  mgtPlat: 'MIRU',
  mgtPlatTip: 'Real-time Video Surveillance System',
  scanCodeToLogIn: 'Scan code to log in',
  scanCodeTip: 'Use the APP bound with the device to scan the code to log in',
  qrcodeExpired: 'QR code is invalid',
  username: '<PERSON>rna<PERSON>',
  password: 'Password',
  passwordTip1: 'Please enter your password',
  rememberPwd: 'Remember your password',
  pleaseEnterAccount: 'Please enter your account number',
  pleaseEnterCorrectPwd: 'Password must be 6 to 128 characters!',
  logIn: 'LogIn',
  account: 'Account',
  accountTip: 'Enter 6-20 digits or letters.',
  pleaseEnterCorrectPwdLen:
    'Password length must be greater than or equal to 6 digits',
  pleaseEnterCorrectPwd18: 'Password must be 6 to 18 characters!',
  verificationCode: 'Verification Code',
  verificationCodeTip: 'Please enter the verification code.',
  verificationCodeError: 'Verification code error',

  home: 'Home',
  deviceManagement: 'Device Management',
  userManagement: 'User Management',
  systemSetting: 'System Setting',
  operationLogs: 'Operation Logs',

  quitTitle: 'Sign Out',
  quitTip: 'Are you sure you want to quit?',
  submit: 'Submit',
  confirm: 'Confirm',
  sure: 'Confirm',
  cancel: 'Cancel',
  search: 'Search',
  inquire: 'Search',
  nodata: 'No Data',
  cannotEmpty: 'Can not be empty',
  operate: 'Operation',
  modify: 'Edit',
  export: 'Export',
  edit: 'Edit',
  delete: 'Delete',
  sureToDelete: 'Are you sure you want to delete?',
  deleteSuccess: 'Successfully deleted',
  addSuccess: 'Added successfully',
  addFail: 'Add failed',
  remove: 'Remove',
  sureToRemove: 'Are you sure you want to remove?',
  removeSuccess: 'Removed successfully',
  editSuccess: 'Edited successfully',
  tip: 'Tip',
  noMore: 'No more',
  getLogSuccess: 'Get the log successfully',
  all: 'All',
  modifySuccess: 'Successfully modified',
  noData: 'Do data',
  refresh: 'Refresh',
  save: 'Save',
  to: 'To',
  startDate: 'Start Date',
  endDate: 'End Date',
  pleaseSelect: 'Please select',

  errCode120: 'Unknown error',
  errCode121: 'Database error',
  errCode122: 'Session timeout',
  errCode123: 'Message format error',
  errCode124:
    'The message rate exceeds the limit, please control the reasonable flow rate (100 messages per second)',
  errCode125: 'Wrong parameters',
  errCode126: 'An error occurred when calling the robot API',
  errCode127: 'vid vkey error',
  errCode128: 'Calling robot API returns data error',
  errCode129: 'Not signed in, need to sign in to perform this action',
  errCode161: 'Account or password incorrect',
  errCode165: 'The account is already signed in to another device.',
  errCode200: 'CID does not exist',
  errCode202: 'The device alias already exists',
  errCode203: 'The device is not bound',
  errCode204: 'The device is already bound to another account',
  errCode205: 'Device verification codes do not match',
  errCode206: 'The device is bound by itself',
  errCode214: 'The device has already been bound by another APP',
  errCode215: 'The device is not online',
  errCode290: 'Random code does not exist',
  errCode291: 'The random code has expired',
  errCode292: 'Status is incorrect',
  errCode401: 'Please log in again',
  errCode500: 'Server error',
  errCode1000: 'Request error',
  errCode1001: 'Request error',
  errCode1002: 'Permission denied!',
  errCode1004: 'Туташуу катасы',
  errCode1100: 'Duplicate Name',
  errCode1101: 'Account already exists',
  errCode1102: 'The current password is incorrect',
  errCode2008: 'During SD card formatting',
  errCode2011: 'SD card reading failed',
  errCode2030: 'SD card historical video has been read',
  errCode2031: 'Failed to read historical video from SD card',
  errCode2032: 'Failed to read SD card historical video card',
  timeout: 'Network error',
  networkError: 'Network error',

  allGroup: 'All Group',
  noDevice: 'No Device',
  online: 'Online',
  offline: 'Offline',
  deviceLoading: 'Loading device list···',
  videoLoading: 'Loading video···',
  failPlay: 'Play failed',
  deviceOffline: 'Device Offline',
  noHistoricalVideo: 'The device has no historical video',
  noOssVideo: 'The device has no cloud storage',
  retry: 'Retry',
  incall: 'In call···',
  screen1: '1-screen',
  screens4: '4-screen',
  screens9: '9-screen',
  screens16: '16-screen',
  screens25: '25-screen',
  liveVideo: 'Live Video',
  cloudStorage: 'Cloud Storage',
  playback: 'Play Back',
  todayVideoOver: 'Video of today is over',
  movementDetected: 'Motion detected',
  soundDetected: 'Sound detected',
  humanDetected: 'Humanoid detected',
  noAudioEnabled: 'Microphone permission is not turned on',
  alarmMessages: 'Alarm messages',
  noMessages: 'No news',
  chooseDevice: 'Please select a device',
  exceedMax: 'The current maximum number of viewers has been reached',
  previousPage: 'Previous page',
  nextPage: 'Next page',
  carousel: 'Carousel',
  refreshList: 'Refresh List',
  refreshListTipCon: 'Are you sure you want to refresh the list?',
  refreshListTipCon1:
    'Note: After refreshing the list, the first page of device screens will be played.',
  refreshSuccess: 'Refreshed successfully',
  rebootDevice: 'Restart Device',
  rebootDeviceTipCon: 'Are you sure you want to restart the device?',
  scrolledToEnd: 'Already scrolled to the bottom',
  clickSingleDeviceTip: 'Carousel status cannot click device.',

  addGroup: 'Add group',
  deviceGroupLoading: 'Loading device group list···',
  addDevice: 'Add device',
  strategicAttributes: 'Strategic Attributes',
  bulkDelete: 'Bulk delete',
  status: 'Status',
  cid: 'Device CID',
  versionNumber: 'Version number',
  deviceNickname: 'Device nickname',
  group: 'Group',
  strategy: 'Strategy',
  editGroup: 'Edit group',
  groupName: 'Group name',
  groupNumber: 'Group number',
  authorizedUser: 'Authorized user',
  groupNameLimit: 'Length limit 20 characters',
  delGroup: 'Delete a group',
  editDevice: 'Edit the device',
  sn: 'Device SN',
  snEnter: 'Please enter SN',
  snEnterTip: 'Please enter a valid 12-digit SN code',
  deviceVerificationCode: 'Device verification code',
  deviceCodeEnter: 'Please enter the verification code',
  deviceNameEnter: 'Please enter a device nickname',
  deleteDevice: 'Delete the device',
  bulkDelDeviceTip: 'Please tick the device you want to delete',
  device: 'Device',
  deviceSearchTip: 'Device CID/Device nickname',
  delGroupHasDeviceTip:
    'There are devices in the group, so it cannot be deleted',

  nickName: 'Nick Name',
  mark: 'Mark',
  addUser: 'Add User',
  resetPassword: 'Reset Password',
  inputLengthLimit: 'Length limit {limit} characters',
  editUser: 'Edit User',
  deleteUser: 'Delete User',
  sureToReset: `Are you sure you want to reset the password to "123456"?`,
  bulkDelUserTip: 'Please tick the user you want to delete',
  userLimitTip: 'Limit 5 users',

  modifyPassword: 'Modify Password',
  passwordNotMatching: 'Password does not match',
  oldPassword: 'Current Password',
  newPassword: 'New Password',
  checkPassword: 'Confirm Password',
  passwordSame: 'The new password is the same as the old password',

  operationTime: 'Operation Time',
  operationType: 'Operation Type',
  editDeviceGroup: 'Edit Device Group',
  operationContent: 'Operation Content',

  saveVideo: 'Save video',
  time: 'Time',
  startTime: 'Start time',
  endTime: 'End time',
  empty: 'Clear',
  timerangeexceeds:
    'The selection range exceeds the limit. Please delete videos in Video Management or make a new selection!',
  novideo: 'No videos found in the current time range, please select again!',
  videomanagement: 'Video Management',
  selectgroup: 'Please select a group',
  allselect: 'Select All',
  download: 'Download',
  downloading: 'Downloading...',
  downloadSuccess: 'Download successful',
  downloadFailed: 'Download failed',
  saveVideoSuccess: 'Save video successfully',
  timeLimit: 'Up to 100 minutes of video can be saved',
  Uploading: 'File Uploading',
  warnings: 'Warning!',
  ok: 'OK',
  cancelVideoSave: 'Cancel saving the video',
  cancelVideoSaveTip: 'Are you sure you want to cancel saving the video?',
  loading: 'Loading...',
  minute: 'min',

  permissionLevel: 'Permission Level',
  manager: 'Manager',
  guest: 'Guest'
}
