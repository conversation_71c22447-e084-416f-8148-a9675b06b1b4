<template>
  <div class="login-container">
    <div class="headBox">
      <div class="headLeftBox">
        <img class="logo" src="../../assets/img/main_logo.png">
        <div>
          <div class="platName">{{ $t('mgtPlat') }}</div>
          <div class="platIntro">{{ $t('mgtPlatTip') }}</div>
        </div>
      </div>
      <div class="headRight">
        <lang-select class="set-language" @handleSetLanguage="handleSetLanguage"/>
      </div>
    </div>
    <div class="contentBox">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="formBox">
        <div class="mainLogoBox marginAuto marBtm20">
          <img src="../../assets/img/main_logo.png">
        </div>
        <el-form-item prop="username" class="marBtm20">
          <el-input
            v-model="loginForm.username"
            :placeholder="$t('account')"
            :maxlength="20"
            name="username"
            type="text"
            auto-complete="on"
          />
        </el-form-item>

        <el-form-item prop="password" class="marBtm20">
          <el-input
            v-if="showPwd"
            v-model="loginForm.password"
            :placeholder="$t('password')"
            :maxlength="18"
            type="text"
            name="password"
            auto-complete="on"
            @keyup.enter.native="handleLogin">
            <i slot="suffix" class="eyeStyle viewIcon" @click="changeEye('close')"/>
          </el-input>
          <el-input
            v-else
            v-model="loginForm.password"
            :placeholder="$t('password')"
            :maxlength="18"
            type="password"
            name="password"
            auto-complete="on"
            @keyup.enter.native="handleLogin">
            <i slot="suffix" class="eyeStyle eyeIcon" @click="changeEye('open')"/>
          </el-input>
        </el-form-item>

        <el-form-item prop="verification_code" class="marBtm20">
          <el-input
            v-model="loginForm.verification_code"
            :maxlength="4"
            :placeholder="$t('verificationCode')"
            type="text"
            @keyup.enter.native="handleLogin">
            <i slot="suffix"><captcha ref="captchaImg" :length="4" :width="87" :height="36" class="captchaBox pointer" @captchaText="getCaptchaText" /></i>
          </el-input>
        </el-form-item>

        <el-form-item class="formItemBox marBtm20">
          <el-checkbox v-model="loginForm.saveUserInfoEnable">{{ $t('rememberPwd') }}</el-checkbox>
        </el-form-item>

        <el-button :loading="loading" :disabled="loginForm.username.trim() === '' || loginForm.password === ''" :class="{primaryDisabledBtn: loginForm.username.trim() === '' || loginForm.password === ''}" type="primary" class="loginBtn primaryBtn" @click.native.prevent="handleLogin">{{ $t('logIn') }}</el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
import { validAZNumber, validPwdKo } from '@/utils/validate'
import Captcha from '@/components/Captcha'
import LangSelect from '@/components/LangSelect'
import { getLanguage } from '@/utils/auth'
import { setToken } from '@/utils/auth'
import { loginAddr } from '@/api/login'
import { encryption, decryption } from '@/utils/jiami'
import Cookies from 'js-cookie'
import tabManager from '@/utils/tabManager'
export default {
  name: 'Login',
  components: { LangSelect, Captcha },
  data() {
    const validateAccount = (rule, value, callback) => {
      if (value.trim() === '') {
        callback(new Error(this.$t('pleaseEnterAccount')))
      } else if (value.trim().length < 6 || value.trim().length > 20 || !validAZNumber(value.trim())) {
        callback(new Error(this.$t('accountTip')))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.trim().length < 6 || value.trim().length > 18) {
        callback(new Error(this.$t('pleaseEnterCorrectPwd18')))
      // } else if (!validPwdKo(value.trim())) {
      //   callback(new Error(this.$t('pleaseEnterCorrectPwd18')))
      } else {
        callback()
      }
    }
    return {
      language: getLanguage(),
      loginForm: {
        username: '',
        password: '',
        verification_code: '',
        saveUserInfoEnable: false
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateAccount }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        verification_code: [{ required: true, message: this.$t('verificationCodeTip'), trigger: 'blur' }]
      },
      showPwd: false,
      captcha_text: '',
      loading: false,
      code_temp: this.$websocket.generatedCode()
    }
  },
  mounted() {
    // 判断是否记住了密码邮箱
    if (Cookies.get('lanSavedUserInfo') == 'true') {
      this.loginForm.saveUserInfoEnable = true
      if (localStorage && localStorage.lanUserName) {
        this.loginForm.username = decryption(localStorage.lanUserName)
        this.loginForm.password = decryption(localStorage.lanPassword)
      }
    }

    // 检查是否有共享的 sessid，如果有则尝试自动登录
    this.checkAndAutoLogin()

    // 监听其他 Tab 的登录事件
    this.setupCrossTabLoginListener()
  },
  beforeDestroy() {
    // 清理事件监听器
    this.$bus.$off('autoLoginFromAnotherTab')
  },
  methods: {
    handleSetLanguage(val) {
      this.language = val
    },
    // 检查并自动登录
    checkAndAutoLogin() {
      const sharedSessid = tabManager.getSessid()
      const sharedAccount = tabManager.getAccount()
      if (sharedSessid && sharedSessid.trim() !== '' && sharedAccount && sharedAccount.trim() !== '') {
        console.log('Found shared sessid and account, attempting auto login:', sharedSessid, sharedAccount)
        this.autoLoginWithSessid(sharedSessid, sharedAccount)
      }
    },
    // 使用 sessid 自动登录
    autoLoginWithSessid(sessid, account) {
      this.loading = true
      const ws_server = process.env.WS_SERVER

      const post_data = {
        'language_type': 0,
        'account': account, // 使用保存的账号
        'passwd': '', // 使用 sessid 登录时不需要密码
        'os': 1,
        'net': this.getNetworkType().type,
        'name': this.getNetworkType().name,
        'bundle_id': '**********',
        'device_token': 'token' + this.code_temp,
        'sessid': sessid, // 使用已有的 sessid
        'vid': '0001',
        'vkey': 'DcWP670PNfCtPIETQk03lEzbt6qRDRDy'
      }

      this.$websocket.initWebSocket('', ws_server)
      this.checkWebsocket(post_data)

      this.$bus.$once('cli_miru_login_rsp', response => {
        console.log('Auto login response:', response)
        sessionStorage.wsServer = ws_server

        if (typeof (response) === 'number') {
          // 自动登录失败，清除无效的 sessid
          console.log('Auto login failed, clearing invalid sessid')
          tabManager.clearSessid()
          this.$websocket.closeWebsocket()
          this.loading = false
          return
        }

        // 自动登录成功
        this.handleLoginSuccess(response)
      })
    },
    // 处理登录成功的通用逻辑
    handleLoginSuccess(response) {
      sessionStorage.account_type = response.body.account_type
      sessionStorage.username = response.body.account
      sessionStorage.account_alias = response.body.alias
      this.$store.commit('SET_TOKEN', response.body.sessid)
      setToken(response.body.sessid)
      sessionStorage.videoUrl = JSON.stringify(response.body.srs_http)

      // 保存 sessid 和 account 到 localStorage 供其他 Tab 共享
      tabManager.setSessid(response.body.sessid, response.body.account)

      this.saveUserInfo()
      this.$router.push({ path: '/home' })
      this.loading = false

      // 防止页面后退
      history.pushState(null, null, document.URL)
      window.addEventListener('popstate', function() {
        history.pushState(null, null, document.URL)
      })
    },
    checkWebsocket(postData) {
      // 0 - 表示连接尚未建立，1 - 表示连接已建立，可以进行通信，2 - 表示连接正在进行关闭，3 - 表示连接已经关闭或者连接不能打开
      var state = this.$websocket.socket.readyState
      console.log(state)
      if (state === 0) {
        setTimeout(() => {
          this.checkWebsocket(postData)
        }, 300)
      } else if (state === 1) {
        this.$websocket.webSocketSend('cli_miru_login', postData)
      } else {
        console.log('???')
        console.log(state)
      }
    },
    changeEye(val) {
      if (val === 'open') {
        this.showPwd = true
      } else {
        this.showPwd = false
      }
    },
    getCaptchaText(val) {
      this.captcha_text = val
    },
    saveUserInfo() {
      if (this.loginForm.saveUserInfoEnable) {
        Cookies.set('lanSavedUserInfo', 'true', {
          expires: 30
        })
        localStorage.lanUserName = encryption(this.loginForm.username)
        localStorage.lanPassword = encryption(this.loginForm.password)
      } else {
        // 删除cookie
        Cookies.set('lanSavedUserInfo', 'false', {
          expires: -1
        })
        localStorage.lanUserName = ''
        localStorage.lanPassword = ''
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          if (this.captcha_text.toLowerCase() !== this.loginForm.verification_code.toLowerCase()) {
            this.$message.error(this.$t('verificationCodeError'))
            return
          }
          this.loading = true

          // 检查是否有缓存的sessid和account
          const cachedSessid = tabManager.getSessid()
          const cachedAccount = tabManager.getAccount()

          if (cachedSessid && cachedAccount) {
            console.log('发现缓存的sessid和account，尝试使用缓存登录')
            this.tryLoginWithCache(cachedSessid, cachedAccount)
          } else {
            console.log('没有缓存，使用账号密码登录')
            const ws_server = process.env.WS_SERVER
            this.toLinkWsLogin(ws_server)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    // 尝试使用缓存的sessid和account登录
    tryLoginWithCache(cachedSessid, cachedAccount) {
      console.log('使用缓存登录:', { sessid: cachedSessid, account: cachedAccount })

      const ws_server = process.env.WS_SERVER
      const post_data = {
        'language_type': 0,
        'account': cachedAccount,
        'passwd': '', // 使用sessid登录时密码为空
        'os': 1,
        'net': this.getNetworkType().type,
        'name': this.getNetworkType().name,
        'bundle_id': '**********',
        'device_token': 'token' + this.code_temp,
        'sessid': cachedSessid, // 使用缓存的sessid
        'vid': '0001',
        'vkey': 'DcWP670PNfCtPIETQk03lEzbt6qRDRDy'
      }

      this.$websocket.initWebSocket('', ws_server)
      this.checkWebsocket(post_data)

      this.$bus.$once('cli_miru_login_rsp', response => {
        console.log('缓存登录响应:', response)
        sessionStorage.wsServer = ws_server

        if (typeof (response) === 'number') {
          console.log('缓存登录失败，清除缓存并使用账号密码登录')
          this.handleLoginFailure()
          // 缓存登录失败，回退到账号密码登录
          this.toLinkWsLogin(ws_server)
          return
        }

        // 缓存登录成功
        console.log('缓存登录成功')
        this.handleLoginSuccess(response)
      })
    },

    toLinkWsLogin(ws_server) {
      const post_data = {
        'language_type': 0,
        'account': this.loginForm.username.trim(),
        'passwd': this.$md5(this.loginForm.password),
        'os': 1,
        'net': this.getNetworkType().type,
        'name': this.getNetworkType().name,
        'bundle_id': '**********',
        'device_token': 'token' + this.code_temp,
        'sessid': '',
        'vid': '0001',
        'vkey': 'DcWP670PNfCtPIETQk03lEzbt6qRDRDy'
      }
      this.$websocket.initWebSocket('', ws_server)
      // 要判断websocket是否连接状态
      this.checkWebsocket(post_data)
      this.$bus.$once('cli_miru_login_rsp', response => {
        console.log('账号密码登录响应:', response)
        sessionStorage.wsServer = ws_server
        if (typeof (response) === 'number') {
          console.log('账号密码登录失败')
          this.handleLoginFailure()
          this.$websocket.closeWebsocket()
          this.loading = false
          this.$refs.captchaImg.generateCaptcha()
          return
        }
        // 使用统一的登录成功处理方法
        console.log('账号密码登录成功')
        this.handleLoginSuccess(response)
      })
    },

    // 处理登录失败，清除缓存
    handleLoginFailure() {
      console.log('登录失败，清除sessid和account缓存')

      // 清除localStorage中的缓存
      tabManager.clearSessid(false) // 不触发登出事件，因为这是登录失败

      // 清除sessionStorage中的相关数据
      sessionStorage.removeItem('token')
      sessionStorage.removeItem('username')
      sessionStorage.removeItem('account_type')
      sessionStorage.removeItem('account_alias')
      sessionStorage.removeItem('videoUrl')

      // 清除store中的token
      this.$store.commit('SET_TOKEN', '')

      console.log('缓存清除完成')
    },

    getNetworkType() {
      var ua = navigator.userAgent
      var networkStr = ua.match(/NetType\/\w+/) ? ua.match(/NetType\/\w+/)[0] : 'NetType/other'
      networkStr = networkStr.toLowerCase().replace('nettype/', '')
      var networkType = 1
      switch (networkStr) {
        case 'wifi':
          networkType = 1
          name = 'wifi'
          break
        case '5g':
          networkType = 5
          name = '5g'
          break
        case '4g':
          networkType = 4
          name = '4g'
          break
        case '3g':
          networkType = 3
          name = '3g'
          break
        case '3gnet':
          networkType = 3
          name = '3g'
          break
        case '2g':
          networkType = 2
          name = '2g'
          break
        default:
          networkType = 10
          name = 'wired'
      }
      return {
        type: networkType,
        name: name
      }
    },
    setupCrossTabLoginListener() {
      // 监听来自其他 Tab 的自动登录事件
      this.$bus.$on('autoLoginFromAnotherTab', (newSessid) => {
        console.log('Received auto login event from another tab, sessid:', newSessid)

        // 检查当前是否正在加载中，如果是则忽略
        if (this.loading) {
          console.log('Login already in progress, ignoring auto login event')
          return
        }

        // 获取对应的 account
        const account = tabManager.getAccount()
        if (!account) {
          console.log('No account found for auto login, ignoring event')
          return
        }

        console.log('Starting auto login with sessid from another tab')
        this.autoLoginWithSessid(newSessid, account)
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  .headBox {
    width: calc(100% - 40px);
    padding: 0 20px;
    height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--color-neutral-600);
    box-shadow: 0px 10px 10px 0px rgba(0,0,0,0.40);
  }
  .headLeftBox {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .logo {
    width: 50px;
    height: 50px;
  }
  .platName {
    font-size: var(--font-size-large);
    color: var(--color-neutral-100);
    line-height: 25px;
    font-weight: bold;
  }
  .platIntro {
    font-size: var(--font-size-small);
    color: var(--color-neutral-100);
    line-height: 17px;
  }
  .headRight {
    text-align: right;
  }
  .set-language {
    font-size: var(--font-size-normal);
    color: var(--color-neutral-200);
  }
  .contentBox {
    height: calc(100vh - 70px);
    background: url("../../assets/img/background.jpg") no-repeat;
    background-size: 100% 100%;
  }
  .formBox{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--color-neutral-600);
    border-radius: 20px;
    color: var(--color-neutral-200);
    width: 400px;
    padding: 40px;
  }
  .mainLogoBox {
    width: 80px;
    height: 80px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .formItemBox /deep/.el-form-item__content{
    line-height: 20px;
  }
  .loginBtn{
    width:100%;
    border: none;
  }
  /deep/ .el-input__inner{
    height: 50px;
    line-height: 50px;
    font-size: var(--font-size-regular);
    color: var(--color-neutral-100);
    border: 1px solid var(--color-neutral-500);
    background: var(--color-neutral-600);
    :focus {
      background: var(--color-neutral-600);
    }
  }
  /deep/ .el-input__suffix{
    right: 0;
  }
  /deep/ .el-form-item__error {
    position: inherit;
  }
  .eyeStyle{
    margin: 14px 20px;
    display: inline-block;
    width: 22px;
    height: 22px;
    cursor: pointer;
  }
  .viewIcon {
    background: url("../../assets/img/view.png");
    background-size: 100% 100%;
    &:hover {
      background: url("../../assets/img/viewHover.png");
      background-size: 100% 100%;
    }
  }
  .eyeIcon {
    background: url("../../assets/img/eye.png");
    background-size: 100% 100%;
    &:hover {
      background: url("../../assets/img/eyeHover.png");
      background-size: 100% 100%;
    }
  }
  /deep/ .el-checkbox__label {
    font-size: var(--font-size-normal);
    color: var(--color-neutral-200);
    padding-left: 6px;
  }
  .captchaBox {
    margin: 7px 20px 7px 0;
  }
}
</style>
