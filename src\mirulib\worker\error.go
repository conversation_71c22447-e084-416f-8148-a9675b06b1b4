package worker

import "fmt"

const CodeOK = 0

// P2P 错误
const (
	ErrorP2PDns                             = 1
	ErrorP2PSocket                          = 2
	ErrorP2PCallerRelay                     = 3
	ErrorP2PCallerStun                      = 4
	ErrorP2PCalleeStun                      = 5
	ErrorP2PCalleeWaitCallerCheckNetTimeOut = 6
	ErrorP2PPeerTimeOut                     = 7
	ErrorP2PUserCancel                      = 8
	ErrorP2PConnectionCheck                 = 9
	ErrorP2PChannel                         = 10
	ErrorP2PDisconetByUser                  = 11
)

// OSS文件操作
const (
	// ErrorUploadFileFailed 上传文件失败
	ErrorUploadFileFailed = 24
	// ErrorCopyFileFailed 拷贝文件失败
	ErrorCopyFileFailed = 25
	// ErrorUploadFileToOSSFailed 上传文件到OSS失败
	ErrorUploadFileToOSSFailed = 26
)

//直播类
const (
	// ErrorVideoPeerNotExist 对端不在线。
	ErrorVideoPeerNotExist = 100
	// ErrorVideoPeerDisconnect 对端断开
	ErrorVideoPeerDisconnect = 101
	// ErrorVideoPeerInConnect 正在查看中.
	ErrorVideoPeerInConnect = 102
	//ErrorVideoPeerNotLogin 本端未登陆
	ErrorVideoPeerNotLogin = 103
)

const (
	// ErrorUnknown 未知错误
	ErrorUnknown = 120
	// ErrorDataBase 数据库错误
	ErrorDataBase = 121
	// ErrorSessionTimeout  会话超时
	ErrorSessionTimeout = 122
	// ErrorInvalidMsg 格式不对
	ErrorInvalidMsg = 123
	// ErrorMsgRateExceedLimit 消息速率超过限制，请控制合理流速（100个每秒）
	ErrorMsgRateExceedLimit = 124
	// ErrorInvalidParameter 参数不对
	ErrorInvalidParameter = 125
	// ErrorCallRobotApi 调用robot API出现错误
	ErrorCallRobotApi = 126
	// ErrorInvalidVKey vid vkey 错误
	ErrorInvalidVKey = 127
	// ErrorCallRobotRet 调用robot API返回数据错误
	ErrorCallRobotRet = 128
	// ErrorNotLogin 未登录，需要登录才能进行此操作
	ErrorNotLogin = 129
	// ErrorTooFrequenly 操作太频繁
	ErrorTooFrequenly = 130
	// ErrorWaitLogin 等待登录
	ErrorWaitLogin = 131
	// ErrorCidIsNull cid为空
	ErrorCidIsNull = 132
)

// 设备端鉴权。
const (
	// ErrorCIDExceedQuota 厂家CID达到配额。关联消息：注册。
	ErrorCIDExceedQuota = 140
	// ErrorCIDSNVerifyFailed SN签名验证失败。关联消息：登陆。
	ErrorCIDSNVerifyFailed = 141
	ErrorPublicKeyNotExist = 142
	ErrorCIDIsDuplicate    = 143
	// ErrorCIDNotRegistered cid未注册
	ErrorCIDNotRegistered  = 144
	ErrorCIDTimezoneEmpty  = 145
	ErrorCIDIotVideoWecall = 146 // 设备未开通微信服务
)

// 客户端登陆类.
const (
	// ErrorLoginInvalidVKey vid, bundleID, vkey校验失败。
	ErrorLoginInvalidVKey = 160
	// ErrorLoginInvalidPass 帐号或密码错误。
	ErrorLoginInvalidPass = 161
	// 第三方帐号登陆： access_token 验证失败。
	ErrorOpenLoginInvalidToken = 162
	// SDK正在初始化，请等待
	ErrorIniting = 163
	// 第三方帐号登陆： 暂时不支持此方式。
	ErrorOpenLoginTypeNotSupport = 164
	// 账号已在其他设备上登录。
	ErrorLoginDenied = 165
)

// 客户端帐号类.
const (
	// ErrorCodeNotMatch 验证码错误。
	ErrorCodeNotMatch = 180
	// ErrorCodeTimeout 验证码超时。
	ErrorCodeTimeout = 181
	// ErrorAccountNotExist 帐号不存在。
	ErrorAccountNotExist = 182
	// ErrorAccountAlreadyExist 帐号已存在。
	ErrorAccountAlreadyExist = 183
	// ErrorSamePass 原始密码与新密码相同。关联消息：修改密码。
	ErrorSamePass = 184
	// ErrorInvalidPass 原密码错误。关联消息：修改密码。
	ErrorInvalidPass = 185
	// ErrorPhoneExist  此手机号码已被绑定。关联消息：帐号、手机号、邮箱绑定。
	ErrorPhoneExist = 186
	// ErrorEmailExist 此邮箱已被绑定。关联消息：帐号、手机号、邮箱绑定。
	ErrorEmailExist = 187
	// 手机号码不合规
	ErrorIsNotPhone = 188
	// 邮箱账号不合规
	ErrorIsNotEmail = 189
	// 忘记密码时，邮箱和手机号不存在时报错
	ErrorInvalidPhoneNumber = 190
	// 第三方账号设置密码超时
	ErrorSetPassTimeout = 191
	// 十分钟内获取验证码超过3次
	ErrorGetCodeTooFrequent = 192
	// ErrorSendSmsFailed 短信接口调用失败
	ErrorSendSmsFailed = 193
	// 帐号下有设备(含被分享设备)未删除
	ErrorHasDevice = 194
	// 不是邮箱或手机号
	ErrorNotEmailOrPhone = 195
	// 操作超出限制，目前用于区域切换操作
	ErrorExceedLimit = 196
	// 小程序注册的账号，需要完善密码
	ErrorPleaseCompletePassword = 197
	// 相同的手机号
	ErrorSamePhone = 198
	// 相同的邮箱
	ErrorSameEmail = 199
)

// 客户端绑定设备类.
const (
	// ErrorCIDNotExist CID不存在。关联消息：客户端绑定。
	ErrorCIDNotExist = 200
	//ErrorCIDBinding 绑定中，正在等待摄像头上传随机数与CID关联关系，随后推送绑定通知
	ErrorCIDBinding = 201
	// ErrorCIDAliasExist 设备别名已存在。
	ErrorCIDAliasExist = 202
	// ErrorCIDNotBind 设备未绑定，不可操作未绑定设备。
	ErrorCIDNotBind = 203
	// 设备已经被其他账号绑定。
	ErrorCIDBinded = 204
	// 设备验证码不匹配
	ErrorInvalidCode = 205
	// 设备被自己绑定
	ErrorCIDBindSelf = 206
	// APP上报与设备上报cid不匹配
	ErrorCIDDiffer = 207
	// ErrorCIDBindTimeout 绑定等待超时
	ErrorCIDBindTimeout = 208
	// ErrorCIDBindErrorCidLen 通过SN vid查询到已注册的cid长度错误
	ErrorCIDBindErrorCidLen = 209
	// ErrorCIDBindVidNotExist vid不存在
	ErrorCIDBindVidNotExist = 210
	// ErrorCIDBindPidNotExist pid不存在
	ErrorCIDBindPidNotExist = 211
	// ErrorCIDBindMacNotExist mac不存在
	ErrorCIDBindMacNotExist = 212
	// ErrorCIDBindCancel 绑定取消
	ErrorCIDBindCancel = 213
	// ErrorCIDBindForOtherClient 设备已经被其他APP端执行绑定操作
	ErrorCIDBindForOtherClient = 214
	// ErrorCIDOffline 设备不在线
	ErrorCIDOffline = 215
	// ErrorCIDNoDevToCall 没有其他设备可以呼叫
	ErrorCIDNoDevToCall = 216
	// ErrorCIDNotSupportDP 该版本不支持此DP，请升级设备
	ErrorCIDNotSupportDP = 217
	// ErrorInvalid4GCard 非法的4G卡
	ErrorInvalid4GCard = 218
	// ErrorCIDNotPermit 操作不允许
	ErrorCIDNotPermit = 219
)

// 客户端分享设备类.
const (
	// ErrorShareInvalidAccount 此帐号还没有注册。
	ErrorShareInvalidAccount = 220
	// ErrorShareAlready 此帐号已经分享。
	ErrorShareAlready = 221
	// ErrorShareToSelf 您不能分享给自己。
	ErrorShareToSelf = 222
	//ErrorShareExceedsLimit 设备分享，被分享账号不能超过5个。
	ErrorShareExceedsLimit = 223
	//ErrorShareNotSameArea 设备分享账号不在同一个区域
	ErrorShareNotSameArea = 224
	//ErrorShareNotPermit 操作不允许
	ErrorShareNotPermit = 225
	// ErrorShareQuickRandomTimeout 分享码已失效
	ErrorShareQuickRandomTimeout = 226
	// ErrorShareStarExceedsLimit 星标（快捷联系人），不能超过5个
	ErrorShareStarExceedsLimit = 227
	// ErrorShareNoAddressBookDev 非通讯录设备
	ErrorShareNoAddressBookDev = 228
	// ErrorShareVoiceRoleIsSet 该角色已设置
	ErrorShareVoiceRoleIsSet = 229
)

// 客户端亲友关系类.
const (
	//EFriendInvalidAccount 添加好友失败 对方账户未注册
	ErrorFriendInvalidAccount = 240
	//EFriendAlready  已经是好友关系
	ErrorFriendAlready = 241
	//EFriendToSelf 不能添加自己为好友
	ErrorFriendToSelf = 242
	//EFriendInvalidRequest 好友请求消息过期
	ErrorFriendInvalidRequest = 243
)

// 获取临时安全凭证
// 获取失败
const ErrorGetCredentials = 260

// 预置位
const (
	// 预置位个数不能超过6个
	ErrorPresetOutLimit = 270
	// 预置位名称重复
	ErrorPresetNameExist = 271
)

// 分组，名称编辑类通用错误
const (
	// 记录不存在
	ErrorRecordNotExist = 280
	// 名称已存在
	ErrorNameExist = 281
	// 存在设备
	ErrorHaveDevice = 282
	// 操作不允许
	ErrorPermissionDenied = 283
)

// 二维码登录
const (
	// ErrorQrLoginRandomInvalid 随机码不存在
	ErrorQrLoginRandomInvalid = 290
	// ErrorQrLoginRandomTimeout 随机码已失效
	ErrorQrLoginRandomTimeout = 291
	// ErrorQrLoginStatus 状态不正确，二维码被多次扫码
	ErrorQrLoginStatus = 292
)

// 4G写号
const (
	// ErrorBurnInvalidCid 无效的CID
	ErrorBurnInvalidCid = 300
	// ErrorBurnCidIsUsed CID已使用
	ErrorBurnCidIsUsed = 301
	// ErrorBurnMacIsUsed 设备已写入其他CID
	ErrorBurnMacIsUsed = 302
)

// 第三方相关
const (
	// ErrorTencentAPICall 调用API失败
	ErrorTencentAPICall = 500
	// ErrorTencentInvalidJsCode 无效的js code
	ErrorTencentInvalidJsCode = 501
	// ErrorTencentInvalidPhoneCode 无效的phone code
	ErrorTencentInvalidPhoneCode = 502
	// ErrorTencentInvalidOpenID 无效的openID
	ErrorTencentInvalidOpenID = 503
	// ErrorTencentOpenID2Account openID与账号不一致
	ErrorTencentOpenID2Account = 504
	// ErrorTencentCompleteInfo 完善用户信息
	ErrorTencentCompleteInfo = 505
	// ErrorWeixinOpenIDIsBinded 微信账号已经被其他账号绑定
	ErrorWeixinOpenIDIsBinded = 506
	// ErrorAppleIDIsBinded apple账号已经被其他账号绑定
	ErrorAppleIDIsBinded = 507
	// ErrorFacebookIDIsBinded facebook账号已经被其他账号绑定
	ErrorFacebookIDIsBinded = 508
	// ErrorUniqueAccountIsBinded 唯一账号已经被其他账号绑定
	ErrorUniqueAccountIsBinded = 509
)

var errorMap = map[int]string{
	// 0
	CodeOK: "OK",
	// 100 直播类
	ErrorVideoPeerNotExist:   "对端不在线", // 100
	ErrorVideoPeerDisconnect: "对端断开",  // 101
	ErrorVideoPeerInConnect:  "正在查看中", // 102
	ErrorVideoPeerNotLogin:   "本端未登陆", // 103
	// 120 通用错误
	ErrorUnknown:            "未知错误",                     // 120
	ErrorDataBase:           "数据库错误",                    // 121
	ErrorSessionTimeout:     "会话超时",                     // 122
	ErrorInvalidMsg:         "格式不对",                     // 123
	ErrorMsgRateExceedLimit: "消息速率超过限制，请控制合理流速（100个每秒）", // 124
	ErrorInvalidParameter:   "参数不对",                     // 125
	ErrorCallRobotApi:       "调用robot API出现错误",          // 126
	ErrorInvalidVKey:        "vid vkey 错误",              // 127
	ErrorCallRobotRet:       "调用robot API返回数据错误",        // 128
	ErrorNotLogin:           "未登录，需要登录才能进行此操作",          // 129
	ErrorTooFrequenly:       "操作太频繁",                    // 130

	// 140 设备端鉴权。
	ErrorCIDExceedQuota:    "厂家CID达到配额。关联消息：注册", // 140
	ErrorCIDSNVerifyFailed: "SN签名验证失败。关联消息：登陆",  // 141
	ErrorPublicKeyNotExist: "PublicKey不存在",      // 142
	ErrorCIDIsDuplicate:    "cid重复",             // 143
	ErrorCIDNotRegistered:  "cid未注册",            // 144
	ErrorCIDTimezoneEmpty:  "cid时区为空",           // 145
	ErrorCIDIotVideoWecall: "设备未开通微信服务",         // 146

	// 160 客户端登陆类.
	ErrorLoginInvalidVKey:        "vid, bundleID, vkey校验失败", // 160
	ErrorLoginInvalidPass:        "帐号或密码错误",                 // 161
	ErrorOpenLoginInvalidToken:   "第三方帐号登陆access_token验证失败", // 162
	ErrorOpenLoginTypeNotSupport: "第三方帐号登陆，暂时不支持此登录方式",      // 163

	// 180 客户端帐号类.
	ErrorCodeNotMatch:           "验证码错误",                      // 180
	ErrorCodeTimeout:            "验证码超时",                      // 181
	ErrorAccountNotExist:        "帐号不存在",                      // 182
	ErrorAccountAlreadyExist:    "帐号已存在",                      // 183
	ErrorSamePass:               "原始密码与新密码相同。关联消息：修改密码",       // 184
	ErrorInvalidPass:            "原密码错误。关联消息：修改密码",            // 185
	ErrorPhoneExist:             "此手机号码已被绑定。关联消息：帐号、手机号、邮箱绑定", // 186
	ErrorEmailExist:             "此邮箱已被绑定。关联消息：帐号、手机号、邮箱绑定",   // 187
	ErrorIsNotPhone:             "手机号码不合规",                    // 188
	ErrorIsNotEmail:             "邮箱账号不合规",                    // 189
	ErrorInvalidPhoneNumber:     "忘记密码时，邮箱和手机号不存在时报错",         // 190
	ErrorSetPassTimeout:         "第三方账号设置密码超时",                // 191
	ErrorGetCodeTooFrequent:     "十分钟内获取验证码超过3次",              // 192
	ErrorSendSmsFailed:          "短信接口调用失败",                   // 193
	ErrorHasDevice:              "帐号下有设备(含被分享设备)未删除",          // 194
	ErrorNotEmailOrPhone:        "不是邮箱或手机号",                   // 195
	ErrorExceedLimit:            "操作超出限制，目前用于区域切换操作",          // 196
	ErrorPleaseCompletePassword: "小程序注册的账号，需要完善密码",            //  197
	ErrorSamePhone:              "相同的手机号",                     // 198
	ErrorSameEmail:              "相同的邮箱",                      // 199

	// 200 客户端绑定设备类.
	ErrorCIDNotExist:           "CID不存在。关联消息：客户端绑定",                 // 200
	ErrorCIDBinding:            "绑定中，正在等待摄像头上传随机数与CID关联关系，随后推送绑定通知", // 201
	ErrorCIDAliasExist:         "设备别名已存在",                           // 202
	ErrorCIDNotBind:            "设备未绑定，不可操作未绑定设备",                   // 203
	ErrorCIDBinded:             "设备已经被其他账号绑定",                       // 204
	ErrorInvalidCode:           "设备验证码不匹配",                          // 205
	ErrorCIDBindSelf:           "设备被自己绑定",                           // 206
	ErrorCIDDiffer:             "APP上报与设备上报cid不匹配",                  // 207
	ErrorCIDBindTimeout:        "绑定等待超时",                            // 208
	ErrorCIDBindErrorCidLen:    "通过SN, vid查询到已注册的cid长度错误",           // 209
	ErrorCIDBindVidNotExist:    "vid不存在",                            // 210
	ErrorCIDBindPidNotExist:    "pid不存在",                            // 211
	ErrorCIDBindMacNotExist:    "mac不存在",                            // 212
	ErrorCIDBindCancel:         "绑定取消",                              // 213
	ErrorCIDBindForOtherClient: "设备已经被其他APP端执行绑定操作",                 // 214
	ErrorCIDOffline:            "设备不在线",                             //215
	ErrorInvalid4GCard:         "非法4G卡，禁止联网",                        // 218
	ErrorCIDNotPermit:          "操作不允许",                             // 219

	// 220 客户端分享设备类.
	ErrorShareInvalidAccount:     "此帐号还没有注册",         // 220
	ErrorShareAlready:            "此帐号已经分享",          // 221
	ErrorShareToSelf:             "您不能分享给自己",         // 222
	ErrorShareExceedsLimit:       "设备分享，被分享账号不能超过5个", // 223
	ErrorShareNotSameArea:        "设备分享账号不在同一个区域",    // 224
	ErrorShareNotPermit:          "操作不允许",            // 225
	ErrorShareQuickRandomTimeout: "分享码已失效",           // 226
	ErrorShareStarExceedsLimit:   "快捷联系不能超过5个",       // 227
	ErrorShareNoAddressBookDev:   "非通讯录设备不能分享",       // 228
	ErrorShareVoiceRoleIsSet:     "该角色已设置",           // 229

	// 240 客户端亲友关系类.
	ErrorFriendInvalidAccount: "添加好友失败 对方账户未注册", // 240
	ErrorFriendAlready:        "已经是好友关系",        // 241
	ErrorFriendToSelf:         "不能添加自己为好友",      // 242
	ErrorFriendInvalidRequest: "好友请求消息过期",       // 243

	// 260 获取临时安全凭证
	ErrorGetCredentials: "获取临时安全凭证失败", // 260

	// 270 预置位
	ErrorPresetOutLimit:  "预置位个数不能超限", // 270
	ErrorPresetNameExist: "预置位名称重复",   // 271

	// 280 分组，名称编辑类通用错误
	ErrorRecordNotExist:   "记录不存在", // 280
	ErrorNameExist:        "名称已存在", // 281
	ErrorHaveDevice:       "存在设备",  // 282
	ErrorPermissionDenied: "操作不允许", // 283

	// 290 二维码登录
	ErrorQrLoginRandomInvalid: "随机码不存在或",        // 290
	ErrorQrLoginRandomTimeout: "随机码已失效",         // 291
	ErrorQrLoginStatus:        "状态不正确，二维码被多次扫码", // 292

	// 300 4G写号
	ErrorBurnInvalidCid: "无效的CID",     // 300
	ErrorBurnCidIsUsed:  "CID已使用",     // 301
	ErrorBurnMacIsUsed:  "设备已写入其他CID", // 302

	// 500 腾讯相关
	ErrorTencentAPICall:          "调用API失败",             // 500
	ErrorTencentInvalidJsCode:    "无效的Js code",          // 501
	ErrorTencentInvalidPhoneCode: "无效的Phone code",       // 502
	ErrorTencentInvalidOpenID:    "无效的openID",           // 503
	ErrorTencentOpenID2Account:   "openID与账号不一致",        // 504
	ErrorTencentCompleteInfo:     "完善用户信息",              // 505
	ErrorWeixinOpenIDIsBinded:    "微信账号已经被其他账号绑定",       // 506
	ErrorAppleIDIsBinded:         "Apple账号已经被其他账号绑定",    // 507
	ErrorFacebookIDIsBinded:      "Facebook账号已经被其他账号绑定", // 508
	ErrorUniqueAccountIsBinded:   "唯一账号已被其他账号绑定",        // 509
}

func GetCodeMsg(code int) string {
	if value, ok := errorMap[code]; ok {
		return value
	}

	return fmt.Sprintf("id_%d", code)
}
