package main

import (
	"mirulib"
	"mirulib/consul"
	"mirulib/worker"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// SrvManager 服务管理器
type SrvManager struct {
	WsSrvs      map[mirulib.SEQ]*WsService
	WsSrvsSess  map[string]*WsService
	nextID      mirulib.SEQ
	l           sync.RWMutex
	loginPolicy *mirulib.LoginControlPolicy
}

func initConn(conf mainConf) {
	for _, v := range conf.App.MiruServer.MsgRoute {
		// find queue
		for _, q := range conf.Base.Queue {
			if q.Name == v.Queue {
				msgRoute[v.Prefix] = q
				break
			}
		}
	}
	listenMQRoute = fmt.Sprintf("miru.%s.%d", mirulib.HostName, conf.App.MiruServer.BindPort)
}

const (
	minWatiTime int64 = 30  // 最小等待时间
	maxWaitTime       = 300 // 最长等待时间
	stepTime          = 30  // 每次递增时间
	triggerMax        = 500 // 递增条件，达到此值则触发递增stepTime时间
)

// NewSrvManager 初始化服务管理器
func NewSrvManager() *SrvManager {
	return &SrvManager{
		WsSrvs:      make(map[mirulib.SEQ]*WsService),
		WsSrvsSess:  make(map[string]*WsService),
		nextID:      10000,
		loginPolicy: mirulib.NewLoginPolicy(minWatiTime, maxWaitTime, stepTime, triggerMax, triggerMax),
	}
}

func (p *SrvManager) GetConnID() (mirulib.SEQ, error) {
	p.l.Lock()
	defer p.l.Unlock()

	id := p.nextID
	i := 0
	for {
		if i > 100 {
			return 0, fmt.Errorf("get conn id erorr")
		}
		_, ok := p.WsSrvs[id]
		if ok {
			id = id + 1
		} else {
			p.nextID = id + 1
			return id, nil
		}
	}
}

// AddSrv 增加WS服务
func (p *SrvManager) AddSrv(srv *WsService) {
	id, err := p.GetConnID()
	if err != nil {
		srv.Conn.Close()
		logger.Info("conn addr: [%s] get id error", srv.Addr)
		return
	}

	p.l.Lock()
	defer p.l.Unlock()
	srv.ID = id
	p.WsSrvs[id] = srv
	logger.Info("add conn addr: [%s], ID: [%d]", srv.Addr, srv.ID)
}

// 连接session生成,更新时
// 更新交由具体的connectRoutine来做,所以这里只加读锁
func (p *SrvManager) UpdateSession(seq mirulib.SEQ, sess string, net int, isLoginOk bool, updateHB bool) {
	p.l.RLock()
	defer p.l.RUnlock()

	var srv *WsService

	srv = p.WsSrvs[seq]
	if srv == nil || len(sess) == 0 {
		logger.Errorf("%d, %s, update sess with invalid param", seq, sess)
		return
	}

	// 这个session已经分配给上一个老的连接,断开老的连接
	if p.WsSrvsSess[sess] != nil && p.WsSrvsSess[sess].ID != seq && !p.WsSrvsSess[sess].Closed {
		logger.Info("%d, %s, duplicate session, close old seq %d", seq, sess, p.WsSrvsSess[sess].ID)
		p.WsSrvsSess[sess].ChanSendMsg(ConnCmd{
			Cmd: ConnCmdCloseDupSess,
		})
	}

	// 本连接的session更新,删掉老的连接
	if len(srv.Session) > 0 && srv.Session != sess {
		logger.Info("%d, %s, update session to:%s", srv.ID, srv.Session, sess)
		// 这里申请写锁
		// 先解除读锁
		p.l.RUnlock()
		p.l.Lock()
		delete(p.WsSrvsSess, srv.Session)
		logger.Debug("------delete seq[%d] sess[%s] from clientsSess %v", srv.ID, srv.Session, srv)
		p.l.Unlock()
		p.l.RLock()
	}

	logger.Info("%d, %s, session update", seq, sess)
	srv.ChanSendMsg(ConnCmd{
		Cmd: ConnCmdUpdateSess,
		Arg: sess,
		Net: net,
	})

	if isLoginOk {
		srv.ChanSendMsg(ConnCmd{
			Cmd:      ConnCmdUpdateLoginOk,
			UpdateHB: updateHB,
		})
	}
	if updateHB {
		srv.ChanSendMsg(ConnCmd{
			Cmd:      ConnCmdSlidingExpiration,
			UpdateHB: updateHB,
		})
	}
}

// GetSrvBySeq 通过seq查询WS连接
func (p *SrvManager) GetSrvBySeq(seq mirulib.SEQ) *WsService {
	p.l.RLock()
	defer p.l.RUnlock()

	return p.WsSrvs[seq]
}

// GetSrvBySession 通过session查询WS连接
func (p *SrvManager) GetSrvBySession(sess string) *WsService {
	p.l.RLock()
	defer p.l.RUnlock()

	return p.WsSrvsSess[sess]
}

// GetSrvBySession 通过session查询WS连接
func (p *SrvManager) GetQueryDataBySessions(sessList []string, detect, offline bool) []mirulib.QuerySessData {
	queryData := make([]mirulib.QuerySessData, 0)

	p.l.RLock()
	defer p.l.RUnlock()

	for _, sess := range sessList {
		data := mirulib.QuerySessData{Sess: sess, Exist: false}
		if srv, ok := p.WsSrvsSess[sess]; ok {
			data.ID = srv.ID
			data.Exist = true
			data.ConnTime = srv.loginTime
			data.IP = srv.Addr
			data.Net = srv.Net
			data.Route = listenMQRoute
			if detect {
				srv.ChanSendMsg(ConnCmd{
					Cmd:    ConnCmdSendMsg,
					Arg:    worker.GetJsonHeader(worker.JIDPubHeartBeat),
					IsJson: false,
				})
				srv.ChanSendMsg(ConnCmd{
					Cmd: ConnCmdOperateTick,
					Arg: true,
				})
			}
		} else if !offline {
			continue
		}
		queryData = append(queryData, data)
	}

	return queryData
}

func (p *SrvManager) GetSrvBySeqSession(seq mirulib.SEQ, sess string) *WsService {
	p.l.RLock()
	defer p.l.RUnlock()

	var c *WsService
	if seq != invalidSeq {
		c = p.WsSrvs[seq]
	}
	if c == nil && sess != "" {
		c = p.WsSrvsSess[sess]
	}

	return c
}

func (p *SrvManager) GetAllSess(typ int) []string {
	p.l.RLock()
	defer p.l.RUnlock()

	sess := []string{}
	for key, _ := range p.WsSrvsSess {
		if typ == mirulib.QueryAllSess {
			sess = append(sess, key)
		} else {
			l := len(key)
			if typ == mirulib.QueryClientSess && (l == mirulib.SessLen || l == mirulib.SessLenV2) {
				sess = append(sess, key)
			} else if typ == mirulib.QueryDogSess && l != mirulib.SessLen && l != mirulib.SessLenV2 {
				sess = append(sess, key)
			}
		}
	}

	return sess
}

func (p *SrvManager) Run() {
	checkPeriod := conf.App.MiruServer.KeepAlive.CheckPeriod
	if checkPeriod < 2*mirulib.HBCheckTimeOut || checkPeriod > mirulib.HBCheckTimePeriod {
		checkPeriod = mirulib.HBCheckTimePeriod
	}

	defer mirulib.LogPanic()

	for {
		logger.Info("check heartbeat period for req by [%d]", checkPeriod)
		time.Sleep(time.Second * time.Duration(checkPeriod))

		curTime := time.Now().Unix()
		var seqArr []mirulib.SEQ
		p.l.RLock()
		for k, v := range p.WsSrvs {
			if curTime-v.hbTime >= mirulib.HBCheckTimeOut {
				v.ChanSendMsg(ConnCmd{
					Cmd: ConnCmdHBTimeout,
				})

				seqArr = append(seqArr, k)
			}
		}
		p.l.RUnlock()

		if len(seqArr) > 0 {
			time.Sleep(time.Second * 3)
			p.l.Lock()
			for _, k := range seqArr {
				delete(p.WsSrvs, k)
			}
			p.l.Unlock()
		}

		logger.Info("check heartbeat period for sess by [%d]", mirulib.HBCheckTimeOut)
		time.Sleep(time.Second * time.Duration(mirulib.HBCheckTimeOut+3))
		curTime = time.Now().Unix()

		var sessArr []string
		p.l.RLock()
		for k, v := range p.WsSrvsSess {
			if curTime-v.hbTime >= mirulib.HBCheckTimeOut {
				v.ChanSendMsg(ConnCmd{
					Cmd: ConnCmdHBTimeout,
				})

				sessArr = append(sessArr, k)
			}
		}
		p.l.RUnlock()

		if len(sessArr) > 0 {
			time.Sleep(time.Second * 3)
			p.l.Lock()
			for _, k := range sessArr {
				delete(p.WsSrvsSess, k)
			}
			p.l.Unlock()
		}
		// logger.Debug("WsSrvs: %#v", p.WsSrvs)
		// logger.Debug("WsSrvsSess: %#v", p.WsSrvsSess)
	}
}

func (p *SrvManager) closeDelete(id mirulib.SEQ, sess string) {
	p.l.Lock()
	defer p.l.Unlock()

	if p.WsSrvs[id] != nil && sess == p.WsSrvs[id].Session {
		delete(p.WsSrvs, id)
		logger.Info("delete conn ID: [%d]", id)
	}
	if p.WsSrvsSess[sess] != nil && id == p.WsSrvsSess[sess].ID {
		delete(p.WsSrvsSess, sess)
		logger.Info("delete conn sess: [%s]", sess)
	}
}

func (p *SrvManager) addSessionKey(sess string, pw *WsService) {
	p.l.Lock()
	defer p.l.Unlock()

	p.WsSrvsSess[sess] = pw
}

/***********************************
    http handle for manager
***********************************/

var lockDelete bool = true

func initHandler() {
	http.Handle("/qs", http.HandlerFunc(querySessNormal))
	http.Handle("/dg", http.HandlerFunc(querySessDebug))
	http.Handle("/ds", http.HandlerFunc(httpDelSess))
	http.Handle("/del_all", http.HandlerFunc(httpDeleteAll))
	http.Handle("/del_num", http.HandlerFunc(httpDeleteNum))
	http.Handle("/print", http.HandlerFunc(printAll))
	http.Handle("/lock", http.HandlerFunc(httpLockDelete))
	http.Handle("/muti_queue", http.HandlerFunc(httpChangeMutiQueue))
	http.Handle("/operate_max", http.HandlerFunc(httpChangeOperateMax))
	http.Handle("/operate_login", http.HandlerFunc(httpAddNotLoginID))
}

// 打印方法
func (c WsService) String() string {
	return fmt.Sprintf("seq:%d, net:%d, sess:%s, addr:%s, loginOk:%t, closed:%t hbTime:%d",
		c.ID, c.Net, c.Session, c.Addr, c.loginOk, c.closed, c.hbTime)
}

func printAll(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")

	f := func(s string) bool { return s == "true" }
	timeout := f(r.FormValue("timeout"))
	del := f(r.FormValue("del"))
	seq := []mirulib.SEQ{}

	srvm.l.RLock()
	fmt.Fprintf(w, "seq total:%d, sess total:%d\n", len(srvm.WsSrvs), len(srvm.WsSrvsSess))

	curTime := time.Now().Unix()
	for _, val := range srvm.WsSrvs {
		if timeout {
			if curTime-val.hbTime > mirulib.HBCheckTimeOut {
				seq = append(seq, val.ID)
				fmt.Fprintf(w, "[%s]\n", val)
			}
			continue
		}

		fmt.Fprintf(w, "[%s]\n", val)
	}
	srvm.l.RUnlock()

	if timeout && del && len(seq) > 0 && !lockDelete {
		for _, s := range seq {
			delClients(s, "")
		}
	}
}

func querySess(w http.ResponseWriter, r *http.Request, isDebug bool) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	seq := r.FormValue("seq")
	sess := r.FormValue("sess")

	srvm.l.RLock()
	defer srvm.l.RUnlock()

	var c *WsService
	if seq != "" {
		seq64, err := strconv.Atoi(seq)
		if err != nil {
			w.WriteHeader(400)
			fmt.Fprintf(w, "invalid seq")
			return
		}

		c = srvm.WsSrvs[mirulib.SEQ(seq64)]
	} else if sess != "" {
		c = srvm.WsSrvsSess[sess]
	} else {
		w.WriteHeader(400)
		fmt.Fprintf(w, "invalid request")
		return
	}

	fmt.Fprintf(w, "seq total:%d, sess total:%d, [%s] ", len(srvm.WsSrvs), len(srvm.WsSrvsSess), c)
	if !isDebug {
		return
	}

	notMatchSess := "\nnotMatchSess:\n"
	hbTimeOutSess := "\nhbTimeOutSess:\n"
	curTime := time.Now().Unix()
	for _, val := range srvm.WsSrvs {
		if curTime-val.hbTime > mirulib.HBCheckTimeOut {
			hbTimeOutSess += fmt.Sprintf("[%s]\n", val)
		}
		if val.Session == "" {
			continue
		}
		if _, ok := srvm.WsSrvsSess[val.Session]; !ok {
			notMatchSess += fmt.Sprintf("[%s]\n", val)
		}
	}

	fmt.Fprintf(w, hbTimeOutSess)
	fmt.Fprintf(w, notMatchSess)

	notMatchSeq := "\nnotMatchSeq:\n"
	hbTimeOutSeq := "\nhbTimeOutSeq:\n"
	curTime = time.Now().Unix()
	for _, val := range srvm.WsSrvsSess {
		if curTime-val.hbTime > mirulib.HBCheckTimeOut {
			hbTimeOutSeq += fmt.Sprintf("[%s]\n", val)
		}
		if val.Session == "" {
			continue
		}
		if _, ok := srvm.WsSrvs[val.ID]; !ok {
			notMatchSeq += fmt.Sprintf("[%s]\n", val)
		}
	}

	fmt.Fprintf(w, hbTimeOutSeq)
	fmt.Fprintf(w, notMatchSeq)
}

func httpDelSess(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	if lockDelete {
		w.Write([]byte("del failed!"))
		return
	}
	sess := r.FormValue("sess")
	srvm.l.RLock()
	c := srvm.WsSrvsSess[sess]
	srvm.l.RUnlock()
	if c == nil {
		w.Write([]byte("cannot find sess: " + sess))
	} else {
		delClients(0, sess)
		w.Write([]byte("del ok"))
	}
}

func delAllLoop() {
	go func() {
		timer := time.NewTicker(1 * time.Second)
		for _ = range timer.C {
			var end bool
			var seq mirulib.SEQ
			srvm.l.RLock()
			if len(srvm.WsSrvs) <= 0 {
				end = true
			}
			for iseq := range srvm.WsSrvs {
				seq = iseq
				break
			}
			srvm.l.RUnlock()
			if end {
				break
			}
			delClients(seq, "")
		}
		timer.Stop()
	}()
}

func httpDeleteAll(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	if lockDelete {
		w.Write([]byte("del failed!"))
		return
	}

	tms := r.FormValue("tm")
	if tms == "" {
		w.Write([]byte("tm must be specify"))
		return
	}

	tm, _ := strconv.ParseInt(tms, 10, 64)
	nowSec := time.Now().Unix()
	if nowSec-10 > tm || nowSec < tm {
		w.Write([]byte("tm not correct"))
		return
	}

	delAllLoop()
	w.Write([]byte("running del all"))
}

func delNumLoop(total int) {
	go func() {
		tmp := total
		logger.Info("delNumLoop for total [%d] in", tmp)
		timer := time.NewTicker(1 * time.Second)
		for _ = range timer.C {
			var end bool
			var seqs []mirulib.SEQ
			srvm.l.RLock()
			if len(srvm.WsSrvs) <= 0 || total <= 0 {
				end = true
			}
			count := 100
			total -= count
			for iseq := range srvm.WsSrvs {
				seqs = append(seqs, iseq)
				if count <= 0 {
					break
				}
				count--
			}
			srvm.l.RUnlock()
			if end {
				break
			}
			for _, seq := range seqs {
				delClients(seq, "")
			}
		}
		timer.Stop()
		logger.Info("delNumLoop for total [%d] end", tmp)
	}()
}

func httpDeleteNum(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	if lockDelete {
		w.Write([]byte("del failed!"))
		return
	}
	totalStr := r.FormValue("total")
	if totalStr == "" {
		w.Write([]byte("total must be specify"))
		return
	}

	total, err := strconv.Atoi(totalStr)
	if err != nil {
		w.Write([]byte("total num is error"))
		return
	}

	delNumLoop(total)
	w.Write([]byte(fmt.Sprintf("running del totol [%d]", total)))
}

func querySessNormal(w http.ResponseWriter, r *http.Request) {
	querySess(w, r, false)
}

func querySessDebug(w http.ResponseWriter, r *http.Request) {
	querySess(w, r, true)
}

func httpLockDelete(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")

	val := r.FormValue("value")
	key := r.FormValue("keyID")
	if key != "CylanLockID@Ws@543" {
		w.Write([]byte("key error!"))
		return
	}

	if val == "True" {
		lockDelete = true
	} else if val == "False" {
		lockDelete = false
	} else {
		w.Write([]byte("value is error!"))
		return
	}

	w.Write([]byte(fmt.Sprintf("lockFlag %v!", lockDelete)))
}

func httpChangeMutiQueue(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	if lockDelete {
		w.Write([]byte("change failed!"))
		return
	}

	val := r.FormValue("num")
	num, err := strconv.Atoi(val)
	if err != nil {
		w.WriteHeader(400)
		fmt.Fprintf(w, "invalid num")
		return
	}

	_, err = worker.InitMutiRecvQueue(conf.Base, conf.App.Worker.WorkerQueue, num)
	if err != nil {
		w.WriteHeader(400)
		fmt.Fprintf(w, "change mutiqueue to num:%d err:%v!", num, err)
		return
	}

	fmt.Fprintf(w, "change mutiqueue to num:%d OK!", num)
}

func httpChangeOperateMax(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	if lockDelete {
		w.Write([]byte("change failed!"))
		return
	}

	val := r.FormValue("max")
	max, err := strconv.Atoi(val)
	if err != nil {
		w.WriteHeader(400)
		fmt.Fprintf(w, "invalid max")
		return
	}

	if max < 10 {
		w.WriteHeader(400)
		fmt.Fprintf(w, "change operateMax to max:%d err:%v!", max, err)
		return
	}

	operateMax = int64(max)
	fmt.Fprintf(w, "change operateMax to max:%d OK!", operateMax)
}

func httpAddNotLoginID(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	if lockDelete {
		w.Write([]byte("add failed!"))
		return
	}

	val := r.FormValue("login_ids")
	arr := strings.Split(val, ",")
	consul.SetNotWantLoginReq(arr)

	fmt.Fprintf(w, "change login_ids to %v OK!", arr)
}

func delClients(seq mirulib.SEQ, sess string) {
	srvm.l.RLock()
	defer srvm.l.RUnlock()

	var c *WsService
	// delete by seq?
	if seq != invalidSeq {
		c = srvm.WsSrvs[seq]
	}
	if c == nil && len(sess) > 0 {
		c = srvm.WsSrvsSess[sess]
	}

	c.ChanSendMsg(ConnCmd{
		Cmd: ConnCmdClose,
	})
}
