package main

import (
	"mirulib"
	"mirulib/dp"
	"mirulib/redislib"
	"mirulib/worker"
	"fmt"
	"time"
)

// 透传消息 JIDPubForward
func (this *MQHandler) JIdPubForward(ctx *worker.JsonWorkerContext) {
	var data map[string]interface{}
	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
		return
	}

	headers, ok := data["headers"].(map[string]interface{})
	if ok {
		headers["caller"] = ctx.Headers.Caller
		data["headers"] = headers
	}

	kvsSObj := mirulib.GetKVSSession(ctx.Headers.Callee)
	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
	err := kvsSObj.Get()
	if err != nil || kvsSObj.Net <= mirulib.NetOffline {
		worker.MQSendErrorReply(ctx, worker.ErrorVideoPeerNotExist)
		return
	}

	worker.MQSendWorkerMsgKVS(kvsSObj, data, ctx.ClientMsg.DStr)
}

// 透传消息 JIDPubP2pForward
// func (this *MQHandler) JIdPubP2pForward(ctx *worker.JsonWorkerContext) {
// 	var data worker.JPubP2pForward
// 	if worker.JsonUnmarshalHandle(&data, ctx, true) != nil {
// 		return
// 	}

// 	kvsSObj := mirulib.GetKVSSession(ctx.Headers.Callee)
// 	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
// 	err := kvsSObj.Get()
// 	if err != nil || kvsSObj.Net <= mirulib.NetOffline {
// 		worker.MQSendErrorReply(ctx, worker.ErrorVideoPeerNotExist)
// 		return
// 	}

// 	data.Headers.Caller = ctx.Headers.Caller
// 	worker.MQSendWorkerMsgKVS(kvsSObj, data, ctx.ClientMsg.DStr)
// }

// func (this *MQHandler) JIdPubP2pForwardRsp(ctx *worker.JsonWorkerContext) {
// 	var data worker.JPubP2pForwardRsp
// 	if worker.JsonUnmarshalHandle(&data, ctx, false) != nil {
// 		return
// 	}

// 	kvsSObj := mirulib.GetKVSSession(ctx.Headers.Callee)
// 	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
// 	err := kvsSObj.Get()
// 	if err != nil || kvsSObj.Net <= mirulib.NetOffline {
// 		return
// 	}

// 	data.Headers.Caller = ctx.Headers.Caller
// 	worker.MQSendWorkerMsgKVS(kvsSObj, data, ctx.ClientMsg.DStr)
// }

// 内部用离线消息处理 JIDPubReportOffline
func (this *MQHandler) JIdPubReportOffline(ctx *worker.JsonWorkerContext) {
	sessid := ctx.Sess
	lenSess := len(sessid)
	if lenSess < 12 {
		return
	}

	os := mirulib.OSNone
	lockSess := redislib.Lock(redislib.RDLockSession, sessid)
	kvsSObj := mirulib.GetKVSSession(sessid)
	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
	err := kvsSObj.Get()
	if mirulib.IsKoreaDog(sessid) { //韩国设备，使用imei地址登录
		kvsSObj.Net = mirulib.NetOffline
		kvsSObj.ClearSeq()
		kvsSObj.LastLoginTime = time.Now().Unix()
		kvsSObj.Name = ""
		kvsSObj.Time = 0
		kvsSObj.Save(0, "", true, true)

		lockSess.Unlock()

		mirulib.DBNO.Query(mirulib.GCameraKorea).Filter("sessid", sessid).Update("net", mirulib.NetOffline)
		if kvsSObj.Account != "" {
			worker.SyncCliPushRefresh(kvsSObj.Account, "", worker.JIDCliKoreaDevList, "")
		}

		return
	}

	if err == nil && ctx.Seq == kvsSObj.GetSeq() && ctx.Route == kvsSObj.GetRoute() {
		//offNet := mirulib.OfflineNet(kvsSObj.Net)
		kvsSObj.Net = mirulib.NetOffline
		kvsSObj.ClearSeq()
		os = kvsSObj.Os
		if mirulib.IsDog(sessid) {
			//lockCam := redislib.Lock(redislib.RDLockCam, sessid)
			kvsCamObj := mirulib.GetKVSCam(sessid)
			kvsCamObj.SetDebugStr(ctx.ClientMsg.DStr)
			if kvsCamObj.Get() == nil {
				kvsCamObj.Net = mirulib.NetOffline
				kvsCamObj.Save(false)
			}
			kvsSObj.LastLoginTime = time.Now().Unix()
			kvsSObj.Name = ""
			kvsSObj.Time = 0
			kvsSObj.Save(0, "", true, true)
			//lockCam.Unlock()
		} else {
			mirulib.DelAccountSession(kvsSObj.Account, "")
			kvsSObj.Delete()
		}

	}
	lockSess.Unlock()

	if kvsSObj.Net != mirulib.NetOffline {
		return
	}

	if os != mirulib.OSIOSPhone && os != mirulib.OSPC && os != mirulib.OSAndroidPhone {
		mirulib.ZSetMag.AddSet(mirulib.OffZSetKey, time.Now().Unix(), sessid, ctx.ClientMsg.DStr)
		worker.SyncDPNet(sessid, mirulib.NetOffline, "", nil, true, true)
	}
}

func (this *MQHandler) JIdPubReportOnline(ctx *worker.JsonWorkerContext) {
	sessid := ctx.Sess
	lenSess := len(sessid)
	if lenSess < 12 {
		return
	}

	kvsSObj := mirulib.GetKVSSession(sessid)
	kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
	err := kvsSObj.Get()
	if err == nil {
		kvsSObj.Expire(120)
		mirulib.ExpireAccountSession(kvsSObj.Account, 120)
	}
}

/*
request消息
*/

// 多设备未读计数查询 JIDPubDpUnreadMuti
func (this *MQHandler) JIdPubDpUnreadMuti(ctx *worker.JsonWorkerContext) {
	dp.PubDpUnreadMuti(ctx, true)
}

// 删除数据 JIDPubDpDel
func (this *MQHandler) JIdPubDpDel(ctx *worker.JsonWorkerContext) {
	dp.PubDpDel(ctx, true)
}

// 批量删除数据 JIDPubDpDelMuti
func (this *MQHandler) JIdPubDpDelMuti(ctx *worker.JsonWorkerContext) {
	dp.PubDpDelMuti(ctx, true)
}

// 操纵数据 JIDPubDpAct
func (this *MQHandler) JIdPubDpAct(ctx *worker.JsonWorkerContext) {
	dp.PubDpAct(ctx, true)
}

func (this *MQHandler) JIdPubDpActRsp(ctx *worker.JsonWorkerContext) {
	dp.PubDpAct(ctx, true)
}

// 查询单个数据 JIDPubDpGet
func (this *MQHandler) JIdPubDpGet(ctx *worker.JsonWorkerContext) {
	dp.PubDpGet(ctx, true)
}

// 查询数据列表 JIDPubDpGetList
func (this *MQHandler) JIdPubDpGetList(ctx *worker.JsonWorkerContext) {
	dp.PubDpGetList(ctx, true)
}

// 批量查询数据 JIDPubDpGetMuti
func (this *MQHandler) JIdPubDpGetMuti(ctx *worker.JsonWorkerContext) {
	dp.PubDpGetMuti(ctx, true)
}

// 设置数据 JIDPubDpSet
func (this *MQHandler) JIdPubDpSet(ctx *worker.JsonWorkerContext) {
	dp.PubDpSet(ctx, true)
}

// 通用请求消息 JIDPubDpMidData
func (this *MQHandler) JIdPubDpMidData(ctx *worker.JsonWorkerContext) {
	var data worker.JPubDpMidData
	_, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}
	data.Headers.Caller = ctx.Headers.Caller
	//sessid := ctx.Headers.Caller

	workerMsg := worker.JPubDpMidDataRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.TypeID = data.Body.TypeID
	var payload interface{}

	switch data.Body.TypeID {
	case mirulib.GetOsByCId:
		var val worker.DPStrMsg
		worker.JsonUnmarshal(&val, data.Body.Payload)
		query := mirulib.DB.Query(mirulib.GCamera).Filter("cid", val.StrVal)
		if !query.Exist() {
			payload = worker.DPIntMsg{-1}
			break
		}

		var obj mirulib.TBCamera
		query.First(&obj, "os")
		//mirulib.DB.Query(mirulib.GCameraOS).Filter("cid_start", val.StrVal[:4]).First(&obj, "os")
		payload = worker.DPIntMsg{int(obj.Os)}
	case mirulib.GetRobotServiceKey:
		var val worker.DPStrMsg
		worker.JsonUnmarshal(&val, data.Body.Payload)
		var ossServiceConf mirulib.TBOssServiceConf
		mirulib.DB.Query(mirulib.GOssServiceConf).Filter("company_vid", val.StrVal, "product_page", "oss").First(&ossServiceConf)
		payload = ossServiceConf
	case mirulib.GetRegionByCid:
		var val worker.DPStrListMsg
		worker.JsonUnmarshal(&val, data.Body.Payload)
		var re = make(map[string]int)
		for _, cid := range val.Arr {
			////230 DP消息值-->设备所在区域（设备登陆过才有记录）。CN:1，US:2，EU:3，SG:4
			//kvsSObj := mirulib.GetKVSCam(cid)
			//if err := kvsSObj.Get(); err != nil {
			//	re[cid] = mirulib.RegionChinaType
			//	continue
			//}
			////通过kvsCam中的region获取其regionType
			//re[cid] = mirulib.GetOssCtypeByRegion(kvsSObj.Region)
			re[cid] = mirulib.OssCNConfig //默认设置为cn，防止跨区session取不到
			kvsSObj := mirulib.GetKVSSession(cid)
			if err := kvsSObj.Get(); err != nil {
				//跨区取kvsSession
				if !worker.OpenRegion {
					continue
				}
				logger.Debug("cid: %s,GetKVSSession from region", cid)
				haveKVSSession := false

				channel := worker.ChanClient.CreateChannel()
				defer channel.Delete()
				for _, region := range worker.OtherRegions {
					// get kvsession from region
					logger.Debug("get kvsession from region")
					if region == worker.Region {
						continue
					}

					worker.RegionCidKVSessionReq(channel, region, []string{cid}, true)
				}
				rsps := channel.RecvFull(1, (*worker.JRegionCidKVSSessionRsp)(nil))
				logger.Debug("channel.RecvFull ,len(rsps):%d ,rsps: %+v", len(rsps), rsps)
				if len(rsps) == 1 {
					r := rsps[0].ClientMsg.(worker.JRegionCidKVSSessionRsp)
					rgnObj, ok := r.KvsObjs[cid]
					if ok {
						kvsSObj = &rgnObj.KvsSObj
						haveKVSSession = true
					}
				}

				if !haveKVSSession {
					continue
				}
			}

			if kvsSObj.Route == "" {
				kvsSObj := mirulib.GetKVSCam(cid)
				if err := kvsSObj.Get(); err != nil {
					re[cid] = worker.GetTypeByDeviceRegion(kvsSObj.Region)
				}
			}
			re[cid] = worker.GetFirstFlag(kvsSObj.Route)
		}

		arrPayload := make([]worker.DPMidRegionCidMsg, 0)
		for k, v := range re {
			arrPayload = append(arrPayload, worker.DPMidRegionCidMsg{k, v})
		}
		payload = arrPayload
	}

	workerMsg.Body.Payload = worker.JsonMarshal(payload)
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 批量统计数据 JIDPubDpGetMutiCount
func (this *MQHandler) JIdPubDpGetMutiCount(ctx *worker.JsonWorkerContext) {
	var data worker.JPubDpGetMutiCount
	kvsSObj, err := worker.JsonUnmarshalHandleEx(&data, ctx, true)
	if err != nil {
		return
	}
	data.Headers.Caller = ctx.Headers.Caller

	rspMsg := worker.JPubDpGetMutiCountRsp{}
	rspMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	rspMsg.Body = make(map[string]interface{})
	for cid, dpDataList := range data.Body {
		if cid == "" {
			continue
		}

		account := kvsSObj.Account
		kvsPucObj := mirulib.GetKVSPUC(cid, kvsSObj.Account)
		if err := kvsPucObj.Get(); err == nil && kvsPucObj.ShareAccount != "" && kvsPucObj.Mark == 0 {
			account = kvsPucObj.ShareAccount
		}

		var dpCntList []interface{}
		for _, dpData := range dpDataList {
			dpData.BeginTime = mirulib.CorrectTime(dpData.BeginTime)
			dpData.EndTime = mirulib.CorrectTime(dpData.EndTime)

			if dpData.Days > 1 {
				dpCountDays := mirulib.JDPBIdCountDays{
					ID:        dpData.ID,
					BeginTime: dpData.BeginTime,
					Days:      dpData.Days,
					Counts:    make([]int, 0),
				}
				for i := int64(0); i < dpData.Days; i++ {
					count := worker.GetDpCount(account, cid, dpData.ID, dpData.BeginTime-(i+1)*86400*1000, dpData.BeginTime-i*86400*1000)
					dpCountDays.Counts = append(dpCountDays.Counts, count)
				}
				dpCntList = append(dpCntList, dpCountDays)
			} else {
				count := worker.GetDpCount(account, cid, dpData.ID, dpData.BeginTime, dpData.EndTime)
				dpCntList = append(dpCntList, mirulib.JDPBIdCount{JDPBId: dpData.JDPBId, Count: count})
			}
		}

		rspMsg.Body[cid] = dpCntList
	}

	worker.MQSendWorkMsg(ctx, &rspMsg)
}

var errRecodeCheck = fmt.Errorf("error")

const (
	RecordTypeMini = iota + 1
	RecordTypeEnd  = 100
)

func isRecordIn(recordKey string, typ int, key, val string) bool {
	if recordKey == "" {
		recordKey = fmt.Sprintf(mirulib.RecordSetKeyFmt, typ, key)
	}

	return mirulib.Redis.Sismember(recordKey, val) > 0
}

func recordDataCheck(ctx *worker.JsonWorkerContext, data *worker.JPubRecord, key *string) error {
	kvsSObj, err := worker.JsonUnmarshalHandleEx(data, ctx, true)
	if err != nil {
		return errRecodeCheck
	}

	if data.Body.Type < RecordTypeMini || data.Body.Type > RecordTypeEnd {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return errRecodeCheck
	}

	if data.Body.Key == "" {
		if mirulib.IsDog(ctx.Sess) {
			data.Body.Key = ctx.Sess
		} else {
			data.Body.Key = kvsSObj.Account
		}
	}

	if data.Body.Key == "" {
		worker.MQSendErrorReply(ctx, worker.ErrorInvalidParameter)
		return errRecodeCheck
	}

	*key = fmt.Sprintf(mirulib.RecordSetKeyFmt, data.Body.Type, data.Body.Key)
	return nil
}

// 记录获取 JIDPubRecordGet
func (this *MQHandler) JIdPubRecordGet(ctx *worker.JsonWorkerContext) {
	var data worker.JPubRecord
	var key string
	if recordDataCheck(ctx, &data, &key) != nil {
		return
	}

	workerMsg := worker.JPubRecordGetRsp{}
	workerMsg.JsonRspHeader = worker.GetJsonRspHeader(data.JsonHeader)
	workerMsg.Body.Key = data.Body.Key
	workerMsg.Body.Values = mirulib.Redis.SmembersDx(key, data.Headers.ReqID)
	worker.MQSendWorkMsg(ctx, workerMsg)
}

// 记录设置 JIDPubRecordSet
func (this *MQHandler) JIdPubRecordSet(ctx *worker.JsonWorkerContext) {
	var data worker.JPubRecord
	var key string
	if recordDataCheck(ctx, &data, &key) != nil {
		return
	}

	var err error
	if data.Body.IsDelete {
		err = mirulib.Redis.SremDx(key, data.Body.Value, data.Headers.ReqID)
	} else {
		err = mirulib.Redis.SaddExpireDx(key, data.Body.Value, mirulib.RecordSetOutTime, data.Headers.ReqID)
	}

	if err == nil {
		worker.MQSendNormalReply(ctx)
	} else {
		worker.MQSendErrorReply(ctx, worker.ErrorDataBase)
	}
}

// 记录是否存在 JIDPubRecordIn
func (this *MQHandler) JIdPubRecordIn(ctx *worker.JsonWorkerContext) {
	var data worker.JPubRecord
	var key string
	if recordDataCheck(ctx, &data, &key) != nil {
		return
	}

	if mirulib.Redis.Sismember(key, data.Body.Value) > 0 {
		worker.MQSendNormalReply(ctx)
	} else {
		worker.MQSendErrorReply(ctx, worker.ErrorRecordNotExist)
	}
}
