package worker

import (
	"encoding/json"
	"errors"
	"mirulib"
	"fmt"
	"reflect"
	"strings"
	"time"
)

type Headers struct {
	ID     string `json:"id"`
	Caller string `json:"caller"`
	Callee string `json:"callee"`
	ReqID  string `json:"req_id"`
	Time   int64  `json:"time"`
}

type Json<PERSON>eader struct {
	Headers Headers `json:"headers"`
}

type JsonRspHeader struct {
	Headers Headers `json:"headers"`

	Ret int    `json:"ret"`
	Msg string `json:"msg"`
}

func GetJsonRspHeader(header JsonHeader) JsonRspHeader {
	return JsonRspHeader{
		Headers: Headers{
			ID:     header.Headers.ID + "_rsp",
			Caller: header.Headers.Callee,
			Callee: header.Headers.Caller,
			ReqID:  header.Headers.ReqID,
			Time:   time.Now().Unix(),
		},
		Ret: 0,
		Msg: "OK",
	}
}

var index = 0

func GetJsonRspHeaderEx(id, reqID string, code int) JsonRspHeader {
	if reqID == "" {
		index += 1
		reqID = fmt.Sprintf("srv_%010d", index)
	}
	if !strings.Contains(id, "_rsp") {
		id += "_rsp"
	}
	return JsonRspHeader{
		Headers: Headers{
			ID:    id,
			ReqID: reqID,
			Time:  time.Now().Unix(),
		},
		Ret: code,
		Msg: GetCodeMsg(code),
	}
}

func GetJsonHeader(id string, params ...string) JsonHeader {
	var reqID, caller, callee string
	paramsLen := len(params)
	if paramsLen >= 1 {
		reqID = params[0]
	}
	if paramsLen >= 2 {
		caller = params[1]
	}
	if paramsLen >= 3 {
		callee = params[2]
	}
	if reqID == "" {
		index += 1
		reqID = fmt.Sprintf("srv_%010d", index)
	}
	return JsonHeader{
		Headers: Headers{
			ID:     id,
			ReqID:  reqID,
			Caller: caller,
			Callee: callee,
			Time:   time.Now().Unix(),
		},
	}
}

// type MsgWorkerRspHeader MsgRspHeader

// type MsgWorkerRsp struct {
// 	MsgWorkerRspHeader

// 	MsgBody interface{}
// }

// worker 上下文
type JsonWorkerContext struct {
	JsonHeader

	Seq        mirulib.SEQ
	Sess       string
	Route      string
	RmtAddr    string
	UpdateSess bool
	Net        int
	ClientMsg  mirulib.QClientMsg
	// 消息来源
	Exchange string
	//兼容过滤标记，用于屏蔽相互调用的多余逻辑
	IsCall          bool
	WorkerMsgID     int
	UpdateLoginOK   bool
	UpdateHeartBeat bool
}

// pushServer统计消息
type JsonPushStatMsg struct {
	// 主机名
	Host string
	// 端口
	Port int
	// 版本号
	Version string
	// 启动时间,UNIX时间戳
	UpTime int64
	// 当前时间
	Now int64
	// 从开机到当前时间 总共接收的连接
	NumConnected int64
	// 从开机到当前时间 总共丢失的连接
	NumDisconnected int64
	// 消息队列发送计数
	NumMQSent int64
	// 消息队列接收计数
	NumMQReceived int64
	// 各个消息id的发送计数
	MsgIdSent map[string]int64 // json can't encode map[int]
	// 各个消息id的接收计数
	MsgIdReceived map[string]int64
	// QOS 限速丢弃的消息技术
	NumQosDropped map[string]int64
}

// worker统计消息
type JsonWorkerStatMsg struct {
	// 主机名
	Host string
	// 版本号
	Version string
	// 启动时间,UNIX时间戳
	UpTime int64
	// 当前时间
	Now int64
	// 消息队列发送计数
	NumMQSent int64
	// 消息队列接收计数
	NumMQReceived int64
	// 每个消息的处理时间
	FuncCosumed map[int]int64
}

// type MsgWebJson struct {
// 	MsgHeader

// 	Json string
// }

// MIDErrorRsp=20251
// type MsgErrorRsp struct {
// 	MsgHeader
// 	Ret   int
// 	ReqID int
// }

// type AiPersonInfo struct {
// 	PersonID   string `json:"person_id"`
// 	PersonName string `json:"person_name"`
// }

// // 智能识别摄像头检测到vip，回调消息
// type MsgWarnVipReq struct {
// 	Sign    string         `json:"sign"`
// 	Time    int64          `json:"time"`
// 	Cid     string         `json:"cid"`
// 	Persons []AiPersonInfo `json:"persons"`
// }

/*
全局消息数据定义
*/
// var HBRsp MsgKeepAliveRsp = MsgKeepAliveRsp{
// 	MsgHeader: MsgHeader{
// 		Id: MIDKeepAliveACK,
// 	},
// }

// var HBRspOld MsgKeepAliveRspOld = MsgKeepAliveRspOld{
// 	MsgHeaderOld: MsgHeaderOld{
// 		Id: IDKeepAliveRsp, //先用旧版过度
// 	},
// }

// var Disconn = MsgDisconnect{
// 	MsgHeader: MsgHeader{
// 		Id: MIDDisconnect,
// 	},
// }

// var Resume = MsgResume{
// 	MsgHeader: MsgHeader{
// 		Id: IDResume, //先用旧版过度
// 	},
// }

// var Reboot = MsgCIDPushReboot{
// 	MsgWorkerRspHeader: MsgWorkerRspHeader{
// 		MsgHeader: MsgHeader{
// 			Id: MIDReboot,
// 		},
// 		Ret: EOK,
// 		Msg: MsgOK,
// 	},
// }
func JsonMarshal(msg interface{}) string {
	switch msg.(type) {
	case []byte:
		return string(msg.([]byte))
	case string:
		return msg.(string)
	default:
		if rBytes, err := json.Marshal(msg); err == nil {
			return string(rBytes)
		}
	}

	return ""
}

func JsonMarshalByte(msg interface{}) []byte {
	if rBytes, err := json.Marshal(msg); err == nil {
		return rBytes
	}
	return []byte{}
}

func JsonUnmarshal(dst, msg interface{}) error {
	if kind := reflect.TypeOf(dst).Kind(); kind != reflect.Ptr {
		logger.Errorf("Dst must be Ptr type:%d", kind)
		return fmt.Errorf("Dst must be Ptr type:%d", kind)
	}

	var bMsg []byte
	switch msg.(type) {
	case []byte:
		bMsg = msg.([]byte)
		break
	case []int8:
		for _, b := range msg.([]int8) {
			bMsg = append(bMsg, byte(b))
		}
		break
	case string:
		bMsg = []byte(msg.(string))
		break
	default:
		logger.Errorf("invalid byte message:[%v]", msg)
		return errors.New("invalid message type")
	}

	if err := json.Unmarshal(bMsg, dst); err != nil {
		logger.Errorf("invalid json message:%s dst:%+v bmsg:%v", err, dst, string(bMsg))
		return err
	}
	return nil
}

func JsonUnmarshalHandle(dst interface{}, ctx *JsonWorkerContext, bSendErr bool) error {
	err := JsonUnmarshal(dst, ctx.ClientMsg.ClientMsg)
	if err != nil {
		if bSendErr {
			MQSendErrorReply(ctx, ErrorInvalidMsg)
		}
	}

	return err
}

func JsonUnmarshalHandleEx(dst interface{}, ctx *JsonWorkerContext, getSession bool) (*mirulib.KVSSession, error) {
	if dst != nil {
		err := JsonUnmarshal(dst, ctx.ClientMsg.ClientMsg)
		if err != nil {
			logger.Errorf("session:%s, err:%s invalid msg", ctx.Headers.Caller, err)
			MQSendErrorReply(ctx, ErrorInvalidMsg)
			return nil, nil
		}
	}

	if getSession {
		kvsSObj := mirulib.GetKVSSession(ctx.Headers.Caller)
		kvsSObj.SetDebugStr(ctx.ClientMsg.DStr)
		if err := kvsSObj.Get(); err != nil {
			logger.Errorf("session:%s, err:%s timeout", ctx.Headers.Caller, err)
			MQSendErrorReply(ctx, ErrorSessionTimeout)
			return nil, err
		}
		return kvsSObj, nil
	} else {
		return nil, nil
	}
}

func GetJsonWorkerContext(queue mirulib.QueueConf, header *mirulib.MqHeader, msg interface{}) *JsonWorkerContext {
	var clientMsg mirulib.QClientMsg
	if err := mirulib.CopyInterface(&clientMsg, msg); err != nil {
		logger.Errorf("invalid msg:%v, err:%s", msg, err)
		return nil
	}

	var msgHeader JsonHeader
	if JsonUnmarshal(&msgHeader, clientMsg.ClientMsg) != nil {
		return nil
	}

	// if len(clientMsg.DStr) != 8 {
	// 	clientMsg.DStr = common.GetRandString(8)
	// }

	msgHeader.Headers.Caller = clientMsg.Sess
	// 准备Context
	ctx := JsonWorkerContext{
		JsonHeader:  msgHeader,
		Route:       clientMsg.Route,
		Seq:         clientMsg.Seq,
		RmtAddr:     clientMsg.RmtAddr,
		Sess:        clientMsg.Sess,
		UpdateSess:  false,
		ClientMsg:   clientMsg,
		Exchange:    queue.ExchangeName,
		IsCall:      false,
		WorkerMsgID: header.ID,
	}

	if header.ID == mirulib.MQIDWorkerRegion {
		ctx.IsCall = clientMsg.IsCall
	} else {
		if header.ID == mirulib.MQIDClientCall {
			ctx.IsCall = true
		}
	}
	return &ctx
}
