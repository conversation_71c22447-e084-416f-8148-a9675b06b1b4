package mirulib

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/garyburd/redigo/redis"
)

// for region caller account
// func GetAccountByCaller(caller string) string {
// 	if caller == "" {
// 		return ""
// 	}
// 	key := fmt.Sprintf(ClientCallerAccFormat, caller)
// 	var account string
// 	Redis.Get(key, &account)
// 	return account
// }

// func SetAccountByCaller(caller, account string) {
// 	if caller == "" || account == "" {
// 		return
// 	}
// 	key := fmt.Sprintf(ClientCallerAccFormat, caller)
// 	Redis.Set(key, account, ClientCallerAccExTime)
// }

//for set
func GetVidPidNum(route, vid string, pid uint32) (vidNum, pidNum int64) {
	if len(vid) > 0 {
		vidNum = Redis.Scard(fmt.Sprintf(OnlineTypeVIDPrefix, route, vid))
	}

	if pid > 0 {
		pidNum = Redis.Scard(fmt.Sprintf(OnlineTypeOSPrefix, route, pid))
	}
	return
}

func GetVidNumEx(routes []string, vid string) (vidNum int64) {
	if len(vid) <= 0 {
		return 0
	}

	totalKey := fmt.Sprintf(OnlineForRobotVidFormat, vid)
	if Redis.Get(totalKey, &vidNum) != nil {
		for _, route := range routes {
			vidNum += Redis.Scard(fmt.Sprintf(OnlineTypeVIDPrefix, route, vid))
		}
		Redis.Set(totalKey, &vidNum, OnlineForRobotExTime)
	}
	return
}

func GetPidNumEx(routes []string, pid uint32) (pidNum int64) {
	if pid <= 0 {
		return
	}

	totalKey := fmt.Sprintf(OnlineForRobotPidFormat, pid)
	if Redis.Get(totalKey, &pidNum) != nil {
		for _, route := range routes {
			pidNum += Redis.Scard(fmt.Sprintf(OnlineTypeOSPrefix, route, pid))
		}
		Redis.Set(totalKey, &pidNum, OnlineForRobotExTime)
	}
	return
}

//func IsSessionOnline(sessid string) bool {
//	num := len(sessid)
//	if num == CidLength || num == CidLengthV2 {
//		return Redis.Sismember(OnlineDogKey, sessid) > 0
//	} else if num > CidLength {
//		return Redis.Sismember(OnlineClientKey, sessid) > 0
//	}
//	return false
//}

//func GetSessionOnline(getType int) (valList []string) {
//	switch getType {
//	case SetGetDog:
//		valList = Redis.Smembers(OnlineDogKey)
//	case SetGetClient:
//		valList = Redis.Smembers(OnlineClientKey)
//	case SetGetDog | SetGetClient:
//		valList = Redis.Smembers(OnlineDogKey)
//		valList = append(valList, Redis.Smembers(OnlineClientKey)...)
//	}
//	return
//}

// func GetSessionNum(route string, getType int) int64 {
// 	switch getType {
// 	case SetGetDog:
// 		return Redis.Scard(fmt.Sprintf(OnlineDogKey, route))
// 	case SetGetClient:
// 		return Redis.Scard(fmt.Sprintf(OnlineClientKey, route))
// 	case SetGetDog | SetGetClient:
// 		return Redis.Scard(fmt.Sprintf(OnlineDogKey, route)) + Redis.Scard(fmt.Sprintf(OnlineClientKey, route))
// 	}
// 	return 0
// }

// func GetAllOnlineTypeOs(route string) (ret map[uint32]int64) {
// 	for i := OSCameraAndroidService; i < OSIdDefineEnd; i++ {
// 		key := fmt.Sprintf(OnlineTypeOSPrefix, route, i)
// 		ret[i] = Redis.Scard(key)
// 	}
// 	return
// }

func GetOnlineTypeOs(key string) int64 {
	return Redis.Scard(key)
}

func GetOnlineTypeOsEx(route string, typ uint32) int64 {
	key := fmt.Sprintf(OnlineTypeOSPrefix, route, typ)
	return Redis.Scard(key)
}

func GetSmembersTypeOs(key string) (valList []string) {
	valList = Redis.Smembers(key)
	return
}

// func GetSmembersTypeOsEx(route string, typ uint32) (valList []string) {
// 	key := fmt.Sprintf(OnlineTypeOSPrefix, route, typ)
// 	valList = Redis.Smembers(key)
// 	return
// }

func SetCidListTime(account string, changeTime int64, dStr string) (err error) {
	key := fmt.Sprintf(CidListTimePrefix, account)
	err = Redis.SetDx(key, changeTime, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cidList change time error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set cidList change time: %d -- account: %s", changeTime, account)
	return
}

func GetAccountTime(account, dStr string) (value int64, err error) {
	key := fmt.Sprintf(AvatarVidPrefix, account)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get account info change time error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get account info change time: %d -- account: %s", value, account)
	return
}

func SetAccountTime(account string, changeTime int64, dStr string) (err error) {
	key := fmt.Sprintf(AvatarVidPrefix, account)
	err = Redis.SetDx(key, changeTime, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set account info change time error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set account info change time: %d -- account: %s", changeTime, account)
	return
}

func GetMiniSendTime(cid, dStr string) (value int64, err error) {
	key := fmt.Sprintf(MiniSendTimeFormat, cid)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "GetMiniSendTime time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "GetMiniSendTime time: %d -- cid: %s", value, cid)
	return
}

func SetMiniSendTime(cid string, changeTime, exTime int64, dStr string) (err error) {
	key := fmt.Sprintf(MiniSendTimeFormat, cid)
	if exTime <= 0 {
		exTime = int64(OneHourExTime)
	}
	err = Redis.SetDx(key, changeTime, int(exTime-1), dStr)
	if err != nil {
		logger.DebugDx(dStr, "SetMiniSendTime time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "SetMiniSendTime time: %d -- cid: %s", changeTime, cid)
	return
}

func DelMiniSendTime(cid string, dStr string) (err error) {
	key := fmt.Sprintf(MiniSendTimeFormat, cid)
	err = Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "DelMiniSendTime time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "DelMiniSendTime -- cid: %s", cid)
	return
}

func GetAiServiceExpire(cid, service, dStr string) (value int64, err error) {
	key := fmt.Sprintf(RobotAiServiceFormat, service, cid)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get ai service: %s expire time error: %s -- cid: %s", service, err, cid)
		return
	}
	logger.DebugDx(dStr, "get ai service: %s expire time: %d -- cid: %s", service, value, cid)
	return
}

func SetAiServiceExpire(cid, service string, expireTime int64, dStr string) (err error) {

	key := fmt.Sprintf(RobotAiServiceFormat, service, cid)
	err = Redis.SetDx(key, expireTime, int(expireTime), dStr)
	if err != nil {
		logger.DebugDx(dStr, "set ai service: %s expire time error: %s -- cid: %s", service, err, cid)
		return
	}
	logger.DebugDx(dStr, "set ai service: %s expire time: %d -- cid: %s", service, expireTime, cid)
	return
}

func GetKVSCheckinStatus(cid, dStr string) (value string, err error) {
	key := fmt.Sprintf(DeviceCheckinStatusFormat, cid)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.InfoDx(dStr, "get device checkin status error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "get device checkin status: %s -- cid: %s", value, cid)
	return
}

func SetKVSCheckinStatus(cid, day, dStr string) (err error) {

	key := fmt.Sprintf(DeviceCheckinStatusFormat, cid)
	err = Redis.SetDx(key, day, WeekExTime, dStr)
	if err != nil {
		logger.InfoDx(dStr, "set device checkin status error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set device checkin status: %s -- cid: %s", day, cid)
	return
}

func DelAiServiceExpire(cid, service, dStr string) (err error) {

	key := fmt.Sprintf(RobotAiServiceFormat, service, cid)
	err = Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete ai service error: %s -- cid: %s -- service: %s", cid, service)
		return err
	}
	logger.DebugDx(dStr, "delete ai service -- cid: %s -- service: %s", cid, service)
	return nil
}

// 缓存客流统计导出文件路径
func GetEVCStatus(account string, beginTime, endTime int64, dStr string) (fileUrl string, err error) {
	key := fmt.Sprintf(EVCStatus, account, beginTime, endTime)
	err = Redis.GetDx(key, &fileUrl, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get EVC file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get EVC file url: %s -- account: %s", fileUrl, account)
	return
}

// 缓存客流统计导出文件路径
func SetEVCStatus(account string, beginTime, endTime int64, fileUrl, dStr string) (err error) {

	key := fmt.Sprintf(EVCStatus, account, beginTime, endTime)
	err = Redis.SetDx(key, fileUrl, WeekExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set EVC file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set EVC file url: %s -- account: %s", fileUrl, account)
	return
}

// 缓存脸库统计导出文件路径
func GetEGDStatus(account, timeStr string, dStr string) (fileUrl string, err error) {
	key := fmt.Sprintf(EGDStatus, account, timeStr)
	err = Redis.GetDx(key, &fileUrl, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get EGD file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get EGD file url: %s -- account: %s", fileUrl, account)
	return
}

// 缓存脸库统计导出文件路径
func SetEGDStatus(account, timeStr, fileUrl, dStr string) (err error) {

	key := fmt.Sprintf(EGDStatus, account, timeStr)
	err = Redis.SetDx(key, fileUrl, WeekExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set EGD file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set EGD file url: %s -- account: %s", fileUrl, account)
	return
}

// 缓存客流数据导出文件路径
func GetEWMStatus(account string, beginTime, endTime int64, dStr string) (fileUrl string, err error) {
	key := fmt.Sprintf(EWMStatus, account, beginTime, endTime)
	err = Redis.GetDx(key, &fileUrl, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get EWM file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get EWM file url: %s -- account: %s", fileUrl, account)
	return
}

// 缓存客流数据导出文件路径
func SetEWMStatus(account string, beginTime, endTime int64, fileUrl, dStr string) (err error) {

	key := fmt.Sprintf(EWMStatus, account, beginTime, endTime)
	err = Redis.SetDx(key, fileUrl, WeekExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set EWM file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set EWM file url: %s -- account: %s", fileUrl, account)
	return
}

// 缓存流向汇总导出文件路径
func GetEFDStatus(account string, beginTime, endTime int64, dStr string) (fileUrl string, err error) {
	key := fmt.Sprintf(EFDStatus, account, beginTime, endTime)
	err = Redis.GetDx(key, &fileUrl, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get EWM file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get EWM file url: %s -- account: %s", fileUrl, account)
	return
}

// 缓存流向汇总导出文件路径
func SetEFDStatus(account string, beginTime, endTime int64, fileUrl, dStr string) (err error) {

	key := fmt.Sprintf(EFDStatus, account, beginTime, endTime)
	err = Redis.SetDx(key, fileUrl, WeekExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set EWM file url error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set EWM file url: %s -- account: %s", fileUrl, account)
	return
}

//存储设备的报警去重间隔temTTL
func GetCidWarnFilterTime(cid, dStr string) (value int64, err error) {
	key := fmt.Sprintf(CidWarnFilterTime, cid)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get account info change time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "get account info change time: %d -- cid: %s", value, cid)
	return
}

func SetCidWarnFilterTime(cid string, filterTime int64, dStr string) (err error) {
	if filterTime < 0 {
		return
	}

	key := fmt.Sprintf(CidWarnFilterTime, cid)
	err = Redis.SetDx(key, filterTime, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set account info change time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set account info filter time: %d -- cid: %s", filterTime, cid)
	return
}

//存储设备的人脸活体检测开关
func GetCidLivingDetection(cid, dStr string) (value int, err error) {
	key := fmt.Sprintf(CidLivingDetection, cid)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get account info change time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "get account info change time: %d -- cid: %s", value, cid)
	return
}

func SetCidLivingDetection(cid string, livingDetectiong int, dStr string) (err error) {
	if livingDetectiong < 0 {
		return
	}

	key := fmt.Sprintf(CidLivingDetection, cid)
	err = Redis.SetDx(key, livingDetectiong, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set account info change time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set account info filter time: %d -- cid: %s", livingDetectiong, cid)
	return
}

// 缓存设备识别结果
func GetCidRecognitionResult(cid string, trackID int, fTime int64, dStr string) (value KVSPersonInfo, err error) {
	key := fmt.Sprintf(CidRecognitionResult, cid, trackID, fTime)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get CRR error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "get CRR value: %v -- cid: %s", value, cid)
	return
}

// 缓存设备识别结果
func SetCidRecognitionResult(cid string, trackID int, fTime int64, value KVSPersonInfo, dStr string) (err error) {

	key := fmt.Sprintf(CidRecognitionResult, cid, trackID, fTime)
	err = Redis.SetDx(key, value, 300, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set CRR error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set CRR value: %v -- cid: %s", value, cid)
	return
}

// 缓存设备GPS信息
func GetDeviceGPSInfo(cid string, dStr string) (value KVSDeviceGPSInfo, err error) {
	key := fmt.Sprintf(DeviceGPSInfoFormat, cid)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get device gps error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "get device gps value: %v -- cid: %s", value, cid)
	return
}

// 缓存设备GPS信息
func SetDeviceGPSInfo(cid string, value KVSDeviceGPSInfo, dStr string) (err error) {

	key := fmt.Sprintf(DeviceGPSInfoFormat, cid)
	err = Redis.SetDx(key, value, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set device gps error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set device gps value: %v -- cid: %s", value, cid)
	return
}

func GetCidListTime(account, dStr string) (value int64, err error) {
	key := fmt.Sprintf(CidListTimePrefix, account)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cidList change time error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get cidList change time: %d -- account: %s", value, account)
	return
}

func GetCidLastWarnTime(cid, dStr string) (value int64, err error) {
	key := fmt.Sprintf(CidLastWarnTime, cid)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get account info change time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "get account info change time: %d -- cid: %s", value, cid)
	return
}

func SetCidLastWarnTime(cid string, changeTime int64, dStr string) (err error) {

	key := fmt.Sprintf(CidLastWarnTime, cid)
	err = Redis.SetDx(key, changeTime, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set account info change time error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set account info change time: %d -- cid: %s", changeTime, cid)
	return
}

func GetFacesVersion(account, dStr string) (ver int64, err error) {
	key := fmt.Sprintf(FacesVersionFormat, account)
	err = Redis.GetDx(key, &ver, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get account faces version error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get account faces version: %d -- account: %s", ver, account)
	return
}

func SetRegisterResultSess(cid, personID, sess, dStr string) (err error) {

	key := fmt.Sprintf(DeviceRegisterResultFormat, cid, personID)
	err = Redis.SetDx(key, sess, MinuteExtime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set register result session error: %s -- cid: %s -- person id: %s", err, cid, personID)
		return
	}
	logger.DebugDx(dStr, "set register result session: %s -- cid: %s -- person id: %s", sess, cid, personID)
	return
}

func GetRegisterResultSess(cid, personID, dStr string) (sess string, err error) {
	key := fmt.Sprintf(DeviceRegisterResultFormat, cid, personID)
	err = Redis.GetDx(key, &sess, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get register result session error: %s -- cid: %s -- person id: %s", err, cid, personID)
		return
	}
	logger.DebugDx(dStr, "get register result session: %s -- cid: %s -- person id: %s", sess, cid, personID)
	return
}

func SetFacesVersion(account string, ver int64, dStr string) (err error) {

	key := fmt.Sprintf(FacesVersionFormat, account)
	err = Redis.SetDx(key, ver, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set account faces version error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set account faces version: %d -- account: %s", ver, account)
	return
}

func GetCidPackage(isNew bool, cid string, dStr string) (int, int) {

	var value []int
	var key string
	if isNew {
		key = fmt.Sprintf(CidPackageFormatNew, cid)
	} else {
		key = fmt.Sprintf(CidPackageFormat, cid)
	}
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid package id error: %s -- cid: %s", err, cid)
		return 0, 0
	}
	logger.DebugDx(dStr, "get cid package id: %v -- cid: %s", value, cid)
	return value[0], value[1]
}

func SetCidPackage(isNew bool, cid string, pkgId, ttl int, dStr string) {

	var value = []int{}
	value = append(value, pkgId)
	value = append(value, 0)
	var key string
	if isNew {
		key = fmt.Sprintf(CidPackageFormatNew, cid)
	} else {
		key = fmt.Sprintf(CidPackageFormat, cid)
	}
	err := Redis.SetDx(key, value, ttl, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid package id error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set cid package id: %v -- cid: %s", value, cid)
	return
}

func GetGroupPackage(isNew bool, channelId int, dStr string) (value int) {

	if channelId == 0 {
		return
	}
	var key string
	if isNew {
		key = fmt.Sprintf(GroupPackageFormatNew, channelId)
	} else {
		key = fmt.Sprintf(GroupPackageFormat, channelId)
	}
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get channel package id error: %s -- channel id: %d", err, channelId)
		return
	}
	logger.DebugDx(dStr, "get channel package id: %d -- channel id: %d", value, channelId)
	return
}

func SetGroupPackage(isNew bool, channelId int, pkgId int, dStr string) {

	if channelId == 0 {
		return
	}
	var key string
	if isNew {
		key = fmt.Sprintf(GroupPackageFormatNew, channelId)
	} else {
		key = fmt.Sprintf(GroupPackageFormat, channelId)
	}
	err := Redis.SetDx(key, pkgId, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set channel package id error: %s -- channel id: %d", err, channelId)
		return
	}
	logger.DebugDx(dStr, "set channel package id: %d -- -- channel id: %d", pkgId, channelId)
	return
}

func UpdateCidPackage(cid string, dStr string) {

	var value []int
	key := fmt.Sprintf(CidPackageFormat, cid)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "uget cid package id error: %s -- cid: %s", err, cid)
		return
	}
	value[1] = 1
	err = Redis.SetDx(key, value, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "uset cid package id error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "uset cid package id: %v -- cid: %s", value, cid)
	return
}

func GetCidDefaultPackage(region string, os uint32, dStr string) (value int) {

	key := fmt.Sprintf(DefaultPackageFormat, os, region)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid default package id error: %s -- region: %s--os: %d", err, region, os)
		return
	}
	logger.DebugDx(dStr, "get cid default package id: %d -- region: %s--os: %d", value, region, os)
	return
}

func SetCidDefaultPackage(region string, os int, pkgId int, dStr string) {

	key := fmt.Sprintf(DefaultPackageFormat, os, region)
	err := Redis.SetDx(key, pkgId, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid default package id error: %s -- region: %s--os: %d", err, region, os)
		return
	}
	logger.DebugDx(dStr, "set cid default package id: %d -- region: %s--os: %d", pkgId, region, os)
	return
}

func GetCidDefaultPackageEx(isNew bool, region string, os uint32, snPrefix string, dStr string) (value int) {

	var key string
	if isNew {
		key = fmt.Sprintf(DefaultPackageFormatNew, os, snPrefix, region)
	} else {
		key = fmt.Sprintf(DefaultPackageFormatEx, os, snPrefix, region)
	}
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid default package id error: %s -- region: %s--os: %d", err, region, os)
		return
	}
	logger.DebugDx(dStr, "get cid default package id: %d -- region: %s--os: %d", value, region, os)
	return
}

func SetCidDefaultPackageEx(isNew bool, region string, os int, snPrefix string, pkgId int, dStr string) {
	var key string
	if isNew {
		key = fmt.Sprintf(DefaultPackageFormatNew, os, snPrefix, region)
	} else {
		key = fmt.Sprintf(DefaultPackageFormatEx, os, snPrefix, region)
	}
	err := Redis.SetDx(key, pkgId, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid default package id error: %s -- region: %s--os: %d", err, region, os)
		return
	}
	logger.DebugDx(dStr, "set cid default package id: %d -- region: %s--os: %d", pkgId, region, os)
	return
}
func GetCidUpgradeInfo(cid string, dStr string) (int, string) {

	var value []interface{}
	key := fmt.Sprintf(CidUpgradeInfoFormat, cid)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid upgrade id error: %s -- cid: %s", err, cid)
		return 0, ""
	}
	logger.DebugDx(dStr, "get cid upgrade id: %v -- cid: %s", value, cid)
	pkgID, _ := value[0].(int64)
	version, _ := value[1].(string)
	return int(pkgID), version
}

func SetCidUpgradeInfo(cid string, pkgId int, version string, dStr string) {

	var value = []interface{}{}
	value = append(value, pkgId)
	value = append(value, version)
	key := fmt.Sprintf(CidUpgradeInfoFormat, cid)
	err := Redis.SetDx(key, value, MonthExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid upgrade id error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set cid upgrade id: %v -- cid: %s", value, cid)
	return
}

func DelCidUpgradeInfo(cid string, dStr string) error {

	key := fmt.Sprintf(CidUpgradeInfoFormat, cid)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete  upgrade error: %s -- cid: %s", err, cid)
		return err
	}
	logger.DebugDx(dStr, "delete upgrade -- cid: %s", cid)
	return nil
}

func Get4GCidBurnInfo(cid string, dStr string) string {

	var mac string
	key := fmt.Sprintf(Cid4GBurnInfo, cid)
	err := Redis.GetDx(key, &mac, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get 4g cid mac error: %s -- cid: %s", err, cid)
		return mac
	}
	logger.DebugDx(dStr, "get 4g cid mac: %v -- cid: %s", mac, cid)
	return mac
}

func Set4GCidBurnInfo(cid string, mac string, dStr string) {

	key := fmt.Sprintf(Cid4GBurnInfo, cid)
	err := Redis.SetDx(key, mac, OneDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set 4g cid mac error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set 4g cid mac: %s -- cid: %s", mac, cid)
	return
}

func Del4GCidBurnInfo(cid string, dStr string) error {

	key := fmt.Sprintf(Cid4GBurnInfo, cid)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete 4g cid mac error: %s -- cid: %s", err, cid)
		return err
	}
	logger.DebugDx(dStr, "delete 4g cid mac -- cid: %s", cid)
	return nil
}

func AddCid2FreeChatSet(cid string, dStr string) {

	timeStr := time.Now().Format("2006-01-02")
	key := fmt.Sprintf(FreeChatSetFormat, timeStr)
	err := Redis.SaddExpireDx(key, cid, ThreeDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "AddCid2FreeChatSet error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "AddCid2FreeChatSet: %s -- cid: %s", key, cid)
	return
}

func CountFreeChatSet(day string, dStr string) int64 {

	key := fmt.Sprintf(FreeChatSetFormat, day)
	return Redis.Scard(key)
}

// func GetVersionsByOsRegion(os uint32, region string) []string {
// 	var versionList []string
// 	key := fmt.Sprintf(CidVersionListKeyFormat, os, region)
// 	err := Redis.GetDx(key, &versionList, "")
// 	if err != nil {
// 		versionList = []string{}
// 	}
// 	return versionList
// }

// func AddVersionsByOsRegion(os uint32, region string, value string) {
// 	var versionList []string
// 	key := fmt.Sprintf(CidVersionListKeyFormat, os, region)
// 	err := Redis.GetDx(key, &versionList, "")
// 	if err != nil {
// 		versionList = []string{}
// 	}
// 	versionList = append(versionList, value)
// 	Redis.SetDx(key, versionList, 0, "")
// }

// func AddUpGradeCids(key string, value string) {
// 	Redis.Sadd(key, value)
// }

func IsUpgradeCid(key, cid string) bool {
	ok := Redis.Sismember(key, cid)
	if ok == 0 {
		return false
	} else {
		return true
	}
}

func GetEmailOrPhone(account string, dStr string) (err error, value string) {

	key := fmt.Sprintf(SetAccountInfoFormat, account)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get email or phone error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get email or phone: %s -- account: %s--os: %d", value, account)
	return
}

func SetEmailOrPhone(account string, value string, dStr string) {

	key := fmt.Sprintf(SetAccountInfoFormat, account)
	err := Redis.SetDx(key, value, SetAccountInfoExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set email or phone error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set email or phone: %s -- account: %s--os: %d", value, account)
	return
}

func GetAlexaCode(code string, dStr string) (err error, value string) {

	key := fmt.Sprintf(AlexaCodeFormat, code)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get alexa code error: %s -- code: %s", err, code)
		return
	}
	logger.DebugDx(dStr, "get alexa code: %s -- code: %s", value, code)
	return
}

func SetAlexaCode(code string, account string, dStr string) {

	key := fmt.Sprintf(AlexaCodeFormat, code)
	err := Redis.SetDx(key, account, ClientCallerAccExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set alexa code error: %s -- code: %s", err, code)
		return
	}
	logger.DebugDx(dStr, "set alexa code: %s -- code: %s", account, code)
	return
}

func DelAlexaCode(code string, dStr string) error {

	key := fmt.Sprintf(AlexaCodeFormat, code)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete  alexa code error: %s -- code: %s", err, code)
		return err
	}
	logger.DebugDx(dStr, "delete alexa code -- code: %s", code)
	return nil
}

func GetOperateCode(account string, typ int, dStr string) (err error, value string) {
	key := fmt.Sprintf(WebOperateCodeFormat, account, typ)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "GetOperateCode error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "GetOperateCode: %s -- account: %s", value, account)
	return
}
func SetOperateCode(code string, account string, typ int, dStr string) {
	key := fmt.Sprintf(WebOperateCodeFormat, account, typ)
	err := Redis.SetDx(key, code, SMSCodeExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "SetOperateCode error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "SetOperateCode: %s -- account: %s", code, account)
	return
}
func DelOperateCode(account string, typ int, dStr string) error {
	key := fmt.Sprintf(WebOperateCodeFormat, account, typ)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "DelOperateCode error: %s -- account: %s", err, account)
		return err
	}
	logger.DebugDx(dStr, "DelOperateCode -- account: %s", account)
	return nil
}
func GetAlexaAccessToken(token string, dStr string) (err error, value string) {

	key := fmt.Sprintf(AlexaAccessTokenFormat, token)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get alexa token error: %s -- code: %s", err, token)
		return
	}
	logger.DebugDx(dStr, "get alexa token: %s -- code: %s", value, token)
	return
}

func SetAlexaAccessToken(token string, account string, dStr string) {

	key := fmt.Sprintf(AlexaAccessTokenFormat, token)
	err := Redis.SetDx(key, account, OneDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set alexa token error: %s -- token: %s", err, token)
		return
	}
	logger.DebugDx(dStr, "set alexa token: %s -- token: %s", account, token)
	return
}

func GetAccountAIFaceLogo(account string, dStr string) (value string, err error) {

	key := fmt.Sprintf(AIFaceLogoFormat, account)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get aiface logo error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "get aiface logo: %s -- account: %s", value, account)
	return
}

func SetAccountAIFaceLogo(fileName string, account string, dStr string) {

	key := fmt.Sprintf(AIFaceLogoFormat, account)
	err := Redis.SetDx(key, fileName, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set aiface logo error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set aiface logo: %s -- account: %s", fileName, account)
	return
}

func GetDeviceRegion(deviceID, dStr string) (value string, err error) {

	key := fmt.Sprintf(AlexaDeviceRegionFormat, deviceID)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get devie region error: %s -- device id: %s", err, deviceID)
		return
	}
	logger.DebugDx(dStr, "get devie region: %s -- device id: %s", value, deviceID)
	return
}

func SetDeviceRegion(region, deviceID, dStr string) {

	key := fmt.Sprintf(AlexaDeviceRegionFormat, deviceID)
	err := Redis.SetDx(key, region, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set devie region error: %s -- device id: %s", err, deviceID)
		return
	}
	logger.DebugDx(dStr, "set devie region: %s -- device id: %s", region, deviceID)
	return
}

func SetVersionParts(cid string, value []string, dStr string) {

	key := fmt.Sprintf(VersionPartsFormat, cid)
	err := Redis.SetDx(key, value, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set version parts error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set version parts: %v -- cid: %s", value, cid)
	return
}

func GetVersionParts(cid string, dStr string) ([]string, error) {
	var value []string
	key := fmt.Sprintf(VersionPartsFormat, cid)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get version parts error: %s -- cid: %s", err, cid)
		return value, err
	}
	logger.DebugDx(dStr, "get version parts: %s -- cid: %s", value, cid)
	return value, nil
}

func GetSysMailType() int {
	var value int
	err := Redis.GetDx(SysMailType, &value, "")
	if err != nil {
		logger.DebugDx("", "get sys mail type error: %s", err)
		value = 0
	}
	if value > 2 || value < 0 {
		value = 0
	}
	return value
}

func SetCidTypeForBind(cid string, value, timeout int, dStr string) {
	if timeout <= 0 {
		timeout = RandomBindExTime
	}
	key := fmt.Sprintf(CidTypeForBindFormat, cid)
	err := Redis.SetDx(key, value, timeout, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid type for bind error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set cid type for bind: %d -- cid: %s", value, cid)
	return
}

func GetCidTypeForBind(cid string, dStr string) (int, error) {
	var value int
	key := fmt.Sprintf(CidTypeForBindFormat, cid)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid type for bind error: %s -- cid: %s", err, cid)
		return value, err
	}
	logger.DebugDx(dStr, "get cid type for bind: %d -- cid: %s", value, cid)
	return value, nil
}

func DelCidTypeForBind(cid string, dStr string) error {
	key := fmt.Sprintf(CidTypeForBindFormat, cid)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete cid type for bind error: %s -- cid: %s", err, cid)
		return err
	}
	logger.DebugDx(dStr, "delete cid type for bind -- cid: %s", cid)
	return nil
}

func SetCidRegionMoveFlag(cid string, value, timeout int, dStr string) {
	if timeout <= 0 {
		timeout = MinuteExtime
	}
	key := fmt.Sprintf(CidForRegionMoveFormat, cid)
	err := Redis.SetDx(key, value, timeout, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid type for bind error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set cid type for bind: %d -- cid: %s", value, cid)
	return
}

func GetCidRegionMoveFlag(cid string, dStr string) (int, error) {
	var value int
	key := fmt.Sprintf(CidForRegionMoveFormat, cid)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid type for bind error: %s -- cid: %s", err, cid)
		return value, err
	}
	logger.DebugDx(dStr, "get cid type for bind: %d -- cid: %s", value, cid)
	return value, nil
}

func DelCidRegionMoveFlag(cid string, dStr string) error {
	key := fmt.Sprintf(CidForRegionMoveFormat, cid)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete cid type for bind error: %s -- cid: %s", err, cid)
		return err
	}
	logger.DebugDx(dStr, "delete cid type for bind -- cid: %s", cid)
	return nil
}

func SetKvsDeviceButtonDefine(cid string, value interface{}, dStr string) {
	key := fmt.Sprintf(DeviceButtonDefineFormat, DeviceButtonDefineVersion, cid)
	err := Redis.SetDx(key, value, DPDevSetExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set device button define error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set device button define: %v -- cid: %s", value, cid)
	return
}

func GetKvsDeviceButtonDefine(cid string, value interface{}, dStr string) error {
	key := fmt.Sprintf(DeviceButtonDefineFormat, DeviceButtonDefineVersion, cid)
	err := Redis.GetDx(key, value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get  device button define error: %s -- cid: %s", err, cid)
		return err
	}
	logger.DebugDx(dStr, "get device button define: %v -- cid: %s", value, cid)
	return nil
}

func DelKvsDeviceButtonDefine(cid string, dStr string) error {
	key := fmt.Sprintf(DeviceButtonDefineFormat, DeviceButtonDefineVersion, cid)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete device button define error: %s -- cid: %s", err, cid)
		return err
	}
	logger.DebugDx(dStr, "delete  device button define -- cid: %s", cid)
	return nil
}

func SetShareQuickInfo(code string, value interface{}, dStr string) {
	key := fmt.Sprintf(QuickShareFormat, code)
	err := Redis.SetDx(key, value, ShareQuickExtime, dStr)
	if err != nil {
		logger.InfoDx(dStr, "set share quick info error: %s -- code: %s", err, code)
		return
	}
	logger.DebugDx(dStr, "setshare quick info : %v -- code: %s", value, code)
	return
}

func GetShareQuickInfo(code string, value interface{}, dStr string) error {
	key := fmt.Sprintf(QuickShareFormat, code)
	err := Redis.GetDx(key, value, dStr)
	if err != nil {
		logger.InfoDx(dStr, "get share quick info error: %s -- code: %s", err, code)
		return err
	}
	logger.DebugDx(dStr, "get share quick info: %v -- code: %s", value, code)
	return nil
}

func DelShareQuickInfo(code string, dStr string) error {
	key := fmt.Sprintf(QuickShareFormat, code)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.InfoDx(dStr, "delete share quick info error: %s -- code: %s", err, code)
		return err
	}
	logger.DebugDx(dStr, "delete share quick info-- code: %s", code)
	return nil
}

func SetOpenLoginInfo(openID string, value interface{}, dStr string) {
	key := fmt.Sprintf(OpenLoginFormat, openID)
	err := Redis.SetDx(key, value, OpenLoginExtime, dStr)
	if err != nil {
		logger.InfoDx(dStr, "set open login info error: %s -- openID: %s", err, openID)
		return
	}
	logger.DebugDx(dStr, "setopen login info : %v -- openID: %s", value, openID)
	return
}

func GetOpenLoginInfo(openID string, value interface{}, dStr string) error {
	key := fmt.Sprintf(OpenLoginFormat, openID)
	err := Redis.GetDx(key, value, dStr)
	if err != nil {
		logger.InfoDx(dStr, "get open login info error: %s -- openID: %s", err, openID)
		return err
	}
	logger.DebugDx(dStr, "get open login info: %v -- openID: %s", value, openID)
	return nil
}

func DelOpenLoginInfo(openID string, dStr string) error {
	key := fmt.Sprintf(OpenLoginFormat, openID)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.InfoDx(dStr, "delete open login info error: %s -- openID: %s", err, openID)
		return err
	}
	logger.DebugDx(dStr, "delete open login info-- openID: %s", openID)
	return nil
}

func SetQrLoginData(random string, value interface{}, dStr string) error {
	key := fmt.Sprintf(QrLoginFormat, random)
	err := Redis.SetDx(key, value, QrLoginTimeOut+10, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set qrlogin error: %s -- random: %s", err, random)
		return err
	}
	logger.DebugDx(dStr, "set qrlogin: %v -- random: %s", value, random)
	return nil
}

func GetQrLoginData(random string, value interface{}, dStr string) error {
	key := fmt.Sprintf(QrLoginFormat, random)
	err := Redis.GetDx(key, value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get qrlogin error: %s -- random: %s", err, random)
		return err
	}
	logger.DebugDx(dStr, "get qrlogin: %v -- random: %s", value, random)
	return nil
}

func GetCidVipVisitNumber(account, cid string, endTime int64, typ int, dStr string) (int, error) {
	var value int
	key := fmt.Sprintf(CidVipVisitNumberFormat, account, cid, endTime, typ)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid vip visit number error: %s -- cid: %s", err, cid)
		return value, err
	}
	logger.DebugDx(dStr, "get cid vip visit number: %d -- cid: %s", value, cid)
	return value, nil
}

func SetCidVipVisitNumber(account, cid string, endTime int64, typ, value int, dStr string) error {
	key := fmt.Sprintf(CidVipVisitNumberFormat, account, cid, endTime, typ)
	err := Redis.SetDx(key, value, OneDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid vip visit number error: %s -- cid: %s", err, cid)
		return err
	}
	logger.DebugDx(dStr, "set cid vip visit number -- cid: %s", cid)
	return nil
}

func GetWebAuthToken(account, authToken, dStr string) (value string, err error) {
	key := fmt.Sprintf(WebAuthToken, account, authToken)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get account authToken error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "get account authToken key: %s", key)
	return
}

func DelWebAuthToken(account, authToken, dStr string) (err error) {
	key := fmt.Sprintf(WebAuthToken, account, authToken)
	err = Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "del account authToken error: %s -- key: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "del account authToken key: %s", key)
	return
}

func ClearWebAccountAuthToken(account, dStr string) {
	keys := Redis.Keys(fmt.Sprintf(WebAuthToken, account, "*"))
	for _, key := range keys {
		Redis.DelDx(key, dStr)
	}

	logger.DebugDx(dStr, "clear account authToken account: %s", account)
	return
}

func SetWebAuthToken(account, authToken string, ttl int, dStr string) (err error) {
	key := fmt.Sprintf(WebAuthToken, account, authToken)
	err = Redis.SetDx(key, authToken, ttl, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set account authToken error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "set account authToken : %s -- key: %s", authToken, key)
	return
}
func GetWebAuthTokenTtl(account, authToken, dStr string) (ttl int) {
	key := fmt.Sprintf(WebAuthToken, account, authToken)
	ttl = Redis.TTL(key, dStr)
	return
}

func GetH5AuthToken(authToken, dStr string) (value string, err error) {
	key := fmt.Sprintf(H5AuthToken, authToken)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get h5 authToken error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "get h5 authToken key: %s", key)
	return
}

func SetH5AuthToken(account, authToken, dStr string) (err error) {
	key := fmt.Sprintf(H5AuthToken, authToken)
	err = Redis.SetDx(key, account, OneDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set h5 authToken error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "set h5 authToken value: %s -- key: %s", account, key)
	return
}

func DelH5AuthToken(account, authToken, dStr string) (err error) {
	key := fmt.Sprintf(H5AuthToken, authToken)
	err = Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "del h5 authToken error: %s -- key: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "del h5 authToken key: %s", key)
	return
}

func GetIosInAppPurchase(authToken, dStr string) (value string, err error) {
	key := fmt.Sprintf(IosInAppPurchase, authToken)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get InAppPurchase error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "get InAppPurchase key: %s", key)
	return
}

func SetIosInAppPurchase(inApp, authToken, dStr string) (err error) {
	key := fmt.Sprintf(IosInAppPurchase, authToken)
	err = Redis.SetDx(key, inApp, OneDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set InAppPurchase error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "set InAppPurchase value: %s -- key: %s", inApp, key)
	return
}

func GetWebWrongPasswordTimes(account, dStr string) (value int, err error) {
	key := fmt.Sprintf(WebWrongPasswordTimes, account)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get WrongPasswordTimes error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "get WrongPasswordTimes key: %s -- value: %d", key, value)
	return
}
func SetWebWrongPasswordTimes(account string, num int, dStr string) (err error) {
	key := fmt.Sprintf(WebWrongPasswordTimes, account)
	err = Redis.SetDx(key, num, OneHourExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set WrongPasswordTimes error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "set WrongPasswordTimes value: %d -- key: %s", num, key)
	return
}
func GetTrialPeriodDays(dStr string) (value int, err error) {

	err = Redis.GetDx(WebTrialPeriodDays, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get trial period days error: %s -- key: %s", err, WebTrialPeriodDays)
		return
	}
	logger.DebugDx(dStr, "get trial period days key: %s", WebTrialPeriodDays)
	return
}

func SetTrialPeriodDays(days int, dStr string) (err error) {

	err = Redis.SetDx(WebTrialPeriodDays, days, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set trial period days error: %s -- key: %s", err, WebTrialPeriodDays)
		return
	}
	logger.DebugDx(dStr, "set trial period days: %d -- key: %s", days, WebTrialPeriodDays)
	return
}

func GetTrialPeriodEnable(dStr string) (value int, err error) {

	err = Redis.GetDx(WebTrialPeriodEnable, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get trial period enable error: %s -- key: %s", err, WebTrialPeriodEnable)
		return
	}
	logger.DebugDx(dStr, "get trial period enable key: %s", WebTrialPeriodEnable)
	return
}

func SetTrialPeriodEnable(enable int, dStr string) (err error) {

	err = Redis.SetDx(WebTrialPeriodEnable, enable, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set trial period enable error: %s -- key: %s", err, WebTrialPeriodEnable)
		return
	}
	logger.DebugDx(dStr, "set trial period enable: %d -- key: %s", enable, WebTrialPeriodEnable)
	return
}

func GetCSDefaultRegion(dStr string) (value string, err error) {
	err = Redis.GetDx(CloudStorageDefaultRegion, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cloud storage default region error: %s -- key: %s", err, CloudStorageDefaultRegion)
		return
	}
	logger.DebugDx(dStr, "get cloud storage default region key: %s -- value: %s", CloudStorageDefaultRegion, value)
	return
}
func SetCSDefaultRegion(region string, dStr string) (err error) {
	err = Redis.SetDx(CloudStorageDefaultRegion, region, KVSForeverTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cloud storage default region error: %s -- key: %s", err, CloudStorageDefaultRegion)
		return
	}
	logger.DebugDx(dStr, "set cloud storage default region: %s -- key: %s", region, CloudStorageDefaultRegion)
	return
}

func GetWebLoginTempToken(token, dStr string) (value string, err error) {
	key := fmt.Sprintf(WebLoginTempToken, token)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get login temp token error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "get login temp token key: %s -- value: %s", key, value)
	return
}
func DelWebLoginTempToken(token, dStr string) (err error) {
	key := fmt.Sprintf(WebLoginTempToken, token)
	err = Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "del login temp token error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "del login temp token key: %s", key)
	return
}
func SetWebLoginTempToken(token, account, dStr string) (err error) {
	key := fmt.Sprintf(WebLoginTempToken, token)
	err = Redis.SetDx(key, account, OneHourExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set login temp token error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "set login temp token value: %s -- key: %s", account, key)
	return
}

func GetWebLoginPhoneCodeTime(account, dStr string) (value int64, err error) {
	key := fmt.Sprintf(WebLoginPhoneCodeTimeFormat, account)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get login phone code time error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "get login phone code time key: %s -- value: %d", key, value)
	return
}

func SetWebLoginPhoneCodeTime(account string, time int64, dStr string) (err error) {
	key := fmt.Sprintf(WebLoginPhoneCodeTimeFormat, account)
	err = Redis.SetDx(key, time, OneDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set login phone code time error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "set login phone code time value: %d -- key: %s", time, key)
	return
}

func GetWebStatFlag(time int64, dStr string) (value int, err error) {
	key := fmt.Sprintf(WebStatFlagFormat, time)
	err = Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get web stat falg error: %s -- key: %s", err, key)
		value = 0
		return
	}
	logger.DebugDx(dStr, "get web stat falg: %s -- value: %d", key, value)
	return
}
func SetWebStatFlag(time int64, num int, dStr string) (err error) {
	key := fmt.Sprintf(WebStatFlagFormat, time)
	err = Redis.SetDx(key, num, ThreeDayExTime, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set web stat falg error: %s -- key: %s", err, key)
		return
	}
	logger.DebugDx(dStr, "set web stat falg value: %d -- key: %s", num, key)
	return
}

func SetCidChatFreeNumber(cid string, value, timeout int, dStr string) {
	key := fmt.Sprintf(CidChatFreeNumberFormat, cid)
	err := Redis.SetDx(key, value, timeout, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set cid chat free number error: %s -- cid: %s", err, cid)
		return
	}
	logger.DebugDx(dStr, "set cid chat free number: %d -- cid: %s", value, cid)
	return
}

func GetCidChatFreeNumber(cid string, dStr string) (int, error) {
	var value int = 0
	key := fmt.Sprintf(CidChatFreeNumberFormat, cid)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get cid chat free number error: %s -- cid: %s", err, cid)
		return value, err
	}
	logger.DebugDx(dStr, "get cid chat free number: %d -- cid: %s", value, cid)
	return value, nil
}

func GetAccountSession(account string, dStr string) string {

	var value string
	key := fmt.Sprintf(MiruAccountSessionFormat, account)
	err := Redis.GetDx(key, &value, dStr)
	if err != nil {
		logger.DebugDx(dStr, "get account session id error: %s -- account: %s", err, account)
		return ""
	}
	logger.DebugDx(dStr, "get account session id: %s -- account: %s", value, account)
	return value
}

func SetAccountSession(account, sessionID string, ttl int, dStr string) {

	key := fmt.Sprintf(MiruAccountSessionFormat, account)
	err := Redis.SetDx(key, sessionID, ttl, dStr)
	if err != nil {
		logger.DebugDx(dStr, "set account session id error: %s -- account: %s", err, account)
		return
	}
	logger.DebugDx(dStr, "set account session id: %s -- account: %s", sessionID, account)
	return
}

func DelAccountSession(account string, dStr string) error {

	key := fmt.Sprintf(MiruAccountSessionFormat, account)
	err := Redis.DelDx(key, dStr)
	if err != nil {
		logger.DebugDx(dStr, "delete account session id error: %s -- account: %s", err, account)
		return err
	}
	logger.DebugDx(dStr, "delete account: %s session id", account)
	return nil
}

func ExpireAccountSession(account string, ttl int) error {

	key := fmt.Sprintf(MiruAccountSessionFormat, account)
	Redis.Expire(key, ttl)
	logger.DebugDx("Expire account: %s session id", account)
	return nil
}

func Str2Double(str string) (ret float64, err error) {
	ss := strings.Split(str, ".")
	intVal, ok := strconv.Atoi(ss[0])
	if ok != nil {
		return 0.0, ok
	}
	ret = float64(intVal) * 1.0
	if len(ss) <= 1 {
		return ret, nil
	}

	var w float64 = 0.1
	fStr := ss[1]
	for i := 0; i < len(fStr); i++ {
		tmp, _ := strconv.Atoi(string(fStr[i]))
		ret += float64(tmp) * w
		w *= 0.1
	}
	return ret, nil
}

func KVSModelRegister(model interface{}) (orderCol []string, orderVal []interface{}, colNum, pkNum int) {
	val := reflect.ValueOf(model)
	if val.Kind() != reflect.Ptr {
		panic(fmt.Errorf("<KVSObjectRegister> cannot use non-ptr"))
	}

	var i int = 0
	ind := reflect.Indirect(val)
	for ; i < ind.NumField(); i++ {
		//field := ind.Field(i)
		sf := ind.Type().Field(i)
		if sf.PkgPath != "" || sf.Anonymous || sf.Tag == "" {
			break
		}

		tagStr := sf.Tag.Get("kvs")
		if tagStr == "" {
			tagStr = string(sf.Tag)
		}

		tagList := strings.Split(tagStr, ";")
		if len(tagList) >= 3 {
			pkNum += 1
		}
		logger.Debug("------tag:%v list:%v", sf.Tag, tagList)
		orderCol = append(orderCol, tagList[0])
		orderVal = append(orderVal, tagList[1])

		//logger.Info("%v", sf.Type.Kind())
		switch typ := sf.Type.Kind(); typ {
		case reflect.Bool:
			num, _ := strconv.Atoi(tagList[1])
			ind.Field(i).SetBool(num > 0)
		case reflect.String:
			ind.Field(i).SetString(tagList[1])
		case reflect.Float32, reflect.Float64:
			ret, _ := Str2Double(tagList[1])
			ind.Field(i).SetFloat(ret)
		case reflect.Int16, reflect.Int32, reflect.Int, reflect.Int64:
			num, _ := strconv.Atoi(tagList[1])
			ind.Field(i).SetInt(int64(num))
		case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			num, _ := strconv.Atoi(tagList[1])
			ind.Field(i).SetUint(uint64(num))
		default:
			panic(fmt.Errorf("Current do not support type %v", typ))
		}
	}

	colNum = i
	return
}

// KVSBase
// inst is pointer
func (this *KVSBase) initInstanceData(inst interface{}, key ...interface{}) {
	var tmp []interface{}
	for _, val := range this._oldVal {
		tmp = append(tmp, val)
	}
	this._oldVal = tmp

	this._inst = inst
	this.SetPk(key...)
}
func (this *KVSBase) getExTime() int {
	//是session表格，cid设置成永不失效
	if this._table == nil {
		cid, _ := this._oldVal[0].(string)
		len := len(cid)
		if len == CidLength || len == CidLengthV2 {
			return DPDevSetExTime
		}
	}
	return this._kvsExTime
}

func delKvs(key string) {
	if err := recover(); err != nil {
		Redis.Del(key)
		LogStack(err)
	}
}
func (this *KVSBase) setVal(iBegin, iLen int, val ...interface{}) {
	refVal := reflect.ValueOf(this._inst)
	ind := reflect.Indirect(refVal)

	defer delKvs(this.GetKVSKey(""))

	var intT int
	var int64T int64
	var fT float64
	var bT bool
	var sT string
	var uint64T uint64
	for i, j := iBegin, 0; i < iLen && j < len(val); i, j = i+1, j+1 {
		sf := ind.Type().Field(i)
		valTyp := reflect.TypeOf(val[j]).Kind()

		//logger.Debug("field %s %v", this._cols[i], val[j])
		switch typ := sf.Type.Kind(); typ {
		case reflect.Bool:
			switch valTyp {
			case reflect.String:
				intT, _ = strconv.Atoi(val[j].(string))
				bT = intT > 0
			default:
				bT = val[j].(bool)
			}
			ind.Field(i).SetBool(bT)
		case reflect.String:
			if valTyp == reflect.String {
				sT = val[j].(string)
			} else {
				switch valTyp {
				case reflect.Int16:
					intT = int(val[j].(int16))
				case reflect.Int32:
					intT = int(val[j].(int32))
				case reflect.Int:
					intT = val[j].(int)
				case reflect.Int64:
					intT = int(val[j].(int64))
				case reflect.Uint32:
					intT = int(val[j].(uint32))
				}
				sT = strconv.Itoa(intT)
			}
			ind.Field(i).SetString(sT)
			this._oldVal[i] = val[j]
		case reflect.Float32, reflect.Float64:
			switch valTyp {
			case reflect.String:
				fT, _ = Str2Double(val[j].(string))
			default:
				fT = val[j].(float64)
			}
			ind.Field(i).SetFloat(fT)
			this._oldVal[i] = fT
		case reflect.Int16, reflect.Int32, reflect.Int, reflect.Int64:
			switch valTyp {
			case reflect.String:
				intT, _ = strconv.Atoi(val[j].(string))
				int64T = int64(intT)
			case reflect.Int16:
				int64T = int64(val[j].(int16))
			case reflect.Int32:
				int64T = int64(val[j].(int32))
			case reflect.Int:
				int64T = int64(val[j].(int))
			case reflect.Int64:
				int64T = val[j].(int64)
			case reflect.Uint32:
				int64T = int64(val[j].(uint32))
			}
			ind.Field(i).SetInt(int64T)
			this._oldVal[i] = int64T
		case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
			switch valTyp {
			case reflect.String:
				intT, _ = strconv.Atoi(val[j].(string))
				uint64T = uint64(intT)
			case reflect.Int16:
				uint64T = uint64(val[j].(int16))
			case reflect.Int32:
				uint64T = uint64(val[j].(int32))
			case reflect.Int:
				uint64T = uint64(val[j].(int))
			case reflect.Int64:
				uint64T = uint64(val[j].(int64))
			case reflect.Uint32:
				uint64T = uint64(val[j].(uint32))
			}
			ind.Field(i).SetUint(uint64T)
			this._oldVal[i] = uint64T
		default:
			panic(fmt.Errorf("Current do not support type %v", typ))
		}
	}
}
func (this *KVSBase) getVal(iBegin, iLen int, bSet bool) (ret []interface{}) {
	refVal := reflect.ValueOf(this._inst)
	ind := reflect.Indirect(refVal)

	for i := iBegin; i < iLen; i += 1 {
		field := ind.Field(i)
		sf := ind.Type().Field(i)

		var val interface{}
		switch typ := sf.Type.Kind(); typ {
		case reflect.Bool:
			val = field.Bool()
		case reflect.String:
			val = field.String()
		case reflect.Float32, reflect.Float64:
			val = field.Float()
		case reflect.Int16, reflect.Int32, reflect.Int, reflect.Int64:
			val = field.Int()
		case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
			val = field.Uint()
		default:
			panic(fmt.Errorf("Current do not support type %v", typ))
		}
		ret = append(ret, val)
		if bSet {
			this._oldVal[i] = val
		}
	}
	return
}
func (this *KVSBase) getDiffVal(iBegin, iLen int, getNet bool) (cols []string, oldVal, newVal []interface{}) {
	refVal := reflect.ValueOf(this._inst)
	ind := reflect.Indirect(refVal)

	var val interface{}
	for i := iBegin; i < iLen; i += 1 {
		field := ind.Field(i)
		sf := ind.Type().Field(i)

		switch typ := sf.Type.Kind(); typ {
		case reflect.Bool:
			val = field.Bool()
		case reflect.Float32, reflect.Float64:
			val = field.Float()
		case reflect.String:
			val = field.String()
		case reflect.Int16, reflect.Int32, reflect.Int, reflect.Int64:
			val = field.Int()
		case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
			val = field.Uint()
		default:
			panic(fmt.Errorf("Current do not support type %v", typ))
		}

		if val != this._oldVal[i] || this._cols[i] == "region" ||
			getNet && (this._cols[i] == "net" || this._cols[i] == "account" || this._cols[i] == "device_token") {
			cols = append(cols, this._cols[i])
			oldVal = append(oldVal, this._oldVal[i])
			newVal = append(newVal, val)
		}
	}
	return
}
func (this *KVSBase) SetPk(key ...interface{}) {
	this.setVal(0, this._pkNum, key...)
	this.setKVSKey(key...)
}
func (this *KVSBase) setKVSKey(v ...interface{}) {
	if len(v) == 0 {
		return
	}

	this._kvsKey = this._prefix
	for i, val := range v {
		if i >= this._pkNum {
			return
		}
		this._kvsKey = fmt.Sprintf("%s%s%v", this._kvsKey, KeySep, val)
	}
}
func (this *KVSBase) getParsepk(strPk string) []string {
	tmp := strings.Split(strPk, KeySep)
	return tmp[1 : 1+this._pkNum]
}

func (this *KVSBase) pack() (ret []interface{}) {
	ret = this.getVal(this._pkNum, this._colNum, true)
	return
}
func (this *KVSBase) unpack(val ...interface{}) {
	this.setVal(this._pkNum, this._colNum, val...)
}
func (this *KVSBase) getPKRawSqlByField(getType int) (sql string, val []interface{}) {
	switch getType {
	case GetSelectSql:
		sql = fmt.Sprintf("select * from %v where", this._table)
	case GetDeleteSql:
		sql = fmt.Sprintf("delete from %v where", this._table)
	case GetInsertSql:
		valF := ""
		for i := 0; i < this._colNum-1; i++ {
			valF += "?,"
		}
		valF += "?"
		sql = fmt.Sprintf("insert into %v(%s) values(%s);", this._table, strings.Join(this._cols, ","), valF)
		return sql, this._oldVal
	case GetUpdateSql:
		cols, _, new := this.getDiffVal(this._pkNum, this._colNum, false)
		if len(cols) == 0 {
			return
		}

		sql = fmt.Sprintf("update %v set", this._table)
		for i := 0; i < len(cols); i++ {
			if i != 0 {
				sql += ","
			}
			sql += fmt.Sprintf(" %s=?", cols[i])
			val = append(val, new[i])
		}
		sql += " where"
	default:
		panic(fmt.Errorf("Can't format by this getType[%d]", getType))
		return
	}

	for i := 0; i < this._pkNum; i++ {
		if i != 0 {
			sql += " and"
		}
		sql += fmt.Sprintf(" %s=?", this._cols[i])
		val = append(val, this._oldVal[i])
	}
	sql += ";"
	return
}
func (this *KVSBase) getPKRawSqlByValue(getType int) (sql string, val []interface{}) {
	switch getType {
	case GetSelectSql:
		sql = fmt.Sprintf("select * from %v where", this._table)
	case GetDeleteSql:
		sql = fmt.Sprintf("delete from %v where", this._table)
	default:
		panic(fmt.Errorf("Can't format by this getType[%d]", getType))
		return
	}

	for i := 0; i < this._pkNum; i++ {
		if i != 0 {
			sql += " and"
		}
		sql += fmt.Sprintf(" %s=?", this._cols[i])
		val = append(val, this._oldVal[i])
		//switch formatType {
		//case FormatStringField:
		//	sql += fmt.Sprintf("\"%v\"", this._oldVal[i])
		//case FormatIntField:
		//	sql += fmt.Sprintf("%v", this._oldVal[i])
		//default:
		//	panic(fmt.Errorf("Can't format by this formatType[%d]", getType))
		//	return
		//}
	}
	sql += ";"
	return
}

func (this *KVSBase) Exist() bool {
	if Redis.ExistDx(this.GetKVSKey(""), this._dStr) {
		return true
	}
	return this.get(true) == nil
}

// Get 提供给外部调用，如果需要设置true，则上层重新实现接口
func (this *KVSBase) Get() error {
	return this.get(false)
}
func (this *KVSBase) get(passKVS bool) error {
	if !passKVS {
		key := this.GetKVSKey("")
		if key == "" {
			logger.InfoDx(this._dStr, "Base get key is null")
			return errors.New("Key is null")
		}

		var val []interface{}
		if err := Redis.GetDx(key, &val, this._dStr); err == nil {
			//logger.DebugDx(this._dStr, "-----[%s] val %v", key, val)
			this.unpack(val...)
			return nil
		}
	}

	if this._table == nil {
		return errors.New("Table is null")
	}

	sql, val := this.getPKRawSqlByValue(GetSelectSql)
	datas, _, err := DB.RawValuesDx(this._dStr, sql, val...)
	logger.Info("Get kvs query sql [%s] %v", sql, val)
	if err != nil || len(datas) == 0 {
		return fmt.Errorf("Mysql have not data by sql %s %v %v", sql, val, err)
	}

	var unpackData []interface{}
	mapVal := datas[0]
	for i := this._pkNum; i < this._colNum; i++ {
		a, ok := mapVal[this._cols[i]]
		if ok {
			unpackData = append(unpackData, a)
		} else {
			return fmt.Errorf("Field[%s] not find", this._cols[i])
		}
	}
	//logger.Info("Get kvs map from sql [%s] %v", this.GetKVSKey(""), mapVal)

	logger.Debug("Get kvs [%s] unpackData %+v", this.GetKVSKey(""), unpackData)
	this.unpack(unpackData...)
	Redis.SetDx(this.GetKVSKey(""), this._oldVal[this._pkNum:], this.getExTime(), this._dStr)
	logger.Debug("Get kvs from sql [%s] %v", this.GetKVSKey(""), this._oldVal[this._pkNum:])
	return nil
}

// Save 默认保存到DB，如果不保存，需要上层重新实现
func (this *KVSBase) Save(insertDB bool) error {
	return this.save(true, insertDB)
}
func (this *KVSBase) save(saveDB bool, insertDB bool) error {
	key := this.GetKVSKey("")
	if len(key) == 0 {
		return errors.New("Key is null")
	}

	if saveDB && this._table != nil {
		getType := GetUpdateSql
		if insertDB {
			getType = GetInsertSql
			this.pack()
		}

		sql, val := this.getPKRawSqlByField(getType)
		logger.DebugDx(this._dStr, "---sql %s %v", sql, val)
		if sql == "" { // none data to insert or update
			return nil
		}
		err := DB.RawExecDx(this._dStr, sql, val...)
		if err == nil && GetUpdateSql == getType {
			Redis.DelDx(key, this._dStr)
		}
		return err
	} else {
		this.pack()
		return Redis.SetDx(key, this._oldVal[this._pkNum:], this.getExTime(), this._dStr)
	}
}

// func (this *KVSBase) save(saveDB bool, insertDB bool) error {
// 	key := this.GetKVSKey("")
// 	if len(key) == 0 {
// 		return errors.New("Key is null")
// 	}

// 	var hasPacked bool = false
// 	if saveDB && this._table != nil {
// 		getType := GetUpdateSql
// 		if insertDB {
// 			getType = GetInsertSql
// 			hasPacked = true
// 			this.pack()
// 		}

// 		sql, val := this.getPKRawSqlByField(getType)
// 		logger.DebugDx(this._dStr, "---sql %s %v", sql, val)
// 		if sql == "" { // none data to insert or update
// 			return nil
// 		}
// 		err := DB.RawExecDx(this._dStr, sql, val...)
// 		if err != nil {
// 			Redis.DelDx(key, this._dStr)
// 			return err
// 		}
// 	}

// 	if !hasPacked {
// 		this.pack()
// 	}

// 	return Redis.SetDx(key, this._oldVal[this._pkNum:], this.getExTime(), this._dStr)
// }
func (this *KVSBase) Delete(dbDel bool) {
	key := this.GetKVSKey("")
	if key != "" {
		if err := Redis.Del(key); err != nil {
			time.Sleep(500 * time.Microsecond)
			Redis.DelDx(key, this._dStr)
		}
	}

	if dbDel && this._table != nil {
		sql, val := this.getPKRawSqlByValue(GetDeleteSql)
		if err := DB.RawExecDx(this._dStr, sql, val...); err != nil {
			time.Sleep(500 * time.Microsecond)
			DB.RawExecDx(this._dStr, sql, val...)
		}
	}
}

func (this *KVSBase) GetKVSKey(in ...interface{}) string {
	/*if len(in) == 0 {
		return this._kvsKey
	}*/
	if len(in) == 0 || in[0] == "" {
		return this._kvsKey
	}

	tmpKey := this._prefix
	for i, val := range in {
		if i >= this._pkNum {
			break
		}
		tmpKey = fmt.Sprintf("%s%s%v", tmpKey, KeySep, val)
	}

	return tmpKey
}

// func (this *KVSBase) GetKVSKey(in interface{}) string {
// 	if in != "" {
// 		return fmt.Sprintf("%s%s%v", this._prefix, KeySep, in)

// 	}
// 	return this._kvsKey
// }
func (this *KVSBase) SetDebugStr(dStr string) {
	this._dStr = dStr
}

func (this *KVSBase) GetTTL() (int64, error) {
	return Redis.Int64(Redis.DoCommand("", "TTL", this.GetKVSKey("")))
}

func (this *KVSBase) GetSetTime() int64 {
	ttl, _ := this.GetTTL()
	if ttl <= 0 {
		return 0
	}
	return time.Now().Unix() - (int64(this.getExTime()) - ttl)
}

//KVSSession
func (this *KVSSession) getSetAcc(account string) string {
	if account == "" {
		account = this.Account
	}
	if account == "" {
		return ""
	}
	return fmt.Sprintf("%s%d%s%s", this.headAcc, this._kvsVersion, KeySep, account)
}
func (this *KVSSession) getSetToken(token string) string {
	if token == "" {
		token = this.DeviceToken
	}
	if token == "" {
		return ""
	}
	return fmt.Sprintf("%s%d%s%s", this.headToken, this._kvsVersion, KeySep, token)
}
func (this *KVSSession) GetSeq() SEQ      { return SEQ(this.Seq) }
func (this *KVSSession) GetRoute() string { return this.Route }
func (this *KVSSession) ClearSeqRoute()   { this.Seq = 0; this.Route = "" }
func (this *KVSSession) ClearSeq()        { this.Seq = 0 }
func (this *KVSSession) Save(seqT SEQ, route string, isForce, getNet bool) error {
	seq := int64(seqT)
	//如果不是强制保存，判断seq，route是否相同，如果满足有效且不同，不能保存，需要检查，可能导致冲突覆盖
	if !isForce {
		if (seq > 0 && seq != this.Seq) || (route != "" && route != this.Route) {
			// panic(fmt.Sprintf("Check Save error [%v:%v] != [%v:%v]", seq, route, this.Seq, this.Route))
			tip := fmt.Sprintf("Check Save error [%v:%v] != [%v:%v]", seq, route, this.Seq, this.Route)
			logger.Info(tip)
			return errors.New(tip)
		}
	} else { //强制保存，
		if seq > 0 && seq != this.Seq {
			this.Seq = seq
		}
		if route != "" && route != this.Route {
			this.Route = route
		}
	}

	sessid := this.Sessid
	sessidLen := len(sessid)
	cols, old, new := this.getDiffVal(this._pkNum, this._colNum, getNet)
	for i := 0; i < len(cols); i++ {
		switch cols[i] {
		case "net":
			if this.Route == "" {
				continue

			}
			// typeKey, vidKey, setKey := "", "", fmt.Sprintf(OnlineClientKey, this.Route)
			typeKey, vidKey := "", ""
			if IsDog(sessid) {
				// setKey = fmt.Sprintf(OnlineDogKey, this.Route)
				typeKey = fmt.Sprintf(OnlineTypeOSPrefix, this.Route, this.Os)
			}
			if sessidLen == CidLengthV2 {
				vidKey = fmt.Sprintf(OnlineTypeVIDPrefix, this.Route, sessid[:4])
			}

			if this.Net <= NetOffline {
				// Redis.SremDx(setKey, sessid, this._dStr)
				Redis.SremDx(typeKey, sessid, this._dStr)
				Redis.SremDx(vidKey, sessid, this._dStr)
			} else {
				// Redis.SaddExpireDx(setKey, sessid, this.getExTime(), this._dStr)
				Redis.SaddExpireDx(typeKey, sessid, this.getExTime(), this._dStr)
				Redis.SaddExpireDx(vidKey, sessid, this.getExTime(), this._dStr)
			}
		case "account":
			if !IsDog(sessid) {
				key := this.GetKVSKey("")
				Redis.SremDx(this.getSetAcc(old[i].(string)), key, this._dStr)
				Redis.SaddExpireDx(this.getSetAcc(""), key, this.getExTime(), this._dStr)
			}
		case "device_token":
			key := this.GetKVSKey("")
			Redis.SremDx(this.getSetToken(old[i].(string)), key, this._dStr)
			Redis.SaddExpireDx(this.getSetToken(new[i].(string)), key, this.getExTime(), this._dStr)
		case "route":
			route := old[i].(string)
			if route == "" {
				continue
			}
			// typeKey, vidKey, setKey := "", "", fmt.Sprintf(OnlineClientKey, route)
			typeKey, vidKey := "", ""
			if sessidLen == CidLength || sessidLen == CidLengthV2 {
				//setKey = fmt.Sprintf(OnlineDogKey, route)
				typeKey = fmt.Sprintf(OnlineTypeOSPrefix, route, this.Os)
			}
			if sessidLen == CidLengthV2 {
				vidKey = fmt.Sprintf(OnlineTypeVIDPrefix, route, sessid[:4])
			}

			Redis.SremDx(typeKey, sessid, this._dStr)
			Redis.SremDx(vidKey, sessid, this._dStr)
			//Redis.SremDx(setKey, sessid, this._dStr)
		}
	}
	return this.save(false, false)
}
func (this *KVSSession) Delete() {
	key := this.GetKVSKey("")
	if key == "" {
		return
	}

	if err := Redis.DelDx(key, this._dStr); err != nil {
		time.Sleep(500 * time.Microsecond)
		Redis.DelDx(key, this._dStr)
	}

	sessidLen := len(this.Sessid)
	if sessidLen == CidLength || sessidLen == CidLengthV2 {
		Redis.SremDx(fmt.Sprintf(OnlineTypeOSPrefix, this.Route, this.Os), key, this._dStr)
		// Redis.SremDx(fmt.Sprintf(OnlineDogKey, this.Route), key, this._dStr)
	} else {
		// Redis.SremDx(fmt.Sprintf(OnlineClientKey, this.Route), key, this._dStr)
	}
	if sessidLen == CidLengthV2 {
		Redis.SremDx(fmt.Sprintf(OnlineTypeVIDPrefix, this.Route, this.Sessid[:4]), key, this._dStr)
	}

	Redis.SremDx(this.getSetAcc(""), key, this._dStr)
	Redis.SremDx(this.getSetToken(""), key, this._dStr)
}
func (this *KVSSession) Rename(newPK string) {
	newKey := this.GetKVSKey(newPK)
	oldKey := this.GetKVSKey("")
	if oldKey == newKey {
		return
	}

	Redis.RenameDx(oldKey, newKey, this._dStr)
	setKey := this.getSetAcc("")
	Redis.SremDx(setKey, oldKey, this._dStr)
	Redis.SaddExpireDx(setKey, newKey, this.getExTime(), this._dStr)
	setKey = this.getSetToken("")
	Redis.SremDx(setKey, oldKey, this._dStr)
	Redis.SaddExpireDx(setKey, newKey, this.getExTime(), this._dStr)

	// clientKey := fmt.Sprintf(OnlineClientKey, this.Route)
	// Redis.SremDx(clientKey, oldKey, this._dStr)
	// Redis.SaddExpireDx(clientKey, newKey, this.getExTime(), this._dStr)
}
func (this *KVSSession) Filter(params map[string]interface{}) (rets []*KVSSession) {
	var setKey string
	var distinct bool = false
	var deviceToken string
	var account string
	var distinctArr []string
	// var getIHome bool = false
	// var getOnlyIHome bool = false
	var online bool = false
	var all bool = false

	// temp, ok := params["getIHome"]
	// if ok {
	// 	getIHome = temp.(bool)
	// }
	// temp, ok = params["getOnlyIHome"]
	// if ok {
	// 	getOnlyIHome = temp.(bool)
	// }

	temp, ok := params["online"]
	if ok {
		online = temp.(bool)
	}

	temp, ok = params["all"]
	if ok {
		all = true
	}

	accTmp, ok := params["account"]
	if ok {
		account = accTmp.(string)
		setKey = this.getSetAcc(account)
		_, distinct = params["deviceToken"]
	} else {
		tmp, _ := params["deviceToken"]
		deviceToken, _ = tmp.(string)
		setKey = this.getSetToken(deviceToken)
	}

	var bos bool = false
	os, ok := params["os"]
	if ok {
		bos = true
	}

	keys := Redis.SmembersDx(setKey, this._dStr)
	var nums int = len(keys)
	if nums <= 0 {
		return
	}
	var totalStep int = nums / MaxGet
	if nums%MaxGet != 0 {
		totalStep = totalStep + 1
	}

	var retStr []string
	var end int
	var begin int
	for step := 0; step < totalStep; step += 1 {
		begin = MaxGet * step
		if MaxGet*(step+1) > nums {
			end = nums
		} else {
			end = MaxGet * (step + 1)
		}
		datas := Redis.MgetDx(keys[begin:end], this._dStr)
		dataj := -1
		for i := begin; i < end; i += 1 {
			dataj += 1
			if datas[dataj] == nil || len(keys[i]) < SessLen {
				logger.InfoDx(this._dStr, "want del %v - %v", setKey, keys[i])
				Redis.SremDx(setKey, keys[i], this._dStr)
				continue
			}

			kvsObj := GetKVSSession("")
			pks := kvsObj.getParsepk(keys[i])
			//logger.Info("- getpk  %v", pks)
			kvsObj.SetPk(pks[0])
			kvsObj.unpack(datas[dataj]...)
			if distinct {
				if IsInArray(deviceToken, distinctArr) || kvsObj.DeviceToken == "" {
					//logger.Info("- inarr [%v:%v:%v] %v", os64, kvsObj.GetAttr("os"), deviceToken, distinctArr)
					continue
				}
				if bos && os != kvsObj.Os {
					//logger.Info("- con [:%v:%v]", os64, kvsObj.GetAttr("os"))
					continue
				}
				distinctArr = append(distinctArr, kvsObj.DeviceToken)
			}

			if !all {
				if online && kvsObj.Net <= NetOffline {
					continue
				}
				// if online && ((kvsObj.Temp == 1 && kvsObj.Net <= NetOffline) ||
				// 	(kvsObj.Temp == 0 && (kvsObj.Net == NetOffline || kvsObj.Net == NetConnect))) {
				// 	continue
				// }
			}
			// if getOnlyIHome {
			// 	if kvsObj.Os == OSEfaml {
			// 		rets = append(rets, kvsObj)
			// 		retStr = append(retStr, kvsObj.Sessid)
			// 	}
			// 	continue
			// }
			// if !getIHome && kvsObj.Os == OSEfaml {
			// 	continue
			// }
			rets = append(rets, kvsObj)
			retStr = append(retStr, kvsObj.Sessid)
		}
	}

	logger.InfoDx(this._dStr, "Filter params %v gets[%v]", params, retStr)
	return
}
func (this *KVSSession) Mget(sess []string, fillNil bool) (rets []*KVSSession) {
	var val interface{}
	datas, errs := Redis.MgetStruct(&val, sess, 1, this.GetKVSKey)
	dataList := datas.([]interface{})
	for i := 0; i < len(sess); i++ {
		if errs[i] == nil {
			kvsObj := GetKVSSession("")
			kvsObj.SetPk(sess[i])
			kvsObj.unpack(dataList[i].([]interface{})...)
			rets = append(rets, kvsObj)
		} else if fillNil {
			rets = append(rets, nil)
		}
	}
	return
}

func (this *KVSSession) MgetEx(keys []string) (rets []*KVSSession) {
	var nums int = len(keys)
	if nums <= 0 {
		return
	}
	var totalStep int = nums / MaxGet
	if nums%MaxGet != 0 {
		totalStep = totalStep + 1
	}

	//logger.Debug("GET keys:%v", keys)
	//var retStr []string
	var end int
	var begin int
	for step := 0; step < totalStep; step += 1 {
		begin = MaxGet * step
		if MaxGet*(step+1) > nums {
			end = nums
		} else {
			end = MaxGet * (step + 1)
		}
		datas := Redis.MgetDx(keys[begin:end], this._dStr)
		dataj := -1
		for i := begin; i < end; i += 1 {
			dataj += 1
			kvsObj := GetKVSSession("")
			pks := kvsObj.getParsepk(keys[i])
			kvsObj.SetPk(pks[0])
			kvsObj.unpack(datas[dataj]...)

			rets = append(rets, kvsObj)
			//retStr = append(retStr, kvsObj.Sessid)
		}
	}

	return
}

func (this *KVSSession) Expire(exTime int) {
	if exTime <= 0 {
		exTime = SMSCodeExTime
	}
	key := this.GetKVSKey("")
	Redis.Expire(key, exTime)
}

// KVSPhoneUser
func (this *KVSPhoneUser) Get() error {
	if this.Account == "" {
		return fmt.Errorf("Not get phoneuser account is nil")
	}

	var val interface{}
	var err error
	if err = Redis.GetDx(this.GetKVSKey(""), &val, this._dStr); err != nil {
		sql := fmt.Sprintf("select * from %s where account=? or sms_phone=? or email=? or unique_account=? limit 1;", this._table)
		datas, num, err := DB.RawValuesDx(this._dStr, sql, this.Account, this.Account, this.Account, this.Account)
		if num == 0 || err != nil {
			return fmt.Errorf("Not get phoneuser data by [%s] %v", this.Account, err)
		}

		var unpackData []interface{}
		mapVal := datas[0]
		for i := 0; i < this._colNum; i++ {
			a, ok := mapVal[this._cols[i]]
			if ok {
				unpackData = append(unpackData, a)
			} else {
				return fmt.Errorf("Field[%s] not find", this._cols[i])
			}
		}

		logger.InfoDx(this._dStr, "unpackData %+v", unpackData)
		this.SetPk(unpackData[:this._pkNum]...)
		this.unpack(unpackData[this._pkNum:]...)
		Redis.SetDx(this.GetKVSKey(""), this._oldVal[this._pkNum:], this.getExTime(), this._dStr)

		if len(this.SmsPhone) > 0 && this.Account != this.SmsPhone {
			Redis.SetDx(this.GetKVSKey(this.SmsPhone), this.Account, this.getExTime(), this._dStr)
		}
		if len(this.Email) > 0 && this.Account != this.Email {
			Redis.SetDx(this.GetKVSKey(this.Email), this.Account, this.getExTime(), this._dStr)
		}
		return nil
	}

	switch reflect.TypeOf(val).Kind() {
	case reflect.String:
		this.SetPk(val)
		return this.get(false)
	case reflect.Slice:
		this.unpack(val.([]interface{})...)
		return nil
	}
	Redis.DelDx(this.GetKVSKey(""), this._dStr)
	return fmt.Errorf("Not support value to get [%s]", this.Account)
}
func (this *KVSPhoneUser) Save(iDB bool) error {
	cols, old, _ := this.getDiffVal(this._pkNum, this._colNum, false)
	for i := 0; i < len(cols); i++ {
		switch cols[i] {
		case "sms_phone", "email":
			Redis.DelDx(this.GetKVSKey(old[i].(string)), this._dStr)
		}
	}

	if len(this.SmsPhone) > 0 && this.Account != this.SmsPhone {
		Redis.SetDx(this.GetKVSKey(this.SmsPhone), this.Account, this.getExTime(), this._dStr)
	}
	if len(this.Email) > 0 && this.Account != this.Email {
		Redis.SetDx(this.GetKVSKey(this.Email), this.Account, this.getExTime(), this._dStr)
	}
	return this.save(true, iDB)
}
func (this *KVSPhoneUser) Delete(dbDel bool) {
	if len(this.SmsPhone) > 0 && this.Account != this.SmsPhone {
		Redis.DelDx(this.GetKVSKey(this.SmsPhone), this._dStr)
	}
	if len(this.Email) > 0 && this.Account != this.Email {
		Redis.DelDx(this.GetKVSKey(this.Email), this._dStr)
	}
	this.KVSBase.Delete(dbDel)
}

//KVSPhoneUserCamera

//KVSPhoneUserScene
func (this *KVSPhoneUserScene) Save(iDB bool) error {
	if iDB && this.SceneId == 0 {
		dbPusObj := TBPhoneUserScene{SceneName: this.SceneName, Account: this.Account, SortNum: this.SortNum}
		id, err := DB.InsertDx(&dbPusObj, this._dStr)
		logger.Info("----insert pus err:%v id:%d", err, id)
		this.SceneId = int(id)
		this.SortNum = int(id)
		DB.QueryDx(GPhoneUserScene, this._dStr).Filter("scene_id", id).Update("sort_num", id)
		return err
	}

	return this.save(true, false)
}

//KVSCamera

//KVSKvBase
func (this *KVSKvBase) Exist() bool          { return Redis.ExistDx(this._key, this._dStr) }
func (this *KVSKvBase) Save(val interface{}) { Redis.SetDx(this._key, val, this._kvsExTime, this._dStr) }
func (this *KVSKvBase) Delete()              { Redis.DelDx(this._key, this._dStr) }
func (this *KVSKvBase) Key() string          { return this._key }
func (this KVSKvBase) Get(val interface{}) (err error) {
	return Redis.GetDx(this._key, &val, this._dStr)
}
func (this *KVSKvBase) SetDebugStr(dStr string) { this._dStr = dStr }
func (this *KVSKvBase) SetPk(pks ...interface{}) {
	this._key = fmt.Sprintf("%s%d", this._head, this._kvsVersion)
	for i := 0; i < len(pks); i++ {
		this._key += fmt.Sprintf("%s%v", KeySep, pks[i])
	}
}

//KVSHistoryList
func (this *KVSHistoryList) GetListByTime(cid string, curPointTime int64, timeStep int /*86400*/, days int /*7*/) (hlist [][]int64) {
	var data [][]int64
	head := this._head + strconv.Itoa(this._kvsVersion) + KeySep + cid + KeySep
	for i := 0; i < days; i++ {
		key := head + strconv.Itoa(int(curPointTime)-(days-i-1)*timeStep)
		if err := Redis.GetDx(key, &data, this._dStr); err == nil {
			hlist = append(hlist, data...)
		}
	}
	return
}

//KVSRobotData

//KVSRobotMsgConfig
func (this *KVSRobotMsgConfig) getSetKey() string {
	if this.Vid == "" {
		return ""
	}
	return fmt.Sprintf("%s%d%s%s", this.headVid, this._kvsVersion, KeySep, this.Vid)
}
func (this *KVSRobotMsgConfig) SetPk(vid, key string) {
	this.Vid = vid
	if key != "" {
		this.KVSKvBase.SetPk(vid, key)
	}
}
func (this *KVSRobotMsgConfig) Save(val interface{}) {
	Redis.SaddDx(this.getSetKey(), this._key, this._dStr)
	Redis.SetDx(this._key, val, this._kvsExTime, this._dStr)
}
func (this *KVSRobotMsgConfig) Delete() {
	Redis.SremDx(this.getSetKey(), this._key, this._dStr)
	Redis.DelDx(this._key, this._dStr)
}
func (this *KVSRobotMsgConfig) GetAll() (ret map[string]string) {
	ret = make(map[string]string)
	setKey := this.getSetKey()
	keys := Redis.SmembersDx(setKey, this._dStr)
	datas := Redis.MgetExDx(keys, this._dStr)
	for i := 0; i < len(keys); i++ {
		if datas[i] == nil {
			Redis.SremDx(setKey, keys[i], this._dStr)
			continue
		}
		l := strings.Split(keys[i], KeySep)
		ret[l[2]] = datas[i].(string)
	}
	return
}
func (this *KVSRobotMsgConfig) DeleteAll() {
	setKey := this.getSetKey()
	Redis.MdelDx(Redis.SmembersDx(setKey, this._dStr), 1, 1, nil, this._dStr)
	Redis.DelDx(setKey, this._dStr)
}

//KVSRobotVKey
func (this *KVSRobotVKey) getIndex(vkey string) int {
	for i := 0; i < len(this.VKeyList); i++ {
		if this.VKeyList[i] == vkey {
			return i
		}
	}
	return -1
}
func (this *KVSRobotVKey) DeleteVKey(vkey string) {
	if index := this.getIndex(vkey); index != -1 {
		this.VKeyList = append(this.VKeyList[:index], this.VKeyList[index+1:]...)
	}
}
func (this *KVSRobotVKey) AddVKey(vkey string) {
	if index := this.getIndex(vkey); index == -1 {
		this.VKeyList = append(this.VKeyList, vkey)
	}
}

func (this *KVSRobotVKey) Get() error {
	if err := Redis.GetDx(this._key, &this.KVSRobotVKeyValue, this._dStr); err != nil {
		var tb TBCompanyVid
		VKeySp := strings.Split(this._key, KeySep)
		vKey := VKeySp[1]
		err := DB.Query(GCompanyVid).Filter("company_vid", vKey).First(&tb)
		if err != nil {
			logger.Errorf("Get GetVkey selectErr count mysql vid:%v err:%v", vKey, err)
			return err
		}
		var BundleID, VKeyList []string

		json.Unmarshal([]byte(tb.BundleID), &BundleID)
		json.Unmarshal([]byte(tb.VKeyList), &VKeyList)

		this.KVSRobotVKeyValue.BundleID = BundleID
		this.KVSRobotVKeyValue.VKeyList = VKeyList
		this.KVSRobotVKeyValue.PubKey = tb.PubKey
		this.KVSRobotVKeyValue.Url = tb.Url
		Redis.SetDx(this._key, &this.KVSRobotVKeyValue, this._kvsExTime, this._dStr)
		logger.Info("Get GetVkey From Mysql end vid:%s, KVSRobotVKeyValue:%v", vKey, this.KVSRobotVKeyValue)
		return nil
	}
	return nil
}

func (this *KVSRobotVKey) Save() {
	VKeySp := strings.Split(this._key, KeySep)
	vKey := VKeySp[1]
	BundleID, _ := json.Marshal(this.KVSRobotVKeyValue.BundleID)
	VkeyList, _ := json.Marshal(this.KVSRobotVKeyValue.VKeyList)
	Url := this.KVSRobotVKeyValue.Url
	PubKey := this.KVSRobotVKeyValue.PubKey

	var tb TBCompanyVid
	err := DB.Query(GCompanyVid).Filter("company_vid", vKey).First(&tb)
	if err != nil {
		dbObj := TBCompanyVid{
			CompanyVid: vKey,
			Url:        Url,
			BundleID:   string(BundleID),
			VKeyList:   string(VkeyList),
			PubKey:     PubKey,
		}
		Id, err := DB.Insert(&dbObj)
		if err != nil {
			Redis.DelDx(this._key, this._dStr)
			logger.Errorf("Save SetVKey Insert Mysql err:%v  Del redis", err)
		}
		logger.Info("Save SetVKey Insert Mysql Success Id->%v", Id)
	} else {
		num, err := DB.Query(GCompanyVid).Filter("company_vid", vKey).Update("url", Url, "bundle_id", string(BundleID), "vkey_list", string(VkeyList), "pubkey", PubKey)
		if err != nil {
			logger.Errorf("Save SetVKey Update Mysql err:%v", err)
		}
		logger.Info("Save SetVKey Update Mysql Success RowAffected num->%v", num)
	}

	Redis.SetDx(this._key, &this.KVSRobotVKeyValue, this._kvsExTime, this._dStr)
}

//KVSSms
func (this *KVSSms) Get() error {
	if err := Redis.GetDx(this._key, &this.KVSSmsValue, this._dStr); err != nil {
		return err
	}
	return nil
}

func (this *KVSSms) Save() {
	exTime := this._kvsExTime
	if this.KVSSmsValue.ExTime == 0 {
		this.KVSSmsValue.ExTime = this._kvsExTime
	} else {
		exTime = this.KVSSmsValue.ExTime - int(time.Now().Unix()-this.KVSSmsValue.Time)
	}

	Redis.SetDx(this._key, &this.KVSSmsValue, exTime, this._dStr)
}

//KVSRobotCIDQuota
func (this *KVSRobotCIDQuota) Get() error {
	var val int
	if err := Redis.GetDx(this._key, &val, this._dStr); err != nil {
		return err
	}
	logger.InfoDx(this._dStr, "KVSRobotCIDQuota %v %v", this._key, val)
	this.Quota = val
	return nil
}

func GetKVSDataPoint(devId string) *KVSDataPoint {
	kvsObjNew := gkvsDataPoint
	kvsObjNew.SetPk(devId)
	return &kvsObjNew
}

func GetKVSDataPointCnt(devId string) *KVSDataPointCnt {
	kvsObjNew := gkvsDataPointCnt
	kvsObjNew.SetPk(devId)
	return &kvsObjNew
}

// func GetKVSCID(devId string) *KVSDataPoint {
// 	kvsObjNew := gkvsDataPoint
// 	kvsObjNew.SetPk(devId)
// 	return &kvsObjNew
// }

func (this *KVSDataPoint) GetObjects(msgId ...int64) ([]KVSDataPointValue, error) {
	if v, err := Redis.ByteSlices(Redis.HMGet(this._key, msgId)); err == nil {
		values := make([]KVSDataPointValue, len(msgId))
		for i, val := range v {
			Decode(&values[i], val)
		}
		return values, nil
	} else {
		return nil, err
	}
}

func (this *KVSDataPoint) Get(msgId int64) (val KVSDataPointValue, err error) {
	var data []byte
	if data, err = redis.Bytes(Redis.HGet(this._key, msgId)); err == nil {
		Decode(&val, data)
	}
	return
}

func (this *KVSDataPoint) Delete(msgId int64) {
	Redis.HDel(this._key, msgId)
}

func (this *KVSDataPoint) DeleteAll() {
	Redis.Del(this._key)
}

func (this *KVSDataPoint) GetBytes(msgId ...int64) ([][]byte, error) {
	return Redis.ByteSlices(Redis.HMGet(this._key, msgId))
}

func (this *KVSDataPoint) Save(msgId int64, Time int64, data []byte) error {
	var val KVSDataPointValue
	val.Time = Time
	val.Value = data
	_, err := Redis.HSet(this._key, msgId, Encode(&val))
	Redis.Expire(this._key, this._kvsExTime)
	return err
}

func GetKVSCounter(pks ...interface{}) *KVSCount {
	var c KVSCount
	for i := 0; i < len(pks); i++ {
		c._key += fmt.Sprintf("%v%s", pks[i], KeySep)
	}
	return &c
}

type KVSHSetCounter struct {
	Key    string
	SubKey interface{}

	_exTime int
	_dStr   string
}

func GetKVSVersionCounter(key string) *KVSHSetCounter {
	var c KVSHSetCounter
	temp := GetKVSDataPointCnt(key)
	c.Key = temp._key
	c.SubKey = key
	c._exTime = KVSForeverTime
	return &c
}

func GetKVSDPCounter(key string, subKey interface{}) *KVSHSetCounter {
	var c KVSHSetCounter
	temp := GetKVSDataPointCnt(key)
	c.Key = temp._key
	c.SubKey = subKey
	c._exTime = temp._kvsExTime
	return &c
}

func (c *KVSHSetCounter) DelAll() error {
	return Redis.Del(c.Key)
}

func (c *KVSHSetCounter) SetSubKey(subKey interface{}, amount int64) (int64, error) {
	return Redis.Int64(Redis.HSet(c.Key, subKey, amount))
}

func (c *KVSHSetCounter) MSetSubKey(args ...interface{}) (int64, error) {
	return Redis.Int64(Redis.HMSet(c.Key, args))
}

func (c *KVSHSetCounter) HGetAll() map[string]int64 {
	val, _ := Redis.HGetAll(c.Key)
	return val
}

func (c *KVSHSetCounter) HGetAllDx(dStr string) map[string]int64 {
	val, _ := Redis.HGetAllDx(c.Key, dStr)
	return val
}

func (c *KVSHSetCounter) IncrSubKey(subKey interface{}, amount int64) (int64, error) {
	return Redis.Int64(Redis.HIncr(c.Key, subKey, amount))
}

func (c *KVSHSetCounter) DecrSubKey(subKey interface{}, amount int64) (int64, error) {
	return Redis.Int64(Redis.HIncr(c.Key, subKey, -amount))
}

func (c *KVSHSetCounter) DelSubKey(subKey interface{}, amount int64) (int64, error) {
	return Redis.Int64(Redis.HDel(c.Key, subKey, amount))
}

func (c *KVSHSetCounter) GetSubKey(subKey interface{}) (int64, error) {
	return Redis.Int64(Redis.HGet(c.Key, subKey))
}

func (c *KVSHSetCounter) MGetSubKey(subKey interface{}) (int64, error) {
	var num int
	var err error
	if v, err := Redis.Ints(Redis.HMGetDx("hmget", c.Key, subKey)); err == nil {
		for _, n := range v {
			if n > 0 {
				num += n
			}
		}
	}

	return int64(num), err
}

func (c *KVSHSetCounter) SetDebugStr(dStr string) { c._dStr = dStr }

func (c *KVSHSetCounter) Get() (int64, error) {
	return Redis.Int64(Redis.HGet(c.Key, c.SubKey))
}

func (c *KVSHSetCounter) Set(amount int64) (int64, error) {
	ret, err := Redis.Int64(Redis.HSet(c.Key, c.SubKey, amount))
	Redis.Expire(c.Key, c._exTime)
	return ret, err
}

func (c *KVSHSetCounter) Expire(exTime int) {
	if exTime <= 0 {
		exTime = c._exTime
	}
	Redis.Expire(c.Key, exTime)
}

// Incr 不需要预先set incr不存在的key也可以
func (c *KVSHSetCounter) Incr(amount int64) (int64, error) {
	ret, err := Redis.Int64(Redis.HIncr(c.Key, c.SubKey, amount))
	Redis.Expire(c.Key, c._exTime)
	return ret, err
}

func (c *KVSHSetCounter) Decr(amount int64) (int64, error) {
	return c.Incr(-amount)
}

func (c *KVSCount) Save() error {
	_, err := Redis.DoCommand("", "SET", c._key, c.Count)
	return err
}

// Incr 不需要预先set incr不存在的key也可以
func (c *KVSCount) Incr(amount int64) (int64, error) {
	return Redis.Incr(c._key, amount)
}

func (c *KVSCount) Decr(amount int64) error {
	return Redis.Decr(c._key, amount)
}

/*
KVSDataCidRandom
*/
func (this *KVSDataCidRandom) Get() error {
	if err := Redis.Get(this._key, &this.CidRandomData); err != nil {
		return err
	}
	return nil
}
func (this *KVSDataCidRandom) Save() {
	Redis.Set(this._key, &this.CidRandomData, this._kvsExTime)
}

//KVSVerParts
func (this *KVSVerParts) Get() error {
	if err := Redis.GetDx(this._key, &this.KVSVerPartsValue, this._dStr); err != nil {
		return err
	}
	return nil
}

func (this *KVSVerParts) Save() {
	Redis.SetDx(this._key, &this.KVSVerPartsValue, this._kvsExTime, this._dStr)
}

//KVSSmsCount
func (this *KVSSmsCount) Get() error {
	if err := Redis.GetDx(this._key, &this.KVSSmsCountValue, this._dStr); err != nil {
		return err
	}
	return nil
}

func (this *KVSSmsCount) Save() {
	Redis.SetDx(this._key, &this.KVSSmsCountValue, this._kvsExTime, this._dStr)
}

//KVSWeChatPush
func (this *KVSWeChatPush) Get() error {
	if err := Redis.GetDx(this._key, &this.KVSWeChatPushValue, this._dStr); err != nil {
		return err
	}
	return nil
}

func (this *KVSWeChatPush) Save() {
	Redis.SetDx(this._key, &this.KVSWeChatPushValue, this._kvsExTime, this._dStr)
}

func GetKVSWeChat(CompanyVid string) *KVSWeChatPush {
	kvsObj := gkvsWeChatPush
	kvsObj.SetPk(CompanyVid)
	return &kvsObj
}

/*
Get kvs object interface
*/
func GetKVSAuthToken(sessid string) *KVSAuthToken {
	kvsObj := gkvsAuthObj
	kvsObj.initInstanceData(&kvsObj, sessid)
	return &kvsObj
}

func GetKVSSession(sessid string) *KVSSession {
	kvsObj := gkvsSObj
	kvsObj.initInstanceData(&kvsObj, sessid)
	return &kvsObj
}

func GetKVSPU(account string) *KVSPhoneUser {
	kvsObj := gkvsPUObj
	kvsObj.initInstanceData(&kvsObj, account)
	return &kvsObj
}

func GetKVSPUC(cid, account string) *KVSPhoneUserCamera {
	kvsObj := gkvsPUCObj
	kvsObj.initInstanceData(&kvsObj, cid, account)
	return &kvsObj
}

func GetKVSPUS(sid int) *KVSPhoneUserScene {
	kvsObj := gkvsPUSObj
	kvsObj.initInstanceData(&kvsObj, sid)
	return &kvsObj
}

func GetKVSCam(cid string) *KVSCamera {
	kvsObj := gkvsCamObj
	kvsObj.initInstanceData(&kvsObj, cid)
	return &kvsObj
}

// func GetKVSHlist(cid string, t_time int64) *KVSHistoryList {
// 	kvsObj := gkvsHlistObj
// 	kvsObj.SetPk(cid, t_time)
// 	return &kvsObj
// }

// func GetKVSRbt(pks ...interface{}) *KVSRobotData {
// 	kvsObj := gkvsRbtObj
// 	kvsObj.SetPk(pks...)
// 	return &kvsObj
// }

// func GetKVSRbtMsgCfg(vid, key string) *KVSRobotMsgConfig {
// 	kvsObj := gkvsRbtMsgCfg
// 	kvsObj.SetPk(vid, key)
// 	return &kvsObj
// }

func GetKVSRbtVKey(vid string) *KVSRobotVKey {
	kvsObj := gkvsRbtVKey
	kvsObj.SetPk(vid)
	return &kvsObj
}

func GetKVSSms(phone string) *KVSSms {
	kvsObj := gkvsSmsObj
	kvsObj.SetPk(phone)
	return &kvsObj
}

func GetKVSSmsEx(phone string, isEmail bool) *KVSSms {
	kvsObj := gkvsSmsObj
	kvsObj.SetPk(phone)
	if isEmail {
		kvsObj._kvsExTime = EmailCodeExTime
	}
	return &kvsObj
}

// func GetKVSRbtCIDQuota(vid string, pid uint32) *KVSRobotCIDQuota {
// 	kvsObj := gkvsRbtCIDQuota
// 	kvsObj.SetPk(vid, pid)
// 	return &kvsObj
// }

func GetKVSDataCidRandom(rand string) *KVSDataCidRandom {
	kvsObj := gkvsDataCidRandom
	kvsObj.SetPk(rand)
	return &kvsObj
}

func GetKVSDataCidRandomEx(rand string, exTime int) *KVSDataCidRandom {
	kvsObj := gkvsDataCidRandom
	kvsObj._kvsExTime = exTime
	kvsObj.SetPk(rand)
	return &kvsObj
}

// func GetKVSSessionRgn(sessid string) *KVSSessionRgn {
// 	kvsObj := gkvsSessionRgn
// 	kvsObj.SetPk(sessid)
// 	return &kvsObj
// }

// func GetKVSVerParts(cid string) *KVSVerParts {
// 	kvsObj := gkvsVerParts
// 	kvsObj.SetPk(cid)
// 	return &kvsObj
// }

func GetKVSSmsCount(phone string) *KVSSmsCount {
	kvsObj := gkvsSmsCountObj
	kvsObj.SetPk(phone)
	return &kvsObj
}

func InitKVSObj(rConf RedisConf) {
	//KVSAuthToken
	gkvsAuthObj._cols, gkvsAuthObj._oldVal, gkvsAuthObj._colNum, gkvsAuthObj._pkNum = KVSModelRegister(&gkvsAuthObj)
	gkvsAuthObj._kvsVersion = rConf.SessionV
	gkvsAuthObj._table = nil
	gkvsAuthObj._head = "auth"
	gkvsAuthObj._kvsExTime = OneDayExTime
	gkvsAuthObj._prefix = fmt.Sprintf("%s%d", gkvsAuthObj._head, gkvsAuthObj._kvsVersion)

	//KVSSession
	gkvsSObj._cols, gkvsSObj._oldVal, gkvsSObj._colNum, gkvsSObj._pkNum = KVSModelRegister(&gkvsSObj)
	gkvsSObj._kvsVersion = rConf.SessionV
	gkvsSObj._table = nil
	gkvsSObj._head = "sid"
	gkvsSObj._kvsExTime = MonthExTime
	gkvsSObj.headAcc = "accsid"
	gkvsSObj.headToken = "tokensid"
	gkvsSObj._prefix = fmt.Sprintf("%s%d", gkvsSObj._head, gkvsSObj._kvsVersion)
	//logger.Info("%+v", gkvsSObj)

	//KVSPhoneUser
	gkvsPUObj._cols, gkvsPUObj._oldVal, gkvsPUObj._colNum, gkvsPUObj._pkNum = KVSModelRegister(&gkvsPUObj)
	gkvsPUObj._kvsVersion = rConf.PhoneUserV
	gkvsPUObj._table = GPhoneUser
	gkvsPUObj._head = "pu"
	gkvsPUObj._kvsExTime = MonthExTime
	gkvsPUObj._prefix = fmt.Sprintf("%s%d", gkvsPUObj._head, gkvsPUObj._kvsVersion)

	//KVSPhoneUserCamera
	gkvsPUCObj._cols, gkvsPUCObj._oldVal, gkvsPUCObj._colNum, gkvsPUCObj._pkNum = KVSModelRegister(&gkvsPUCObj)
	gkvsPUCObj._kvsVersion = rConf.PhoneUserCameraV
	gkvsPUCObj._table = GPhoneUserCamera
	gkvsPUCObj._head = "puc"
	gkvsPUCObj._kvsExTime = MonthExTime
	gkvsPUCObj._prefix = fmt.Sprintf("%s%d", gkvsPUCObj._head, gkvsPUCObj._kvsVersion)
	//g_kvsPUCObj.headCid = "cidpuc"
	//logger.Info("%+v", gkvsPUCObj)

	//KVSPhoneUserScene
	gkvsPUSObj._cols, gkvsPUSObj._oldVal, gkvsPUSObj._colNum, gkvsPUSObj._pkNum = KVSModelRegister(&gkvsPUSObj)
	gkvsPUSObj._kvsVersion = rConf.PhoneUserSceneV
	gkvsPUSObj._table = GPhoneUserScene
	gkvsPUSObj._head = "pus"
	gkvsPUSObj._kvsExTime = MonthExTime
	gkvsPUSObj._prefix = fmt.Sprintf("%s%d", gkvsPUSObj._head, gkvsPUSObj._kvsVersion)

	//KVSCamera
	gkvsCamObj._cols, gkvsCamObj._oldVal, gkvsCamObj._colNum, gkvsCamObj._pkNum = KVSModelRegister(&gkvsCamObj)
	gkvsCamObj._kvsVersion = rConf.CameraV
	gkvsCamObj._table = GCamera
	gkvsCamObj._head = "cam"
	gkvsCamObj._kvsExTime = MonthExTime
	gkvsCamObj._prefix = fmt.Sprintf("%s%d", gkvsCamObj._head, gkvsCamObj._kvsVersion)
	//logger.Info("%+v", gkvsCamObj)

	//KVSHistoryList
	// gkvsHlistObj._kvsVersion = rConf.HistoryListV
	// gkvsHlistObj._head = "hlist"
	// gkvsHlistObj._kvsExTime = MonthExTime
	//logger.Info("%+v", gkvsHlistObj)

	//KVSRobotData
	// gkvsRbtObj._kvsVersion = rConf.RobotDataV
	// gkvsRbtObj._head = "rbt"
	// gkvsRbtObj._kvsExTime = KVSForeverTime

	//KVSRobotMsgConfig
	// gkvsRbtMsgCfg._kvsVersion = rConf.RobotMsgConfigV
	// gkvsRbtMsgCfg._head = "rbtcfg"
	// gkvsRbtMsgCfg._kvsExTime = KVSForeverTime
	// gkvsRbtMsgCfg.headVid = "vidrbtcfg"

	//KVSRobotVKey
	gkvsRbtVKey._kvsVersion = rConf.RobotVKeyV
	gkvsRbtVKey._head = "rbtvkey"
	gkvsRbtVKey._kvsExTime = KVSForeverTime

	//KVSSms
	gkvsSmsObj._kvsVersion = rConf.SmsV
	gkvsSmsObj._head = "sms"
	gkvsSmsObj._kvsExTime = SMSCodeExTime

	//KVSCIDQuoys
	// gkvsRbtCIDQuota._kvsVersion = rConf.CIDQuoysV
	// gkvsRbtCIDQuota._head = "cidquota"
	// gkvsRbtCIDQuota._kvsExTime = KVSForeverTime

	//KVSDataPoint
	gkvsDataPoint._kvsVersion = rConf.DataPointV
	gkvsDataPoint._head = "dp"
	gkvsDataPoint._kvsExTime = KVSForeverTime

	gkvsDataPointCnt._kvsVersion = rConf.DataPointCntV
	gkvsDataPointCnt._head = "dpc"
	gkvsDataPointCnt._kvsExTime = MonthExTime

	//KVSDataCidRandom
	gkvsDataCidRandom._kvsVersion = rConf.DataCidRandomV
	gkvsDataCidRandom._head = "bindrand"
	gkvsDataCidRandom._kvsExTime = RandomBindExTime

	//KVSVerParts
	// gkvsVerParts._kvsVersion = rConf.VerPartsV
	// gkvsVerParts._head = "verparts"
	// gkvsVerParts._kvsExTime = KVSForeverTime

	//KVSSessionRgn
	// gkvsSessionRgn._kvsVersion = rConf.SessionRgnV
	// gkvsSessionRgn._head = "sidrgn"
	// gkvsSessionRgn._kvsExTime = OneDayExTime

	//KVSSmsCount
	gkvsSmsCountObj._kvsVersion = rConf.SmsCountV
	gkvsSmsCountObj._head = "smscount"
	gkvsSmsCountObj._kvsExTime = SMSCodeCountExTime

	//wechat push conf
	gkvsWeChatPush._kvsVersion = rConf.WeChatPushV
	gkvsWeChatPush._head = "wechat"
	gkvsWeChatPush._kvsExTime = WeChatPushExTime

	// ButtonDefine version
	DeviceButtonDefineVersion = rConf.ButtonDefineV
}
