package worker

import (
	"mirulib"
	"time"
)

//IsHasMsg 判断worker消息是否有Msg字段
// func IsHasMsg(workMsg interface{}) bool {
// 	t := reflect.TypeOf(workMsg)
// 	isHas := false
// 	switch t.Kind() {
// 	case reflect.Ptr:
// 		_, isHas = t.Elem().FieldByName("Msg")
// 	case reflect.Struct:
// 		_, isHas = t.FieldBy<PERSON>ame("Msg")
// 	case reflect.Slice:
// 		var msgH mirulib.MsgWorkerRspHeader
// 		if mirulib.CopyInterface(&msgH, workMsg) == nil {
// 			if msgH.Msg == mirulib.MsgOK {
// 				isHas = true
// 			}
// 		}
// 	}
// 	return isHas
// }

//part 1: MQheader赋值

// NewWorkerMsg 返回消息头,需要再赋值 WorkerMsg 部分
// 原路由返回
func NewWorkerMsg(ctx *JsonWorkerContext, workMsg interface{}) *mirulib.QWorkerMsg {
	qID := mirulib.MQIDWorker
	bMsg, err := mirulib.JsonEncode(workMsg)
	if err != nil {
		return nil
	}

	return &mirulib.QWorkerMsg{
		MqHeader: mirulib.MqHeader{
			ID: qID,
		},
		Seq:             ctx.Seq,
		Sess:            ctx.Headers.Caller,
		UpdateSess:      ctx.UpdateSess,
		Net:             ctx.Net,
		WorkerMsg:       bMsg,
		DStr:            ctx.ClientMsg.DStr,
		UpdateLoginOK:   ctx.UpdateLoginOK,
		UpdateHeartBeat: ctx.UpdateHeartBeat,
	}
}

// NewWorkerMsgByMQheader 返回消息头,需要再赋值 WorkerMsg 部分
// 指定路由
func NewWorkerMsgByMQheader(seq mirulib.SEQ, sess string, updateSess bool, workMsg interface{}, dStr string) *mirulib.QWorkerMsg {
	qID := mirulib.MQIDWorker
	bMsg, err := mirulib.JsonEncode(workMsg)
	if err != nil {
		return nil
	}

	return &mirulib.QWorkerMsg{
		MqHeader: mirulib.MqHeader{
			ID: qID,
		},
		Seq:        seq,
		Sess:       sess,
		UpdateSess: updateSess,
		WorkerMsg:  bMsg,
		DStr:       dStr,
	}
}

//part 2: worker消息头赋值

// NewMsgHeader 通用的MsgHeader
func NewMsgHeader(ctx *JsonWorkerContext) Headers {
	return Headers{
		ID:     ctx.Headers.ID + "_rsp",
		Caller: ctx.Headers.Callee,
		Callee: ctx.Headers.Caller,
		ReqID:  ctx.Headers.ReqID,
		Time:   time.Now().Unix(),
	}
}

// NewMsgHeaderByMsgId 指定消息ID的MsgHeader
func NewMsgHeaderByMsgId(ctx *JsonWorkerContext, msgID string) Headers {
	return Headers{
		ID:     msgID,
		Caller: ctx.Headers.Callee,
		Callee: ctx.Headers.Caller,
		ReqID:  ctx.Headers.ReqID,
		Time:   time.Now().Unix(),
	}
}

//part 3: 具体的返回消息

// NewWorkerReply 返回通用的响应消息:
func NewWorkerReply(ctx *JsonWorkerContext) *mirulib.QWorkerMsg {
	return NewWorkerMsg(ctx, JsonRspHeader{
		Headers: NewMsgHeader(ctx),
		Ret:     CodeOK,
		Msg:     GetCodeMsg(CodeOK),
	})
}

// NewWorkerErrorReply 返回通用的错误响应消息:
func NewWorkerErrorReply(ctx *JsonWorkerContext, code int) *mirulib.QWorkerMsg {
	return NewWorkerMsg(ctx, JsonRspHeader{
		Headers: NewMsgHeader(ctx),
		Ret:     code,
		Msg:     GetCodeMsg(code),
	})
}

// NewWorkerErrorReplyByMsgid 返回错误响应消息:指定消息ID
func NewWorkerErrorReplyByMsgid(ctx *JsonWorkerContext, code int, msgID string) *mirulib.QWorkerMsg {
	return NewWorkerMsg(ctx, JsonRspHeader{
		Headers: NewMsgHeaderByMsgId(ctx, msgID),
		Ret:     code,
		Msg:     GetCodeMsg(code),
	})
}

// NewMsgWorkerRspHeader TODO
func NewMsgWorkerRspHeader(ctx *JsonWorkerContext) JsonRspHeader {
	return JsonRspHeader{
		Headers: NewMsgHeader(ctx),
		Ret:     CodeOK,
		Msg:     GetCodeMsg(CodeOK),
	}
}
