package pub

const LOGID string = "LOGID"
const ACCOUNTTYPE string = "ACCOUNTTYPE"
const ACCOUNTAGENTID string = "ACCOUNTAGENTID"
const ACCOUNTPOLICY string = "ACCOUNTPOLICY"
const H5ACCOUNT string = "H5ACCOUNT"
const LOGLENGTHLIMIT int = 1024 * 10

// 角色类型
const (
	RoleTypeSuper int = iota + 1 // 超级管理员 1
	_
	RoleTypeManager // 管理员 3
	RoleTypeGuest   // 访客 4
)

// 后台操作类型
const (
	MiruOperateLogin          int = iota + 1 // 登录 1
	MiruOperateEditGroup                     // 编辑设备组 2
	MiruOperateAddAccount                    // 添加用户 3
	MiruOperateEditAccount                   // 编辑用户 4
	MiruOperateDelAccount                    // 删除用户 5
	MiruOperateResetPassword                 // 重置用户密码 6
	MiruOperateChangePassword                // 修改密码 7
)
