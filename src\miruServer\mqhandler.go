package main

import (
	"mirulib"
	"mirulib/worker"

	"golang.org/x/net/websocket"
)

func GetConnCount() int {
	srvm.l.RLock()
	defer srvm.l.RUnlock()

	return len(srvm.WsSrvs)
}

// ws消息处理函数
func WsHandler(wsConn *websocket.Conn) {
	if !canAccept() {
		return
	}
	// 连接达到最大值，拒绝连接
	connCount := GetConnCount()
	if connCount >= conf.App.MiruServer.MaxConn {
		logger.Info("ws conn is out range")
		wsConn.Close()
		return
	}

	req := wsConn.Request()
	ip := req.Header.Get("X-Forwarded-For")
	if ip == "" {
		ip = req.Header.Get("X-Real-IP")
	}
	if ip == "" {
		ip = req.RemoteAddr
	}

	logger.Info("receive connection From: %s", ip)
	wsConn.PayloadType = websocket.BinaryFrame
	wsSrv := NewWsService(wsConn, ip)
	srvm.AddSrv(wsSrv)
	wsSrv.Run()
}

type mqHandle struct {
}

func (p mqHandle) MQHandle(queue mirulib.QueueConf, header *mirulib.MqHeader, msg interface{}) {
	// logger.Info("mq recv msg: %v", msg)
	switch header.ID {
	// for test only
	case mirulib.MQIDClient:
		logger.Errorf("Error: recv MQID_CLIENT")
	case mirulib.MQIDWorker, mirulib.MQIDWorkerNew:
		var workerMsg mirulib.QWorkerMsg
		if err := mirulib.CopyInterface(&workerMsg, msg); err != nil {
			logger.Errorf("invalid msg:%v, err:%s", msg, err)
			return
		}

		logger.Debug("fastserver recv QWorkerMsg:%+v", workerMsg)

		if workerMsg.UpdateSess {
			srvm.UpdateSession(workerMsg.Seq, workerMsg.Sess, workerMsg.Net, workerMsg.UpdateLoginOK, workerMsg.UpdateHeartBeat)
		}

		var rspHeader worker.JsonRspHeader
		if worker.JsonUnmarshal(&rspHeader, workerMsg.WorkerMsg) != nil {
			return
		}

		callee := rspHeader.Headers.Callee
		if callee == "" {
			callee = workerMsg.Sess
			rspHeader.Headers.Callee = callee
		}
		var srv *WsService
		if callee != "" && !workerMsg.UpdateSess {
			srv = srvm.GetSrvBySession(callee)
		}
		if srv == nil {
			srv = srvm.GetSrvBySeq(workerMsg.Seq)
		}
		if srv != nil {
			// 登录命令的时候，callee可能为空，更新的时候也可以发送
			if srv.Session == callee || srv.Session == "" || workerMsg.UpdateSess {
				logger.DebugDx(rspHeader.Headers.ReqID, "recv callee:%s %v", callee, workerMsg.WorkerMsg)
				if !handleRemoteMsg(srv, workerMsg.WorkerMsg, &rspHeader, workerMsg.DStr) {
					srv.Send2Client(workerMsg.WorkerMsg, &rspHeader)
				}
			} else {
				logger.InfoDx(rspHeader.Headers.ReqID, "not send msg recv UpdateSess:%v session:%s callee:%s %v", workerMsg.UpdateSess, srv.Session, callee, workerMsg.WorkerMsg)
			}
		} else {
			logger.ErrorfDx(rspHeader.Headers.ReqID, "not send msg recv %v", workerMsg.WorkerMsg)
		}
	case mirulib.MQIDPushCtl:
		var ctlMsg mirulib.QPushCtlMsg
		if err := mirulib.CopyInterface(&ctlMsg, msg); err != nil {
			logger.Errorf("invalid msg:%v, err:%s", msg, err)
			return
		}
		onCtlMsg(ctlMsg.CtlMsg)
	default:
		logger.Errorf("unknown mqid:%d", header.ID)
	}
}

func (p mqHandle) OnReceive() {
	static.updateMqRecv()
}

func (p mqHandle) OnSend() {
	static.updateMqSend()
}
